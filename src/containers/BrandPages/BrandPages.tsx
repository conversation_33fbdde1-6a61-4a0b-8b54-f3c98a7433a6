import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { BrandPagesView } from './BrandPagesView';
import { BrandPagesTable } from './components';
import { Loading } from '../../components';
import {
  getBrandPages,
  deleteBrandPage,
  enableBrandPage,
  disableBrandPage,
  changeFilter,
} from './BrandPagesActions';

import useAPIError from 'components/APIErrorNotification/useAPIError';
import { CLEAR_BRAND_PAGE_ERROR_MESSAGES } from 'containers/BrandPageForm/BrandPageFormTypes';
import { useTranslation } from 'react-i18next';

export default () => {
  const { t }: { t: any } = useTranslation();
  const { addMessage } = useAPIError();
  const dispatch = useDispatch();
  const brandPages = useSelector((state: any) => state.brandPages);
  const brandPageForm = useSelector((state: any) => state.brandPageForm);
  const deleteBrandPageError = useSelector((state: any) => state.deleteError);
  const deleteBrandPageSuccess = useSelector(
    (state: any) => state.deleteSuccess
  );
  const enableBrandPageError = useSelector((state: any) => state.enableError);
  const enableBrandPageSuccess = useSelector((state: any) => state.enableSuccess);
  const disableBrandPageError = useSelector((state: any) => state.disableError);
  const disableBrandPageSuccess = useSelector((state: any) => state.disableSuccess);

  const [filter, setFilter] = useState({
    pageSize: brandPages?.filter?.pageSize || 10,
    page: brandPages?.filter?.page || 0,
    sort: brandPages?.filter?.sort || {
      field: 'id',
      dir: 'desc',
    },
    search: '',
  });

  const onFilterChange = (data) => {
    const filterUpdate = { ...filter, ...data };
    setFilter(filterUpdate);
    dispatch(changeFilter(filterUpdate));
  };

  useEffect(() => {
    if (brandPageForm.editSuccess) {
      addMessage(t('successUpdateBrand'), 'success');
    }
    if (brandPageForm.success) {
      addMessage(t('successCreatedBrand'), 'success');
    }
    if (brandPageForm.error) {
      addMessage(t(brandPageForm.error.errorCode || 'errorOccured'), 'error');
    }
    if (deleteBrandPageError) {
      addMessage(t(deleteBrandPageError.errorCode || 'errorOccured'), 'error');
    }
    if (deleteBrandPageSuccess) {
      addMessage(t('successDeleteBrand'), 'success');
    }

    if (enableBrandPageError) {
      addMessage(t(enableBrandPageError.errorCode || 'errorOccured'), 'error');
    }
    if (enableBrandPageSuccess) {
      addMessage(t('successEnableBrand'), 'success');
    }

    if (disableBrandPageError) {
      addMessage(t(disableBrandPageError.errorCode || 'errorOccured'), 'error');
    }
    if (disableBrandPageSuccess) {
      addMessage(t('successDisableBrand'), 'success');
    }

    dispatch({
      type: CLEAR_BRAND_PAGE_ERROR_MESSAGES,
    });
    dispatch(getBrandPages(filter));
  }, [deleteBrandPageSuccess, deleteBrandPageError, filter]);

  const handleDelete = (selectedBrand) => {
    dispatch(deleteBrandPage(selectedBrand.id));
  };

  const handleEnableDisable = (selectedBrand) => {
    if(selectedBrand.status !== "DISABLED") {
      dispatch(disableBrandPage(selectedBrand.id));
    } else {
      dispatch(enableBrandPage(selectedBrand.id));
    }
  }

  return (
    <BrandPagesView onFilterChange={onFilterChange} filter={filter}>
      {!brandPages.loading && brandPages.data ? (
        <BrandPagesTable
          brandPages={brandPages.data}
          pagination={brandPages.pagination}
          filter={filter}
          onFilterChange={onFilterChange}
          handleDelete={handleDelete}
          handleEnableDisable={handleEnableDisable}
        >
          {brandPages.loading && <Loading />}
        </BrandPagesTable>
      ) : (
        <Loading />
      )}
    </BrandPagesView>
  );
};
