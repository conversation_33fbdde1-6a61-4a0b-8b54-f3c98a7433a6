import React from 'react';
import { AdminForm } from './Forms';
import { PageTitle, Loading } from 'components';
import { useTranslation } from 'react-i18next';

import './UserForm.scss';

export const UserFormView = ({ data, loading }) => {
  const { t }: { t: any } = useTranslation();

  return (
    <div className="user-create-page">
      <PageTitle
        title={data?.activeUser?.id ? t('editUser') : t('createUser')}
      />
      {loading && <Loading />}
      <AdminForm user={data.activeUser} />
    </div>
  );
};
