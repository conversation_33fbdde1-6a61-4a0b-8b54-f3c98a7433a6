import * as actionTypes from './GlobalSettingActionTypes';

const INITIAL_STATE = {
  data: {},
  loading: false,
  error: false,
};

export default (state: any = INITIAL_STATE, action: any) => {
  switch (action.type) {
    case actionTypes.GET_GLOBAL_SETTING:
      return { ...state, loading: true, data: {} };
    case actionTypes.GET_GLOBAL_SETTING_SUCCESS:
      return { ...state, loading: false, data: action.payload };
    case actionTypes.GET_GLOBAL_SETTING_ERROR:
      return { ...state, error: action.payload };
    case actionTypes.SAVE_GLOBAL_SETTING:
      return { ...state, loading: true };
    case actionTypes.SAVE_GLOBAL_SETTING_SUCCESS:
      return {
        ...state,
        loading: false,
        data: action.payload,
        success: true,
      };
    case actionTypes.SAVE_GLOBAL_SETTING_ERROR:
      return { ...state, loading: false, error: action.payload };
    case actionTypes.CLEAR_STATE:
      return INITIAL_STATE;
    default:
      return state;
  }
};
