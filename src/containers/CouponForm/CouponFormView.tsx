import React, { useEffect, useState, useCallback } from 'react';
import { useParams } from 'react-router';
import { Redirect } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

import { getBrandPackageForBrandAdmin } from '../BrandAccount/BrandAccountActions';
import { AdminForm } from './Forms';
import { PageTitle, Loading, InfoDialog } from 'components';
import * as couponActions from './CouponFormActions';
import * as RoleManager from 'services/role';

import { useInfoText } from 'utils/useInfoText';

import './CouponForm.scss';

const isIdValid = (id) => id !== 'create';

export const CouponFormView = ({ loading }) => {
  const { id } = useParams();
  const { t }: { t: any } = useTranslation();
  const dispatch = useDispatch();

  const couponError = useSelector((state) => state.couponsForm.error);
  const activeCoupon = useSelector(
    (state: any) => state.couponsForm.activeCoupon
  );
  const brandAccount = useSelector((state: any) => state.brandAccount);
  const { brandPackage } = brandAccount;

  const [infoHtmlText, setInfoHtmlText] = useState<string | undefined>();
  const secondaryTitle =
    !isIdValid(id) && brandPackage
      ? `(${brandPackage.usedCoupons}/${brandPackage.coupons})`
      : '';

  useEffect(() => {
    if (isIdValid(id)) {
      couponActions.getCoupons(id);
    } else if (RoleManager.isAbleTo('brand_account', 'view')) {
      dispatch(getBrandPackageForBrandAdmin());
    }
  }, [id]);

  const couponInfoTexts = useInfoText(
    [
      'couponBrandIdInfo',
      'couponCategoryIdInfo',
      'couponReleaseDateInfo',
      'couponAmountConditionInfo',
      'couponValidationDateInfo',
      'couponIsVisibleOnBrandPageInfo',
      'couponRegulationsInfo',
      'couponDiscountTypeAmountInfo',
      'couponDiscountTypePercentageInfo',
      'couponDiscountTypeFreeInfo',
      'couponDiscountCodeTypeGenericInfo',
      'couponDiscountCodeTypeUniqueInfo',
      'couponDiscountCodeTypeStationaryInfo',
      'couponAvailableForNewCustomersInfo',
      'couponImageInfo',
      'couponWebsiteLinkInfo',
      'enableVirtualExpirationInfo',
      'couponIsCountDownEnabledInfo',
      'endOfTheMonthInfo',
      'everyTwoWeeksInfo',
      'everyDaysInfo'
    ],
    setInfoHtmlText
  );

  const onInfoClose = useCallback(
    () => setInfoHtmlText(undefined),
    [setInfoHtmlText]
  );

  return (
    <div className="coupon-create-page">
      <PageTitle
        title={isIdValid(id) ? t('editCoupon') : t('createCoupon')}
        secondaryTitle={secondaryTitle}
      />
      {loading && <Loading />}

      {couponError && couponError.errorCode === 'couponNotFound' ? (
        <Redirect to="/404" />
      ) : (
        (activeCoupon || !isIdValid(id)) && (
          <AdminForm
            coupon={activeCoupon}
            couponInfoTexts={couponInfoTexts}
            brandPackage={brandPackage}
          />
        )
      )}

      <InfoDialog
        open={!!infoHtmlText}
        textHtml={infoHtmlText}
        onCloseClick={onInfoClose}
      />
    </div>
  );
};
