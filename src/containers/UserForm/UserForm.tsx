import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Snackbar } from '@material-ui/core';
import { Redirect } from 'react-router-dom';

import { UserFormView } from './UserFormView';
import { getUser, clearSuccess } from './UserFormActions';
import { CLEAR_USER_ERROR_MESSAGES } from './UserFormTypes';
import { useTranslation } from 'react-i18next';

export default (props) => {
  const dispatch = useDispatch();
  const userForm = useSelector((state: any) => state.userForm);
  const { t }: { t: any } = useTranslation();

  useEffect(() => {
    dispatch({ type: CLEAR_USER_ERROR_MESSAGES });
    if (props.match.params?.id !== 'create') {
      dispatch(getUser(props.match.params.id));
    }

    return () => {};
  }, [dispatch]);

  const resetSuccess = () => {
    dispatch(clearSuccess());
  };

  if (userForm.error && userForm.error.errorCode === 'userNotFound') {
    return <Redirect to="/dashboard/users/404" />;
  } else {
    return (
      <>
        <UserFormView data={userForm} loading={userForm?.loading} />

        <Snackbar
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          open={userForm?.success}
          onClose={resetSuccess}
          autoHideDuration={6000}
          message={
            props.match.params.id ? (
              <span id="message-id">{t('formUpdateSuccess')}</span>
            ) : (
              <span id="message-id">
                {t('formCreateSuccess')}{' '}
                <Link to={'/dashboard/users/' + userForm.id}>
                  {t('formEditLink')}
                </Link>
              </span>
            )
          }
        />
      </>
    );
  }
};
