import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { ConsumerView } from './ConsumerView';
import {
  getConsumers,
  deleteConsumer,
  exportConsumers,
  changeFilter,
} from './ConsumerActions';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { Loading } from 'components';
import { ConsumerTable } from './components/ConsumerTable';
import { CLEAR_CONSUMER_ERROR_MESSAGES } from 'containers/ConsumerForm/ConsumerFormTypes';
import { CLEAR_CONSUMER } from 'containers/Consumer/ConsumerTypes';
import { useTranslation } from 'react-i18next';

export default () => {
  const { t }: { t: any } = useTranslation();
  const dispatch = useDispatch();
  const consumers = useSelector((state: any) => state.consumer);
  const consumersError = useSelector((state: any) => state.consumer.error);
  const consumerDeleteSuccess = useSelector(
    (state: any) => state.consumer.deleteSuccess
  );

  const [filter, setFilter] = useState({
    pageSize: consumers?.filter?.pageSize || 10,
    page: consumers?.filter?.page || 0,
    sort: consumers?.filter?.sort || {
      field: 'email',
      dir: 'DESC',
    },
    search: '',
  });

  const onFilterChange = (data) => {
    const filterUpdate = { ...filter, ...data };
    setFilter(filterUpdate);
    dispatch(getConsumers(filterUpdate));
    dispatch(changeFilter(filterUpdate));
  };
  const { addMessage } = useAPIError();

  const exportAllConsumers = () => {
    dispatch(exportConsumers('allConsumers'));
  };

  useEffect(() => {
    if (consumersError) {
      addMessage(t(consumersError.errorCode || 'errorOccured'), 'error');
    }
    if (consumerDeleteSuccess) {
      addMessage(t('successDeleteConsumer'), 'warning');
      dispatch(getConsumers(filter));
    }

    dispatch({
      type: CLEAR_CONSUMER_ERROR_MESSAGES,
    });
    dispatch({
      type: CLEAR_CONSUMER,
    });
    dispatch(getConsumers(filter));
  }, [dispatch, consumerDeleteSuccess, consumersError]);

  const handleDelete = (selectedConsumer) => {
    dispatch(deleteConsumer(selectedConsumer));
  };

  return (
    <ConsumerView
      onFilterChange={onFilterChange}
      filter={filter}
      exportAllConsumers={exportAllConsumers}
    >
      {consumers.data ? (
        <ConsumerTable
          consumers={consumers.data}
          pagination={consumers.pagination}
          filter={filter}
          handleDelete={handleDelete}
          onFilterChange={onFilterChange}
        />
      ) : (
        <Loading />
      )}
    </ConsumerView>
  );
};
