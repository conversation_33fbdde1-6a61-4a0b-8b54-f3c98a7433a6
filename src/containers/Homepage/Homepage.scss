.homepage-container {
  padding: 15px;

  h1 {
    margin-bottom: 15px;
    font-size: 20px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .save-button {
    background-color: #2196f3;
    color: white;
    border-radius: 20px;
    padding: 2px 10px;
    font-size: 11px;
    text-transform: uppercase;
    min-width: 50px;
    height: 24px;

    &:hover {
      background-color: #1976d2;
    }
  }

  .upload-box {
    margin-right: 10px;
  }

  .delete-icon {
    color: #f44336;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
  }

  .tag {
    display: flex;
    align-items: center;
    background-color: #f1f1f1;
    border-radius: 15px;
    padding: 2px 8px;
    font-size: 11px;

    .tag-delete {
      margin-left: 5px;
      cursor: pointer;
      font-size: 1rem;
    }
  }

  .search-input {
    margin-bottom: 15px;
  }

  .add-button {
    margin-top: 10px;
    background-color: #000;
    color: white;
    border-radius: 20px;
    padding: 2px 10px;
    font-size: 11px;
    text-transform: uppercase;
    margin: 10px auto;
    display: block;

    &:hover {
      background-color: #333;
    }
  }

  .item-row {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    padding: 6px 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
    font-size: 12px;

    .item-actions {
      margin-left: auto;
      display: flex;
      gap: 5px;
    }
  }

  .editor-container {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .button-options {
    margin-top: 10px;
    display: flex;
    gap: 10px;
  }
}

// Override Material-UI styles
.MuiButton-root {
  text-transform: none;
  font-weight: 500;
  font-size: 11px;
}

.MuiFormControlLabel-root {
  margin-right: 0;
}

.MuiSwitch-root {
  width: 40px;
  height: 24px;
  padding: 0;
  margin: 0 8px;
}

.MuiSwitch-switchBase {
  padding: 2px;
}

.MuiSwitch-thumb {
  width: 18px;
  height: 18px;
}

.MuiChip-root {
  height: 22px;
  font-size: 11px;
}

.MuiChip-deleteIcon {
  width: 16px;
  height: 16px;
}

.MuiIconButton-root {
  padding: 6px;
}

.MuiInputBase-root {
  font-size: 11px;
}

.MuiFormLabel-root {
  font-size: 10px;
  margin-bottom: 2px;
}

// Autocomplete styling
.MuiAutocomplete-root {
  .MuiInputBase-root {
    border-radius: 20px;
    padding: 2px 8px;
  }

  .MuiAutocomplete-endAdornment {
    right: 8px;
  }

  .MuiAutocomplete-option {
    font-size: 12px;
    min-height: 30px;
    padding: 4px 12px;
  }
}