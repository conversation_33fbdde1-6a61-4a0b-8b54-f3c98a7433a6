import API from 'services/api';

export const getConsumer = async (id) => {
  // expected error messages from BE
  // - consumerNotFound
  return await API.get(`/consumers/${id}`);
};

export const getConsumers = async (props) => {
  const {
    pageSize = 10,
    page = 0,
    search = '',
    sort = [{ field: 'email', dir: 'ASC' }],
  } = props;
  return await API.post('/consumers/list', {
    page,
    pageSize,
    sort: sort.field
      ? [{ field: sort.field, dir: sort.dir.toUpperCase() }]
      : [],
    search,
  });
};

export const cancelConsumerSubscription = async (id) => {
  // expected error messages from BE
  // - consumerNotFound
  // - activeSubscriptionForConsumerNotFound
  // - productPackageNotFound
  // - recurringPaymentForConsumerNotFound
  return await API.post(`consumers/cancel-subscription/${id}`);
};

export const deleteConsumer = async (id) => {
  // expected error messages from BE
  // - consumerNotFound
  return await API.delete(`/consumers/${id}`);
};

export const exportAllConsumers = async () => {
  // expected error messages from BE
  // - primaryEmailForConsumerNotFound
  // - subscriptionForConsumerNotFound
  return await API.get('/consumers/export', {
    headers: {
      accept: 'text/csv; charset=utf-8',
    },
  });
};

export const exportConsumersPerCategory = async (id) => {
  if (id === -1) {
    return await API.get('/reports/activity-per-category', {
      headers: {
        accept: 'text/csv; charset=utf-8',
      },
    });
  } else {
    return await API.get(`/reports/activity-per-category?categoryId=${id}`, {
      headers: {
        accept: 'text/csv; charset=utf-8',
      },
    });
  }
};

export const exportConsumersPerBrand = async (id) => {
  if (id === -1) {
    return await API.get('/reports/activity-per-brand', {
      headers: {
        accept: 'text/csv; charset=utf-8',
      },
    });
  } else {
    return await API.get(`/reports/activity-per-brand?brandId=${id}`, {
      headers: {
        accept: 'text/csv; charset=utf-8',
      },
    });
  }
};

export const exportActivityOfAllUsers = async () => {
  return await API.get('/reports/activity-per-consumer', {
    headers: {
      accept: 'text/csv; charset=utf-8',
    },
  });
};

export const exportNonRegisteredAndSubcribedToNewsletter = async () => {
  return await API.get('/newsletter/export', {
    headers: {
      accept: 'text/csv; charset=utf-8',
    },
  });
};

export const editConsumer = async (id, data) => {
  // expected error messages from BE
  // - consumerNotFound
  return await API.patch(`/consumers/${id}`, {
    ...data,
  });
};

export const changeSubscription = async (consumerId, productPackageId) => {
  return await API.post(
    `/consumers/change-subscription?consumerId=${consumerId}&productPackageId=${productPackageId}`
  );
};
