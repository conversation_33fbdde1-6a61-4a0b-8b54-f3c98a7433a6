{"name": "admin", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "1.x", "@hookform/error-message": "^0.0.4", "@material-ui/core": "^4.10.2", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.60", "@material-ui/pickers": "^3.2.10", "@material-ui/styles": "^4.11.4", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/jest": "^24.0.0", "@types/node": "^12.0.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "axios": "^0.21.1", "bootstrap": "^4.5.0", "clsx": "^1.1.1", "crypto-js": "^4.2.0", "date-fns": "^2.16.1", "i18next": "^20.4.0", "i18next-browser-languagedetector": "^6.1.2", "i18next-http-backend": "^1.3.0", "jsonwebtoken": "^8.5.1", "react": "^16.13.1", "react-color": "^2.19.3", "react-dom": "^16.13.1", "react-dropzone": "^11.0.1", "react-hook-form": "^6.3.2", "react-i18next": "^11.11.4", "react-number-format": "^4.7.3", "react-player": "^2.9.0", "react-quill": "^1.3.5", "react-redux": "^7.2.0", "react-router-dom": "^5.2.0", "react-scripts": "3.4.1", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "sass": "^1.63.6", "typescript": "~3.7.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}