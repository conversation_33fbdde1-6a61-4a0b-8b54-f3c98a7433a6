import {
  C<PERSON><PERSON>_CATEGORY,
  C<PERSON>AR_CATEGORY_SUCCESS,
  CREATE_CATEGORY,
  CREATE_CATEGORY_ERROR,
  CREATE_CATEGORY_SUCCESS,
  EDIT_CATEGORY,
  EDIT_CATEGORY_ERROR,
  EDIT_CATEGORY_SUCCESS,
  GET_CATEGORY,
  GET_CATEGORY_ERROR,
  GET_CATEGORY_SUCCESS,
} from './ReportFormTypes';

const INITIAL_STATE = {
  error: null,
  loading: false,
  isDone: true,
  userError: null,
};

export default (state = INITIAL_STATE, action) => {
  switch (action.type) {
    case CREATE_CATEGORY:
      return { ...state, loading: true };

    case CREATE_CATEGORY_SUCCESS:
      return { ...state, loading: false, success: true };

    case CREATE_CATEGORY_ERROR:
      return { ...state, loading: false, error: action.payload };

    case EDIT_CATEGORY:
      return { ...state, loading: true };

    case EDIT_CATEGORY_SUCCESS:
      return { ...state, loading: false, success: true };

    case EDIT_CATEGORY_ERROR:
      return { ...state, loading: false, error: action.payload };

    case GET_CATEGORY:
      return { ...state, loading: true };

    case GET_CATEGORY_SUCCESS:
      return { ...state, loading: false };

    case GET_CATEGORY_ERROR:
      return { ...state, loading: false, error: action.payload };

    case CLEAR_CATEGORY:
      return {
        ...INITIAL_STATE,
      };

    case CLEAR_CATEGORY_SUCCESS:
      return {
        ...state,
        success: null,
      };

    default:
      return state;
  }
};
