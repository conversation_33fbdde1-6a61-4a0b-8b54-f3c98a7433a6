import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { CategoriesView } from './CategoriesView';
import {
  deleteCategory,
  getCategories,
  changeFilter,
} from './CategoriesActions';
import { CategoriesTable } from './components/CategoriesTable';
import { Loading } from '../../components';
import useAPIError from '../../components/APIErrorNotification/useAPIError';
import { CLEAR_CATEGORY_ERROR_MESSAGES } from 'containers/CategoryForm/CategoryFormTypes';
import { CLEAR_CATEGORY } from 'containers/Categories/CategoriesTypes';

import { useTranslation } from 'react-i18next';

export default () => {
  const { t }: { t: any } = useTranslation();
  const { addMessage } = useAPIError();
  const dispatch = useDispatch();
  const categories = useSelector((state: any) => state.categories);
  const categoriesError = useSelector((state: any) => state.categories.error);
  const categoryDeleteSuccess = useSelector(
    (state: any) => state.categories.deleteSuccess
  );

  const categoryForm = useSelector((state: any) => state.categoryForm);

  const [filter, setFilter] = useState({
    pageSize: categories?.filter?.pageSize || 10,
    page: categories?.filter?.page || 0,
    sort: categories?.filter?.sort || {
      field: 'id',
      dir: 'DESC',
    },
    search: '',
  });

  const onFilterChange = (data) => {
    const filterUpdate = { ...filter, ...data };
    setFilter(filterUpdate);
    dispatch(getCategories(filterUpdate));
    dispatch(changeFilter(filterUpdate));
  };

  useEffect(() => {
    if (categoryForm.editSuccess) {
      addMessage(t('successCategoryUpdated'), 'success');
    }
    if (categoryForm.success) {
      addMessage(t('successCategoryCreated'), 'success');
    }
    if (categoryForm.error) {
      addMessage(t(categoryForm.error?.errorCode || 'errorOccured'), 'error');
    }
    if (categoriesError) {
      addMessage(t(categoriesError.errorCode || 'errorOccured'), 'error');
    }
    if (categoryDeleteSuccess) {
      addMessage(t('successCategoryDeleted'), 'warning');
    }
    dispatch({
      type: CLEAR_CATEGORY_ERROR_MESSAGES,
    });
    dispatch({
      type: CLEAR_CATEGORY,
    });
    dispatch(getCategories(filter));
  }, [dispatch, categoriesError, categoryDeleteSuccess]);

  const handleDelete = (selectedCategory) => {
    dispatch(deleteCategory(selectedCategory));
  };

  return (
    <CategoriesView onFilterChange={onFilterChange} filter={filter}>
      {categories.data && (
        <CategoriesTable
          categories={categories.data}
          pagination={categories.pagination}
          handleDelete={handleDelete}
          filter={filter}
          onFilterChange={onFilterChange}
        >
          {categories.loading && <Loading />}
        </CategoriesTable>
      )}
    </CategoriesView>
  );
};
