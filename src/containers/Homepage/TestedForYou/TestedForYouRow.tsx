import React, { useEffect, useState } from 'react';
import { FormLabel, IconButton, TextField, Button } from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/Delete';
import { RichTextInput } from 'components/FormInputs';
import { useForm, Controller } from 'react-hook-form';

interface TestedForYouItem {
  id?: string | number;
  brandId?: string | number; // Made optional
  textPreview?: string;
  logo?: string;
  image?: string;
  imageLink?: string;
  content?: string;
  buttonText?: string;
  buttonLink?: string;
  heading?: string;
}

interface TestedForYouRowProps {
  item: TestedForYouItem;
  onDelete: () => void;
  onChange: (data: Partial<TestedForYouItem>) => void;
  onImageChange?: (type: 'logo' | 'image', file: File) => void;
}

const labelStyle = { fontSize: 12, marginBottom: 2 };
const inputStyle = { borderRadius: '15px', fontSize: 11, height: 28 };
const thirdColumnStyle = { width: '31%', marginRight: '2%' };

const TestedForYouRow: React.FC<TestedForYouRowProps> = ({
  item,
  onDelete,
  onChange,
  onImageChange
}) => {
  // Track filenames to display
  const [logoFileName, setLogoFileName] = useState<string>('');
  const [imageFileName, setImageFileName] = useState<string>('');

  // When item changes (e.g., initial load or after save), update filename display
  useEffect(() => {
    // If there's a URL, extract the filename for display
    if (item.logo) {
      // Display actual filename from URL
      const fileName = getFileNameFromUrl(item.logo);
      setLogoFileName(fileName);
    } else {
      setLogoFileName('');
    }

    if (item.image) {
      // Display actual filename from URL
      const fileName = getFileNameFromUrl(item.image);
      setImageFileName(fileName);
    } else {
      setImageFileName('');
    }
  }, [item.logo, item.image]);

  // Helper to extract filename from URL
  const getFileNameFromUrl = (url: string): string => {
    if (!url) return '';

    // Handle base64 URLs specially (they don't have a real filename)
    if (url.startsWith('data:')) {
      return 'Current image';
    }

    // For regular URLs, get the last part after the last slash
    return url.split('/').pop() || '';
  };

  const handleLogoChange = (file: File) => {
    setLogoFileName(file.name);
    // Notify parent component about the file selection
    onImageChange?.('logo', file);
  };

  const handleImageChange = (file: File) => {
    setImageFileName(file.name);
    // Notify parent component about the file selection
    onImageChange?.('image', file);
  };

  const truncateFileName = (text: string, maxLength: number): string => {
    if (!text) return '';

    // Already short enough.
    if (text.length <= maxLength) return text;

    const dotIdx = text.lastIndexOf('.');
    const extension = text.slice(dotIdx + 1);
    const extWithDots = `...${extension}`;

    // Characters left for the base (may be 0 or negative if ext is huge)
    const baseLen = Math.max(0, maxLength - extWithDots.length);
    const baseName = text.slice(0, baseLen);

    return `${baseName}${extWithDots}`;
  };
  const { control, watch, setValue } = useForm({
    defaultValues: {
      textPreview: item.textPreview || '',
      logo: item.logo || '',
      image: item.image || '',
      imageLink: item.imageLink || '',
      content: item.content || item.textPreview || '', // Use textPreview as fallback for backward compatibility
      buttonText: item.buttonText || '',
      buttonLink: item.buttonLink || '',
      heading: item.heading || '',
    },
  });

  const values = watch();

  // Update parent component when values change
  useEffect(() => {
    onChange && onChange({ ...item, ...values });
    // eslint-disable-next-line
  }, [
    values.textPreview,
    values.logo,
    values.image,
    values.imageLink,
    values.content,
    values.buttonText,
    values.buttonLink,
    values.heading
  ]);

  // Set values when item changes
  useEffect(() => {
    if (item.textPreview && setValue) {
      setValue('textPreview', item.textPreview);
    } else if ((item as any).text && setValue) {
      // For backward compatibility
      setValue('textPreview', (item as any).text);
    }

    if (item.logo && setValue) {
      setValue('logo', item.logo);
    }

    if (item.image && setValue) {
      setValue('image', item.image);
    }

    if (item.imageLink && setValue) {
      setValue('imageLink', item.imageLink);
    }

    if (item.content && setValue) {
      setValue('content', item.content);
    } else if (item.textPreview && setValue) {
      // Fallback to textPreview for backward compatibility
      setValue('content', item.textPreview);
    } else if ((item as any).text && setValue) {
      // Fallback to text for backward compatibility
      setValue('content', (item as any).text);
    }

    if (item.buttonText && setValue) {
      setValue('buttonText', item.buttonText);
    }

    if (item.buttonLink && setValue) {
      setValue('buttonLink', item.buttonLink);
    }

    if (item.heading && setValue) {
      setValue('heading', item.heading);
    }
  }, [
    item,
    setValue
  ]);

  // Add effect to fix the editor focus issue
  useEffect(() => {
    // Add a small delay to ensure the component is fully rendered
    const timer = setTimeout(() => {
      const editors = document.querySelectorAll('.ql-editor');
      editors.forEach(editor => {
        editor.setAttribute('contenteditable', 'true');
      });
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="tested-for-you-row" style={{
      backgroundColor: '#f8f9fa',
      border: '1px solid #707070',
      borderRadius: '15px',
      padding: '20px',
      marginBottom: '10px',
      display: 'flex',
      justifyContent: 'center'
    }}>
      <div className="tested-for-you-content" style={{ display: 'flex', flexWrap: 'wrap', width: '100%' }}>
        {/* First row - heading, logo, image */}
        <div style={thirdColumnStyle}>
          <FormLabel style={labelStyle}>heading</FormLabel>
          <Controller
            name="heading"
            control={control}
            render={({ onChange, value }) => (
              <TextField
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                fullWidth
                variant="outlined"
                size="small"
                InputProps={{ style: inputStyle }}
                inputProps={{ style: { fontSize: 11 } }}
              />
            )}
          />
        </div>

        <div style={thirdColumnStyle}>
          <FormLabel style={labelStyle}>logo</FormLabel>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <Button variant="contained" component="label" className="upload-button" style={{ fontSize: 10, height: 28, width: '50%' }}>
              UPLOAD
              <input
                type="file"
                hidden
                accept=".jpg,.jpeg,.png"
                onChange={(e) => {
                  if (e.target.files?.[0]) {
                    handleLogoChange(e.target.files[0]);
                  }
                }}
              />
            </Button>
            {logoFileName && (
              <div style={{ display: 'flex', alignItems: 'center', fontSize: 11 }}>
                <span style={{ color: '#000', textDecoration: 'underline' }}>
                  {truncateFileName(logoFileName, 12)}
                </span>
              </div>
            )}
          </div>
        </div>

        <div style={thirdColumnStyle}>
          <FormLabel style={labelStyle}>image</FormLabel>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <Button variant="contained" component="label" className="upload-button" style={{ fontSize: 10, height: 28, width: '50%' }}>
              UPLOAD
              <input
                type="file"
                hidden
                accept=".jpg,.jpeg,.png"
                onChange={(e) => {
                  if (e.target.files?.[0]) {
                    handleImageChange(e.target.files[0]);
                  }
                }}
              />
            </Button>
            {imageFileName && (
              <div style={{ display: 'flex', alignItems: 'center', fontSize: 11 }}>
                <span style={{ color: '#000', textDecoration: 'underline' }}>
                  {truncateFileName(imageFileName, 12)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Add spacing between rows */}
        <div style={{ width: '100%', height: '15px' }}></div>

        {/* Second row - image link, button text, button link */}
        <div style={thirdColumnStyle}>
          <FormLabel style={labelStyle}>image link</FormLabel>
          <Controller
            name="imageLink"
            control={control}
            render={({ onChange, value }) => (
              <TextField
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                fullWidth
                variant="outlined"
                size="small"
                InputProps={{ style: inputStyle }}
                inputProps={{ style: { fontSize: 11 } }}
              />
            )}
          />
        </div>

        <div style={thirdColumnStyle}>
          <FormLabel style={labelStyle}>button text</FormLabel>
          <Controller
            name="buttonText"
            control={control}
            render={({ onChange, value }) => (
              <TextField
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                fullWidth
                variant="outlined"
                size="small"
                InputProps={{ style: inputStyle }}
                inputProps={{ style: { fontSize: 11 } }}
              />
            )}
          />
        </div>

        <div style={thirdColumnStyle}>
          <FormLabel style={labelStyle}>button link</FormLabel>
          <Controller
            name="buttonLink"
            control={control}
            render={({ onChange, value }) => (
              <TextField
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                fullWidth
                variant="outlined"
                size="small"
                InputProps={{ style: inputStyle }}
                inputProps={{ style: { fontSize: 11 } }}
              />
            )}
          />
        </div>



        {/* Fourth row - Content (Rich Text Editor) */}
        <div style={{ width: '100%', marginTop: '10px' }}>
          <FormLabel style={labelStyle}>content</FormLabel>
          <div className="editor-container" style={{ marginTop: '5px' }}>
            <RichTextInput
              name="content"
              control={control}
              validate={() => true}
              onChangeHandler={(onChange: any) => (content: any) => {
                onChange(content);
              }}
              theme="snow"
              style={{}}
            />
          </div>
        </div>

        {/* For backward compatibility - hidden field */}
        <div style={{ display: 'none' }}>
          <Controller
            name="textPreview"
            control={control}
            render={({ onChange, value }) => (
              <input
                type="hidden"
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
              />
            )}
          />
        </div>
      </div>

      {onDelete && (
        <div className="tested-for-you-actions" style={{ marginLeft: 'auto', paddingLeft: '10px' }}>
          <IconButton
            onClick={onDelete}
            style={{ color: '#757575' }}
          >
            <DeleteIcon />
          </IconButton>
        </div>
      )}
    </div>
  );
};

export default TestedForYouRow;
