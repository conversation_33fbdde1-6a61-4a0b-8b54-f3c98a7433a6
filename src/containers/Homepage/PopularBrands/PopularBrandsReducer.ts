import * as PopularBrandsTypes from './PopularBrandsTypes';
import { PopularBrands } from 'repository/popularEntities';

interface State {
    loading: boolean;
    error: any;
    popularBrands: PopularBrands | null;
}

const initialState: State = {
    loading: false,
    error: null,
    popularBrands: null
};

export default function reducer(state = initialState, action) {
    switch (action.type) {
        case PopularBrandsTypes.GET_POPULAR_BRANDS:
        case PopularBrandsTypes.UPDATE_POPULAR_BRANDS:
            return {
                ...state,
                loading: true,
                error: null
            };
        case PopularBrandsTypes.GET_POPULAR_BRANDS_SUCCESS:
            return {
                ...state,
                loading: false,
                popularBrands: action.payload
            };
        case PopularBrandsTypes.UPDATE_POPULAR_BRANDS_SUCCESS:
            return {
                ...state,
                loading: false,
                popularBrands: action.payload
            };
        case PopularBrandsTypes.GET_POPULAR_BRANDS_ERROR:
        case PopularBrandsTypes.UPDATE_POPULAR_BRANDS_ERROR:
            return {
                ...state,
                loading: false,
                error: action.payload
            };
        default:
            return state;
    }
}
