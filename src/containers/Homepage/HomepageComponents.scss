// Common styles for all homepage components
.homepage-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
  margin-bottom: 10px;
  position: relative;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      flex-grow: 1;
    }

    .save-button {
      background-color: #2196f3;
      color: white;
      border-radius: 20px;
      padding: 3px 15px;
      font-size: 12px;
      text-transform: uppercase;
      min-width: 60px;
      height: 26px;

      &:hover {
        background-color: #1976d2;
      }
    }
  }

  // Form elements styling
  .MuiFormLabel-root {
    font-size: 12px;
    margin-bottom: 5px;
    display: block;
    text-transform: capitalize;
    font-weight: normal;
  }

  .MuiInputBase-root {
    border-radius: 15px;
    font-size: 12px;
    padding: 2px;
  }

  .MuiOutlinedInput-root {
    border-radius: 15px;
  }

  .MuiOutlinedInput-input {
    padding: 6px 10px;
  }

  .MuiInputBase-input {
    height: auto;
    font-size: 12px;
  }

  .date-picker-input {
    .MuiInputBase-root {
      border-radius: 15px;
    }

    .MuiOutlinedInput-root {
      border-radius: 15px;
    }

    .MuiOutlinedInput-input {
      padding: 5px 10px;
      font-size: 12px;
    }
  }

  // Upload buttons
  .upload-button {
    background-color: #2196f3;
    color: white;
    border-radius: 15px;
    padding: 2px 10px;
    font-size: 12px;
    text-transform: capitalize;
    height: 24px;

    &:hover {
      background-color: #1976d2;
    }
  }

  // Add buttons (black)
  .add-button {
    background-color: #000;
    color: white;
    border-radius: 15px;
    padding: 2px 12px;
    font-size: 12px;
    text-transform: capitalize;
    margin: 5px auto;
    display: block;
    height: 26px;

    &:hover {
      background-color: #333;
    }
  }

  // Tags/chips styling
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
  }

  .MuiChip-root {
    border-radius: 15px;
    height: 22px;
    font-size: 11px;
    background-color: #f1f1f1;
  }

  // Search input
  .search-container {
    margin-bottom: 5px;

    .MuiInputBase-root {
      border-radius: 20px;
    }
  }

  // Item rows
  .item-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    padding: 5px;
    border-radius: 4px;
    background-color: #f9f9f9;
    font-size: 14px;

    .item-actions {
      margin-left: auto;
      display: flex;
      gap: 5px;

      .MuiIconButton-root {
        padding: 5px;
      }
    }
  }

  // Delete icon
  .delete-icon {
    color: #000;
    font-size: 18px;
  }

  // Editor container
  .editor-container {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;

    .quill {
      .ql-toolbar {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
      }

      .ql-container {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        height: 150px;
      }
    }
  }

  // Checkbox and radio styling
  .MuiFormControlLabel-root {
    margin-right: 0;

    .MuiCheckbox-root, .MuiRadio-root {
      padding: 4px;
    }

    .MuiFormControlLabel-label {
      font-size: 14px;
    }
  }

  // Switch styling
  .MuiSwitch-root {
    height: 26px;
    width: 42px;
    padding: 0;
    margin-right: 8px;

    .MuiSwitch-switchBase {
      padding: 3px;
    }

    .MuiSwitch-thumb {
      width: 16px;
      height: 16px;
    }

    .MuiSwitch-track {
      border-radius: 13px;
    }
  }
}

// Specific component styles
.banner-slider-section {
  @extend .homepage-section;

  .banner-row {
    background-color: #f9f9f9;
    border-radius: 15px;
    padding: 5px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    border: 1px solid #707070;

    .banner-content {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .banner-actions {
      display: flex;
      align-items: center;
    }
  }
}

.coupon-of-the-week-section {
  @extend .homepage-section;

  .coupon-row {
    background-color: #f9f9f9;
    border-radius: 15px;
    padding: 5px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    border: 1px solid #707070;

    .coupon-content {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .coupon-actions {
      display: flex;
      align-items: center;
    }
  }
}

.popular-coupons-section {
  @extend .homepage-section;
}

.popular-brands-section {
  @extend .homepage-section;
}

.categories-section {
  @extend .homepage-section;

  .mode-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 5px;

    .mode-button {
      border-radius: 20px;
      padding: 5px 15px;
      font-size: 12px;
      background-color: #f1f1f1;
      border: 1px solid #ddd;

      &.active {
        background-color: #000;
        color: white;
      }
    }
  }
}

.text-banner-section {
  @extend .homepage-section;

  .MuiFormLabel-root {
    font-weight: 600;
  }
}

.tested-for-you-section {
  @extend .homepage-section;

  .edit-icon {
    color: #000;
    font-size: 18px;
  }
}

// Autocomplete styling
.MuiAutocomplete-root {
  .MuiInputBase-root {
    padding: 0px 8px;
    height: 32px;
  }

  .MuiAutocomplete-endAdornment {
    right: 8px;
  }

  .MuiAutocomplete-inputRoot {
    padding: 0 !important;
  }

  .MuiAutocomplete-option {
    font-size: 12px;
    min-height: 24px;
    padding: 3px 10px;
  }

  .MuiAutocomplete-paper {
    border-radius: 10px;
    margin-top: 4px;
  }
}

// Icon buttons
.MuiIconButton-root {
  padding: 3px;
}

.MuiSvgIcon-root {
  font-size: 18px;
}
