import API from 'services/api';

export interface CouponOfTheWeek {
    id?: number;
    brandId: number;
    couponId: number;
    startDate: string;
    endDate: string;
    status: string;
}

export interface CouponOfTheWeekListRequest {
    page: number;
    size: number;
    filters: {
        status: string;
    };
}

export interface CouponOfTheWeekResponse {
    data: CouponOfTheWeek[];
    total: number;
}

export const getCouponOfTheWeeks = async (request: CouponOfTheWeekListRequest): Promise<CouponOfTheWeekResponse> => {
    const response = await API.post('/coupon-of-the-week/list', request);
    return response.data;
};

export const getCouponOfTheWeekById = async (brandId: number, couponOfTheWeekId: number): Promise<CouponOfTheWeek> => {
    const response = await API.get(`/coupon-of-the-week/brand/${brandId}/coupon-of-the-week/${couponOfTheWeekId}`);
    return response.data;
};

export const createCouponOfTheWeek = async (data: CouponOfTheWeek): Promise<CouponOfTheWeek> => {
    const response = await API.post('/coupon-of-the-week', data);
    return response.data;
};

export const updateCouponOfTheWeek = async (data: CouponOfTheWeek): Promise<CouponOfTheWeek> => {
    const response = await API.put('/coupon-of-the-week', data);
    return response.data;
};

export const deleteCouponOfTheWeek = async (couponOfTheWeekId: number): Promise<void> => {
    await API.delete(`/coupon-of-the-week/${couponOfTheWeekId}`);
};
