import { Checkbox, makeStyles } from '@material-ui/core';
import React, { useCallback } from 'react';

const useStyles = makeStyles({
  root: {
    height: 42,
  },
});

interface Props {
  isDisabled: boolean;
  isRequested: boolean;
  onRequestChange(isRequested: boolean): void;
}

const UnlockRequestInput = ({
  isDisabled,
  isRequested,
  onRequestChange,
}: Props) => {
  const classes = useStyles();

  const onChange = useCallback(
    () => onRequestChange(!isRequested),
    [isRequested, onRequestChange]
  );

  return (
    <Checkbox
      className={classes.root}
      checked={isRequested}
      onChange={onChange}
      inputProps={{ disabled: isDisabled }}
    />
  );
};

export default UnlockRequestInput;
