import API from 'services/api';

export interface Banner {
    id?: number;
    desktopBanner: string;
    mobileBanner: string;
    brandId: number;
    startDate: string;
    endDate: string;
    link: string;
}

export interface IBannerSlider {
    id?: number;
    enableAutoSlide: boolean;
    autoSlideTime: number;
    banners: Banner[];
}

export interface BannerSliderListRequest {
    page: number;
    size: number;
    filters: {
        status: string;
    };
}

export interface BannerSliderResponse {
    data: IBannerSlider[];
    total: number;
}

export const getBannerSliders = async (request: BannerSliderListRequest): Promise<BannerSliderResponse> => {
    const response = await API.post('/banner-slider/list', request);
    return response.data;
};

export const getBrandBannerSlider = async (brandId: number, bannerSliderId: number): Promise<IBannerSlider> => {
    const response = await API.get(`/banner-slider/brand/${brandId}/bannerSlider/${bannerSliderId}`);
    return response.data;
};

export const createBannerSlider = async (bannerSlider: IBannerSlider): Promise<IBannerSlider> => {
    const response = await API.put('/banner-slider', bannerSlider);
    return response.data;
};

export const updateBannerSlider = async (bannerSlider: IBannerSlider): Promise<IBannerSlider> => {
    const response = await API.put('/banner-slider', bannerSlider);
    return response.data;
};

export const deleteBannerSlider = async (bannerId: number): Promise<void> => {
    await API.delete(`/banner-slider/${bannerId}`);
};

export const uploadFile = async (file: File, bannerId?: number, imageType?: string) => {
    const url = `/media/upload?entityType=BannerSlider${bannerId ? `&bannerId=${bannerId}` : ''}${imageType ? `&imageType=${imageType}` : ''}`;
    const formData = new FormData();
    formData.append('file', file);
    return await API.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
};

export const deleteFile = async (file: string) => {
    return await API.delete(`/media/delete?s3FileName=${file}`);
};
