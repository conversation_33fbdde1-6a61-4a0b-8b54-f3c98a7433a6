import * as PopularCouponsTypes from './PopularCouponsTypes';
import { PopularCoupons } from 'repository/popularEntities';

interface State {
    loading: boolean;
    error: any;
    popularCoupons: PopularCoupons | null;
}

const initialState: State = {
    loading: false,
    error: null,
    popularCoupons: null
};

export default function reducer(state = initialState, action) {
    switch (action.type) {
        case PopularCouponsTypes.GET_POPULAR_COUPONS:
        case PopularCouponsTypes.UPDATE_POPULAR_COUPONS:
            return {
                ...state,
                loading: true,
                error: null
            };
        case PopularCouponsTypes.GET_POPULAR_COUPONS_SUCCESS:
            return {
                ...state,
                loading: false,
                popularCoupons: action.payload
            };
        case PopularCouponsTypes.UPDATE_POPULAR_COUPONS_SUCCESS:
            return {
                ...state,
                loading: false,
                popularCoupons: action.payload
            };
        case PopularCouponsTypes.GET_POPULAR_COUPONS_ERROR:
        case PopularCouponsTypes.UPDATE_POPULAR_COUPONS_ERROR:
            return {
                ...state,
                loading: false,
                error: action.payload
            };
        default:
            return state;
    }
}
