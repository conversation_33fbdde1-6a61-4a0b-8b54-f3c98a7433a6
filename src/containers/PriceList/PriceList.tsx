import React, { useEffect } from 'react';
import { PageTitle, Loading } from 'components';
import { AdminForm } from './Forms/AdminForm';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import * as priceListActions from './PriceListActions';

export default () => {
  const { t }: { t: any } = useTranslation();
  const dispatch = useDispatch();
  const packages = useSelector((state: any) => state.priceList);

  useEffect(() => {
    dispatch(priceListActions.getPackages());
  }, [dispatch]);

  return (
    <>
      <PageTitle title={t('priceList')} />
      {packages.loading || packages.data.length === 0 ? (
        <Loading />
      ) : (
        <AdminForm packagesList={packages.data} />
      )}
    </>
  );
};
