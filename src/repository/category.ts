import API from 'services/api';

export const uploadFile = async (file) => {
  return await API.post('/media/upload?entityType=Category', file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const deleteFile = async (file) => {
  return await API.delete(`/media/delete?s3FileName=${file}`);
};

export const getCategories = (props) => {
  const {
    pageSize = 10,
    page = 0,
    sort = { field: 'id', dir: 'DESC' },
    search = '',
  } = props;
  return API.post('/categories/list', {
    page,
    pageSize,
    sort: sort.field
      ? [{ field: sort.field, dir: sort.dir.toUpperCase() }]
      : [],
    search,
  });
};

export const createCategory = (props) => {
  return API.post('/categories', {
    ...props,
    isPopular: props.isPopular === undefined ? 0 : +props.isPopular,
  });
};

export const deleteCategory = async (id) => {
  // expected error messages from BE
  // - categoryNotFound (with id)
  return await API.delete(`/categories/${id}`);
};

export const getCategory = (id) => {
  // expected error messages from BE
  // - categoryNotFound (with id)
  return API.get(`/categories/${id}`);
};

export const editCategory = (id, data) => {
  // expected error messages from BE
  // - categoryNotFound (with id)
  return API.put(`/categories/`, {
    id,
    ...data,
    isPopular: data.isPopular === undefined ? 0 : +data.isPopular,
  });
};

export const getAllCategoriesMap = () => {
  return API.get('/categories/allCategoriesMap');
};
