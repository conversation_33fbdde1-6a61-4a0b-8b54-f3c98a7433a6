import React, { useState, useCallback } from 'react';
import { Grid, Button, TextField } from '@material-ui/core';
import SearchIcon from '@material-ui/icons/Search';
import { useDispatch } from 'react-redux';
import { debounce } from 'utils/debouncer';
import { PageTitle, BoxWrap } from 'components';
import './Consumer.scss';
import { useTranslation } from 'react-i18next';

export const ConsumerView = ({
  onFilterChange,
  filter,
  children,
  exportAllConsumers,
}) => {
  const { t }: { t: any } = useTranslation();
  const [consumerQuery, setConsumerQuery] = useState('');
  const delayedQuery = useCallback(
    debounce((q) => {
      onFilterChange({
        ...filter,
        search: q,
      });
    }, 500),
    [filter]
  );

  const onChange = (e) => {
    setConsumerQuery(e.target.value);
    delayedQuery(e.target.value);
  };

  return (
    <div className="page consumer">
      <PageTitle title={t('customersManagement')}>
        <Button
          onClick={exportAllConsumers}
          color="primary"
          variant="contained"
        >
          {t('exportCustomers')}
        </Button>
      </PageTitle>
      <BoxWrap>
        <BoxWrap.Toolbar>
          <Grid container spacing={1} alignItems="flex-end">
            <Grid item>
              <SearchIcon />
            </Grid>
            <Grid item>
              <TextField
                id="input-with-icon-grid"
                label={t('searchCustomers')}
                onChange={onChange}
                value={consumerQuery}
              />
            </Grid>
          </Grid>
        </BoxWrap.Toolbar>
        {children}
      </BoxWrap>
    </div>
  );
};
