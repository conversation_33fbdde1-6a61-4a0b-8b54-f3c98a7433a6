import React from 'react';
import { useHistory } from 'react-router-dom';
import { makeStyles } from '@material-ui/core/styles';
import Card from '@material-ui/core/Card';
import CardActions from '@material-ui/core/CardActions';
import CardContent from '@material-ui/core/CardContent';
import Button from '@material-ui/core/Button';
import { Box, Typography } from '@material-ui/core';

interface Props {
  title?: string;
  description?: string;
  icon?: string;
  redirectTo?: string;
  customAction?: () => void;
  customButton?: React.ReactNode;
  isDisabled?: boolean;
}

const useStyles = makeStyles({
  root: {
    borderRadius: '10px',
    padding: '16px',
    boxShadow: 'none',
    border: '1px solid #E5E5E5',
  },
  title: {
    fontSize: 18,
    fontWeight: 700,
  },
  description: {
    fontSize: 15
  }
});

const PageCard = function({
  title, description, 
  icon, redirectTo, 
  customAction,
  customButton, 
  isDisabled = false 
}: Props) {
  const classes = useStyles();

  let history = useHistory();

  function handleRedirect() {
    if(customAction) {
      customAction()
    } else {
      history.push(redirectTo);
    }
  }

  return (
    <Card className={classes.root}>
      <CardContent style={{ padding: '0px'}}>
        <Box display={'flex'} flexDirection={'column'} style={{ opacity: isDisabled ? 0.5 : 1 }}>
          <Box
            height={140} 
            display={'flex'} 
            justifyContent={'center'} 
            alignItems={'center'} 
            borderRadius={10} 
            style={{ background: '#008BFF' }}
            marginBottom={1.5}
          >
            <img src={icon} alt="icon" height='70px' width='68px' />
          </Box>
          <Typography variant='subtitle1' style={{ fontSize: '18px', fontWeight: 'bold' }}>
            {title}
          </Typography>
          <Typography variant='subtitle1' style={{ fontSize: '15px', minHeight: '52px' }}>
            {description}
          </Typography>
        </Box>
      </CardContent>
      <CardActions style={{ padding: '0px', marginTop: '8px'}}>
        {
          ((redirectTo || customAction) && !customButton) && (
            <Button size="small" className='button black-button' onClick={handleRedirect}>VERWALTEN</Button>
          )
        }

      {
        customButton && customButton
      }
      </CardActions>
    </Card>
  );
}

export default PageCard;