import React, { useEffect, useCallback } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import RadioButtonUncheckedOutlinedIcon from '@material-ui/icons/RadioButtonUncheckedOutlined';
import RadioButtonCheckedIcon from '@material-ui/icons/RadioButtonChecked';
import { Button, Grid, FormLabel } from '@material-ui/core';
import * as priceListActions from '../PriceListActions';
import { BoxWrap, Loading } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import {
  FormTextField,
  FormCheckBox,
  FormNumberFormat,
} from 'components/FormInputs';

export const AdminForm = ({ packagesList }) => {
  const { t }: { t: any } = useTranslation();
  const { addMessage } = useAPIError();
  const dispatch = useDispatch();
  const priceList = useSelector((state: any) => state.priceList);

  const form = useForm({
    criteriaMode: 'all',
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: {
      packages: packagesList,
    },
  });

  const { control, handleSubmit, reset, errors } = form;

  const { fields } = useFieldArray({
    control,
    name: 'packages',
  });

  useEffect(() => {
    if (priceList.error) {
      addMessage(t(priceList.error.errorCode), 'error');
    }
    if (priceList.success) {
      addMessage(t('priceListUpdateSuccess'), 'success');
    }

    return () => dispatch(priceListActions.clearState());
  }, [priceList, dispatch]);

  const onSubmit = useCallback(
    (submitData) => {
      const payload = submitData.packages.map((submitDataPackage, index) => ({
        ...packagesList[index],
        ...submitDataPackage,
      }));

      dispatch(priceListActions.savePackages(payload));
    },
    [packagesList, dispatch]
  );

  const cancelClick = () => {
    reset({
      packages: packagesList.map((packageListItem) => ({
        coupons: packageListItem.coupons,
        couponUpdates: packageListItem.couponUpdates,
        freeImageVideo: packageListItem.freeImageVideo,
        pricePerCoupon: packageListItem.pricePerCoupon,
        pricePerCouponUpdate: packageListItem.pricePerCouponUpdate,
        pricePerImageVideo: packageListItem.pricePerImageVideo,
        pricePerUnboxingVideo: packageListItem.pricePerUnboxingVideo,
      })),
    });
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        {fields.length ? (
          fields.map((item, index) => (
            <BoxWrap key={item.id}>
              <Grid container spacing={7}>
                <Grid item sm={6} xs={12}>
                  <FormLabel>{t(item.name)}</FormLabel>
                </Grid>
              </Grid>
              <Grid container spacing={7}>
                <Grid item sm={6} xs={12}>
                  <FormTextField
                    name={`packages[${index}].coupons`}
                    label={t('amountCoupons')}
                    rules={{
                      required: t('amountCouponRequired'),
                      min: {
                        value: 0,
                        message: t('pleaseInsertPositiveNumberOr0'),
                      },
                    }}
                    control={control}
                    errors={errors}
                    type="number"
                    fullWidth={true}
                  />
                </Grid>
                <Grid item sm={6} xs={12}>
                  <FormNumberFormat
                    name={`packages[${index}].pricePerCoupon`}
                    control={control}
                    rules={{
                      required: t('pricePerCouponRequired'),
                      min: {
                        value: 0,
                        message: t('pleaseInsertPositiveNumberOr0'),
                      },
                    }}
                    label={t('pricePerCoupon')}
                    errors={errors}
                  />
                </Grid>
              </Grid>
              <Grid container spacing={7}>
                <Grid item sm={6} xs={12}>
                  <FormTextField
                    name={`packages[${index}].couponUpdates`}
                    label={t('amountRefreshes')}
                    rules={{
                      required: t('amountRefreshesRequired'),
                      min: {
                        value: 0,
                        message: t('pleaseInsertPositiveNumberOr0'),
                      },
                    }}
                    control={control}
                    errors={errors}
                    type="number"
                    fullWidth={true}
                  />
                </Grid>
                <Grid item sm={6} xs={12}>
                  <FormNumberFormat
                    name={`packages[${index}].pricePerCouponUpdate`}
                    control={control}
                    rules={{
                      required: t('pricePerRefreshRequired'),
                      min: {
                        value: 0,
                        message: t('pleaseInsertPositiveNumberOr0'),
                      },
                    }}
                    label={t('pricePerRefresh')}
                    errors={errors}
                  />
                </Grid>
              </Grid>
              <Grid container spacing={7}>
                <Grid item sm={6} xs={12}>
                  <FormCheckBox
                    control={control}
                    name={`packages[${index}].freeImageVideo`}
                    label={t('freeImageVideo')}
                    icon={<RadioButtonUncheckedOutlinedIcon />}
                    checkedIcon={<RadioButtonCheckedIcon />}
                    style={{ marginLeft: '-12px' }}
                  />
                </Grid>
                <Grid item sm={6} xs={12}>
                  <FormNumberFormat
                    name={`packages[${index}].pricePerImageVideo`}
                    control={control}
                    rules={{
                      required: t('pricePerImageVideoRequired'),
                      min: {
                        value: 0,
                        message: t('pleaseInsertPositiveNumberOr0'),
                      },
                    }}
                    label={t('pricePerImageVideo')}
                    errors={errors}
                  />
                </Grid>
              </Grid>
              <Grid container spacing={7}>
                <Grid item sm={6} xs={12}>
                  <FormCheckBox
                    control={control}
                    name={`packages[${index}].freeUnboxingVideo`}
                    label={t('freeUnboxingVideo')}
                    icon={<RadioButtonUncheckedOutlinedIcon />}
                    checkedIcon={<RadioButtonCheckedIcon />}
                    style={{ marginLeft: '-12px' }}
                  />
                </Grid>
                <Grid item sm={6} xs={12}>
                  <FormNumberFormat
                    name={`packages[${index}].pricePerUnboxingVideo`}
                    control={control}
                    rules={{
                      required: t('pricePerUnboxingVideoRequired'),
                      min: {
                        value: 0,
                        message: t('pleaseInsertPositiveNumberOr0'),
                      },
                    }}
                    label={t('pricePerUnboxingVideo')}
                    errors={errors}
                  />
                </Grid>
              </Grid>
            </BoxWrap>
          ))
        ) : (
          <Loading />
        )}
        <Button
          className="submit-button"
          type="submit"
          variant="contained"
          color="primary"
        >
          {t('save')}
        </Button>
        <Button
          onClick={cancelClick}
          className={'btn btn-cancel'}
          color="primary"
        >
          {t('cancel')}
        </Button>
      </form>
    </>
  );
};
