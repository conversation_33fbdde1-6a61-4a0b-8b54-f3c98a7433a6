import React from 'react';
import { AdminForm } from './Forms';
import { PageTitle, Loading } from 'components';

import './BrandUserForm.scss';
import { useTranslation } from 'react-i18next';

export const BrandUserFormView = ({ data, brandPages, loading }) => {
  const { t }: { t: any } = useTranslation();

  return (
    <div className="user-create-page">
      <PageTitle
        title={`${data?.activeUser?.id ? t('edit') : t('createBrandUser')} `}
      />
      {loading && <Loading />}
      <AdminForm user={data.activeUser} brandPages={brandPages} />
    </div>
  );
};
