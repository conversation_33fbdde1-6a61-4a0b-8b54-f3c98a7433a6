import {
  GET_CATEGORIES,
  GET_CATEGORIES_ERROR,
  GET_CATEGORIES_SUCCESS,
  DELETE_CATEGORIES,
  DELETE_CATEGORIES_ERROR,
  DELETE_CATEGORIES_SUCCESS,
  CATEGORIES_PAGINATION,
  GET_ALL_CATEGORIES_MAP,
  GET_ALL_CATEGORIES_MAP_SUCCESS,
  GET_ALL_CATEGORIES_MAP_ERROR,
  CHANGE_PAGINATION,
} from './CategoriesTypes';

import * as CategoryService from 'repository/category';

export const changeFilter = (filter) => {
  return async (dispatch) => {
    dispatch({
      type: CHANGE_PAGINATION,
      payload: {
        page: filter.page,
        pageSize: filter.pageSize,
        sort: { ...filter.sort },
      },
    });
  };
};

export const getCategories = (filter = {}) => {
  return async (dispatch) => {
    dispatch({
      type: GET_CATEGORIES,
    });

    try {
      const response = await CategoryService.getCategories(filter);
      dispatch({
        type: GET_CATEGORIES_SUCCESS,
        payload: response.data.data,
      });
      dispatch({
        type: CATEGORIES_PAGINATION,
        payload: {
          current: response.data.number + 1,
          first: 1,
          last: response.data.total,
        },
      });
    } catch (error) {
      dispatch({
        type: GET_CATEGORIES_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const deleteCategory = (category) => {
  return async (dispatch) => {
    dispatch({
      type: DELETE_CATEGORIES,
    });
    try {
      const response = await CategoryService.deleteCategory(category);
      dispatch({
        type: DELETE_CATEGORIES_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: DELETE_CATEGORIES_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getCategoriesMap = () => {
  return async (dispatch) => {
    dispatch({
      type: GET_ALL_CATEGORIES_MAP,
    });

    try {
      const response = await CategoryService.getAllCategoriesMap();
      dispatch({
        type: GET_ALL_CATEGORIES_MAP_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: GET_ALL_CATEGORIES_MAP_ERROR,
        payload: error.response?.data,
      });
    }
  };
};
