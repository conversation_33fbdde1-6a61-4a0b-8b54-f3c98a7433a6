import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Snackbar } from '@material-ui/core';
import { Redirect } from 'react-router-dom';

import { BrandUserFormView } from './BrandUserFormView';
import { getUser, clearSuccess } from '../UserForm/UserFormActions';
import { getBrandPages } from '../BrandPages/BrandPagesActions';
import {
  CLEAR_USER,
  CLEAR_USER_ERROR_MESSAGES,
} from '../UserForm/UserFormTypes';

export default (props) => {
  const dispatch = useDispatch();
  const userForm = useSelector((state: any) => state.userForm);
  const brandPages = useSelector((state: any) => state.brandPages);
  const userId = props.match.params.id;

  useEffect(() => {
    dispatch({ type: CLEAR_USER });
    dispatch({ type: CLEAR_USER_ERROR_MESSAGES });
    dispatch(
      getBrandPages({
        pageSize: 999999,
        page: 0,
        sort: {
          field: 'id',
          dir: 'desc',
        },
        search: '',
      })
    );
    if (userId !== 'create') {
      dispatch(getUser(userId));
    }
  }, [dispatch, userId]);

  useEffect(() => () => dispatch({ type: CLEAR_USER }), []);

  const resetSuccess = () => {
    dispatch(clearSuccess());
  };

  if (userForm.error && userForm.error.errorCode === 'userNotFound') {
    return <Redirect to="/dashboard/brand-users/404" />;
  } else {
    return (
      <>
        {brandPages.data && (userForm.activeUser || userId === 'create') ? (
          <BrandUserFormView
            data={userForm}
            brandPages={brandPages.data}
            loading={userForm?.loading}
          />
        ) : null}
        <Snackbar
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          open={userForm?.success}
          onClose={resetSuccess}
          autoHideDuration={6000}
          message={
            props.match.params.id ? (
              <span id="message-id">{'formUpdateSuccess'}</span>
            ) : (
              <span id="message-id">
                {'formCreateSuccess'}{' '}
                <Link to={'/dashboard/brand-user/' + userForm.id}>
                  {'formEditLink'}
                </Link>
              </span>
            )
          }
        />
      </>
    );
  }
};
