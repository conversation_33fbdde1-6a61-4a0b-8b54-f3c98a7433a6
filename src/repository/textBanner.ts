import API from 'services/api';

export interface TextBanner {
    id?: number;
    content: string;
    isButton: boolean;
    buttonText?: string;
    buttonLink?: string;
    image?: string;
}

export const getTextBanner = async (): Promise<TextBanner> => {
    const response = await API.post('/text-banner/list');
    // The API returns { data: [{ ... }], total: number }
    // We need to extract the first item from the data array
    if (response.data && response.data.data && response.data.data.length > 0) {
        return response.data.data[0];
    }
    // Return empty object if no data is found
    return {
        content: '',
        isButton: false
    };
};

export const updateTextBanner = async (data: TextBanner): Promise<TextBanner> => {
    const response = await API.put('/text-banner', data);
    // The API might return the same structure as getTextBanner
    // Check if the response has a data array and extract the first item if needed
    if (response.data && response.data.data && response.data.data.length > 0) {
        return response.data.data[0];
    }
    return response.data;
};

export const uploadFile = async (file: File, textBannerId?: number) => {
    const url = `/media/upload?entityType=TextBanner${textBannerId ? `&textBannerId=${textBannerId}` : ''}`;
    const formData = new FormData();
    formData.append('file', file);
    return await API.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
};

export const deleteFile = async (file: string) => {
    return await API.delete(`/media/delete?s3FileName=${file}`);
};
