import * as headImgActionTypes from './HeaderImagesActionTypes';
import * as headerImagesRepository from 'repository/headerImages';

export const getHeaderImagesList = (callback?: Function) => {
  return async (dispatch) => {
    dispatch({
      type: headImgActionTypes.GET_HEAD_IMG_LIST,
    });

    try {
      const response = await headerImagesRepository.listHeaderImages();
      dispatch({
        type: headImgActionTypes.GET_HEAD_IMG_LIST_SUCCESS,
        payload: response.data,
      });
      callback && callback(response.data);
    } catch (error) {
      const errorData = error.response?.data;
      dispatch({
        type: headImgActionTypes.GET_HEAD_IMG_LIST_ERROR,
        payload: errorData,
      });
      callback && callback(errorData);
    }
  };
};

export const uploadHeaderImage = (
  formData,
  page,
  successCallback?: Function,
  errorCallback?: Function
) => {
  return async (dispatch) => {
    dispatch({
      type: headImgActionTypes.UPLOAD_HEAD_IMG,
      payload: page,
    });

    try {
      const response = await headerImagesRepository.uploadHeaderImage(
        formData,
        page
      );
      dispatch({
        type: headImgActionTypes.UPLOAD_HEAD_IMG_SUCCESS,
        payload: { page: page, data: response.data },
      });
      successCallback && successCallback(response.data);
    } catch (error) {
      const errorData = error.response?.data;
      dispatch({
        type: headImgActionTypes.UPLOAD_HEAD_IMG_ERROR,
        payload: { page: page, error: errorData },
      });
      errorCallback && errorCallback(errorData);
    }
  };
};
