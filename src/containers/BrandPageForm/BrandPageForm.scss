.s1description .ql-editor {
  min-height: 250px;
}

.brandDescription .ql-editor {
  min-height: 250px;
}

.brand-page-table-title {
  margin-top: 80px;
  margin-bottom: 10px;
}

.custom-mui-success,
.custom-mui-info,
.custom-mui-error {
  border-radius: 25px;
  color: black;
  padding: 16px 16px;
  .MuiSvgIcon-root {
    color: black;
    font-size: 40px;
  }
  .MuiAlert-message {
    margin-top: auto;
    margin-bottom: auto;
    font-weight: 500;
  }
}
.custom-mui-success {
  background-color: #00c46a;
}
.custom-mui-info {
  background-color: #ffba00;
}
.custom-mui-error {
  background-color: #EAEAEA;
}

.checkbox-section {
  margin-left: -12px;
  margin-top: -30px;
  display: flex;
  align-items: center;
}

.checkbox-label-14 {
  font-size: 14px;
  font-weight: 500;
}

.font-12 {
  font-size: 12px;
}

.form-sub-label {
  font-size: 12px;
  font-weight: 500;
}

.alert-box{
  background: #FFE600 !important;
  border-radius: 10px;
  color:black;
}