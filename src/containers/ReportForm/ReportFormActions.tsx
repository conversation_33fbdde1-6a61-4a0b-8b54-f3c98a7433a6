import {
  C<PERSON>AR_CATEGORY,
  <PERSON>LEAR_CATEGORY_SUCCESS,
  CREATE_CATEGORY,
  EDIT_CATEGORY,
  GET_CATEGORY,
  GET_CATEGORY_ERROR,
} from './ReportFormTypes';

export const createCategory = (type: string, user: any, callback: Function) => {
  return async (dispatch) => {
    dispatch({
      type: CREATE_CATEGORY,
    });
  };
};

export const editCategory = (
  type: string,
  id: number,
  user: any,
  callback: Function
) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_CATEGORY,
    });
  };
};

export const getCategory = (id: number) => {
  return async (dispatch) => {
    dispatch({
      type: GET_CATEGORY,
    });
  };
};

export const clearCategory = () => ({
  type: CLEAR_CATEGORY,
});

export const removeError = () => ({
  type: GET_CATEGORY_ERROR,
  payload: null,
});

export const clearSuccess = () => ({
  type: CLEAR_CATEGORY_SUCCESS,
  payload: null,
});
