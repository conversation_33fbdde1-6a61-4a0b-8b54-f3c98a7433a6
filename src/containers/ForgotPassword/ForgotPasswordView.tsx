import React from 'react';
import { Link } from 'react-router-dom';
import Snackbar from '@material-ui/core/Snackbar';
import { TextField, Button } from '@material-ui/core';
import { useForm } from 'react-hook-form';

import { emailRegex } from 'services/config';
import { IntroCard, InputError, Loading } from 'components';
import { useTranslation } from 'react-i18next';
import TrafficoLogo from "../../assets/images/traffico_logo.jpeg"

import './ForgotPassword.scss';

export const ForgotPasswordView = ({
  clearErrors,
  resetPassword,
  forgotPasswordState,
}) => {
  const { t }: { t: any } = useTranslation();
  const { register, handleSubmit, errors } = useForm();
  let title = t('forgotYourPassword');
  let subtitle = t('dontDispairWeveGotYou');

  if (forgotPasswordState?.success) {
    title = t('passwordReset');
    subtitle = t('youCaowUseYourNewPassword');
  }

  const submitForm: any = ({ email }) => {
    resetPassword(email);
  };

  return (
    <div className="fp-page">
      <IntroCard title={title} subtitle={subtitle}>
        {forgotPasswordState.loading ? <Loading /> : null}

        <div className="fp-form-wrap">
          <img src={TrafficoLogo} className='logo' alt='traffico_logo' width={350} />

          {forgotPasswordState.success ? (
            <span className="reset-success">
              <br></br>
              <p>{t('verificationEmailSent')}</p>
            </span>
          ) : (
            <>
              <div className='forget-password-text'>
                {t('forgotPassword')}
              </div>
              <div className="grower"></div>
              <form onSubmit={handleSubmit(submitForm)}>
                <TextField
                  inputRef={register({ required: true, pattern: emailRegex })}
                  className="fp-input"
                  margin="normal"
                  variant="outlined"
                  size="small"
                  label={t('email')}
                  inputProps={{
                    name: 'email',
                  }}
                  helperText={
                    errors.email && (
                      <InputError message={t('validEmailIsRequired')} />
                    )
                  }
                />
                <Button
                  type="submit"
                  className="fp-button"
                  fullWidth
                  variant="contained"
                  color="primary"
                  size="small"
                >
                  {t('resetPassword')}
                </Button>
              </form>
            </>
          )}

          <Link className="forgot-link" to="/login">
            {t('backToLoginScreen')}
          </Link>
        </div>
      </IntroCard>

      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        open={!!forgotPasswordState.error}
        autoHideDuration={6000}
        onClose={() => {
          clearErrors();
        }}
        message={
          <span id="message-id">
            {t(forgotPasswordState.error?.errorCode || 'errorOccured')}
          </span>
        }
      />
    </div>
  );
};
