import React, { useState, useCallback } from 'react';
import SearchIcon from '@material-ui/icons/Search';
import { Link } from 'react-router-dom';
import { Grid, Button, TextField } from '@material-ui/core';
import { PageTitle, BoxWrap } from 'components';
import { debounce } from 'utils/debouncer';
import { useTranslation } from 'react-i18next';

import './Product.scss';

export const ProductView = ({ onFilterChange, filter, children }) => {
  const { t }: { t: any } = useTranslation();
  const [userQuery, setUserQuery] = useState('');
  const delayedQuery = useCallback(
    debounce((q) => {
      onFilterChange({
        ...filter,
        search: q,
      });
    }, 500),
    [filter]
  );
  const onChange = (e) => {
    setUserQuery(e.target.value);
    delayedQuery(e.target.value);
  };

  return (
    <div className="page product">
      <PageTitle title={t('productManagement')}>
        <Button
          component={Link}
          to="/dashboard/memberships/create"
          color="primary"
          variant="contained"
        >
          {t('createProduct')}
        </Button>
      </PageTitle>
      <BoxWrap>
        <BoxWrap.Toolbar>
          <Grid container spacing={1} alignItems="flex-end">
            <Grid item>
              <SearchIcon />
            </Grid>
            <Grid item>
              <TextField
                id="input-with-icon-grid"
                label={t('searchProduct')}
                onChange={onChange}
                value={userQuery}
              />
            </Grid>
          </Grid>
        </BoxWrap.Toolbar>
        {children}
      </BoxWrap>
    </div>
  );
};
