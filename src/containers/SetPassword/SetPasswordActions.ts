import {
  SET_PASSWORD_START,
  SET_PASSWORD_SUCCESS,
  SET_PASSWORD_ERROR,
  SET_PASSWORD_SET_TOKEN,
} from './SetPasswordTypes';
import * as AuthRepository from 'repository/auth';

export const getTokenErrorType = (error) => {
  switch (error) {
    case 'verificationTokenNotFound':
      return 'invalid';
    case 'verificationTokenExpired':
      return 'expired';
    default:
      return 'unknown error';
  }
};

export const setPasswordUser = (data, redirect = true) => {
  return async (dispatch) => {
    dispatch({
      type: SET_PASSWORD_START,
    });

    try {
      const response = await AuthRepository.setPasswordUser(data);

      if (response.data && response.data === 'Token is valid.') {
        dispatch({
          type: SET_PASSWORD_SUCCESS,
          payload: response.data,
        });

        if (redirect) {
          window.location.href = '/login';
        }
      } else {
        dispatch({
          type: SET_PASSWORD_ERROR,
          payload: response.data,
        });
      }
    } catch (error) {
      dispatch({
        type: SET_PASSWORD_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearErrors = () => ({
  type: SET_PASSWORD_ERROR,
  payload: null,
});

export const setPasswordSetToken = (token) => ({
  type: SET_PASSWORD_SET_TOKEN,
  payload: token,
});
