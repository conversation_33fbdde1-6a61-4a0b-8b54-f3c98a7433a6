import React, { useState, useCallback } from 'react';
import { Grid, Button, TextField } from '@material-ui/core';
import { PageTitle, BoxWrap } from 'components';
import SearchIcon from '@material-ui/icons/Search';
import { debounce } from 'utils/debouncer';

import './BrandUsers.scss';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

export const BrandUsersView = ({ onFilterChange, filter, children }) => {
  const [userQuery, setUserQuery] = useState('');
  const delayedQuery = useCallback(
    debounce((q) => {
      onFilterChange({
        ...filter,
        search: q,
      });
    }, 500),
    [filter]
  );

  const { t }: { t: any } = useTranslation();

  const onChange = (e) => {
    setUserQuery(e.target.value);
    delayedQuery(e.target.value);
  };

  return (
    <div className="user-page">
      <PageTitle title={t('brandUserManagement')}>
        <Button
          component={Link}
          to="/dashboard/brand-user/create"
          color="primary"
          variant="contained"
        >
          {t('createUser')}
        </Button>
      </PageTitle>
      <BoxWrap>
        <BoxWrap.Toolbar>
          <Grid container spacing={1} alignItems="flex-end">
            <Grid item>
              <SearchIcon />
            </Grid>
            <Grid item>
              <TextField
                id="input-with-icon-grid"
                style={{ minWidth: '350px' }}
                label={t('searchUsersOrBrands')}
                onChange={onChange}
                value={userQuery}
              />
            </Grid>
          </Grid>
        </BoxWrap.Toolbar>
        {children}
      </BoxWrap>
    </div>
  );
};
