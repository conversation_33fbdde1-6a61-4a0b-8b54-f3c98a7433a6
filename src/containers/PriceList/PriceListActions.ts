import * as priceListActionTypes from './PriceListActionTypes';
import * as PriceListService from 'repository/price-list';

export const getPackages = () => {
  return async (dispatch) => {
    dispatch({
      type: priceListActionTypes.GET_PACKAGES,
    });
    try {
      const response = await PriceListService.getPackages();
      dispatch({
        type: priceListActionTypes.GET_PACKAGES_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: priceListActionTypes.GET_PACKAGES_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const savePackages = (data) => {
  return async (dispatch) => {
    dispatch({
      type: priceListActionTypes.SAVE_PACKAGES,
    });

    try {
      await PriceListService.savePackages(data);
      dispatch({
        type: priceListActionTypes.SAVE_PACKAGES_SUCCESS,
        payload: data,
      });
    } catch (error) {
      dispatch({
        type: priceListActionTypes.SAVE_PACKAGES_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearState = () => ({ type: priceListActionTypes.CLEAR_STATE });
