import React, { useState, useEffect } from 'react';
import { But<PERSON>, FormLabel, TextField, IconButton } from '@material-ui/core';
import { Autocomplete } from '@material-ui/lab';
import { DatePickerInput } from 'components/FormInputs';
import DeleteIcon from '@material-ui/icons/Delete';
import { useForm, Controller } from 'react-hook-form';

interface BannerItem {
  id?: string;
  brand?: string;
  desktopBanner?: string;
  mobileBanner?: string;
  startDate?: Date | null;
  endDate?: Date | null;
  link?: string;
}

interface Brand {
  id: string;
  name: string;
}

interface BannerRowProps {
  banner?: BannerItem;
  setValue?: any;
  brands?: Brand[];
  handleBrandChange?: (event: any, value: Brand | null) => void;
  onImageChange?: (type: 'desktopBanner' | 'mobileBanner', file: File) => void;
  onRemove?: (index: number, banner: BannerItem) => void;
  index?: number;
}

const labelStyle = { fontSize: 12, marginBottom: 2 };
const inputStyle = { borderRadius: '12px', fontSize: 11, height: 28 };
const columnStyle = { width: '14%', marginRight: '2%' };

const BannerRow: React.FC<BannerRowProps> = ({
  banner = {},
  onRemove,
  brands = [],
  handleBrandChange,
  onImageChange,
  setValue: parentSetValue,
  index
}) => {
  // Local form for each row
  const { control, watch, reset } = useForm({
    defaultValues: {
      startDate: banner.startDate || null,
      endDate: banner.endDate || null,
      link: banner.link || ''
    }
  });

  // Reset form values when banner prop changes
  useEffect(() => {
    reset({
      startDate: banner.startDate || null,
      endDate: banner.endDate || null,
      link: banner.link || ''
    });
  }, [banner, reset]);

  // Watch for changes and notify parent
  const startDate = watch('startDate');
  const endDate = watch('endDate');
  const link = watch('link');

  // Sync startDate, endDate, and link to parent when they change
  useEffect(() => {
    if (parentSetValue) parentSetValue('startDate', startDate);
  }, [startDate]);
  useEffect(() => {
    if (parentSetValue) parentSetValue('endDate', endDate);
  }, [endDate]);
  useEffect(() => {
    if (parentSetValue) parentSetValue('link', link);
  }, [link]);

  // Track filenames to display
  const [desktopBannerFileName, setDesktopBannerFileName] = useState<string>('');
  const [mobileBannerFileName, setMobileBannerFileName] = useState<string>('');

  // When banner changes (e.g., initial load or after save), update filename display
  useEffect(() => {
    // If there's a URL, extract the filename for display
    if (banner.desktopBanner) {
      // Display actual filename from URL
      const fileName = getFileNameFromUrl(banner.desktopBanner);
      setDesktopBannerFileName(fileName);
    } else {
      setDesktopBannerFileName('');
    }

    if (banner.mobileBanner) {
      // Display actual filename from URL
      const fileName = getFileNameFromUrl(banner.mobileBanner);
      setMobileBannerFileName(fileName);
    } else {
      setMobileBannerFileName('');
    }
  }, [banner.desktopBanner, banner.mobileBanner]);

  // Helper to extract filename from URL
  const getFileNameFromUrl = (url: string): string => {
    if (!url) return '';

    // Handle base64 URLs specially (they don't have a real filename)
    if (url.startsWith('data:')) {
      return 'Current image';
    }

    // For regular URLs, get the last part after the last slash
    return url.split('/').pop() || '';
  };

  const handleDesktopBannerChange = (file: File) => {
    setDesktopBannerFileName(file.name);
    // Don't modify the actual banner.desktopBanner URL yet - that happens on save
    // Just notify parent component about the file selection
    onImageChange?.('desktopBanner', file);
  };

  const handleMobileBannerChange = (file: File) => {
    setMobileBannerFileName(file.name);
    // Don't modify the actual banner.mobileBanner URL yet - that happens on save
    // Just notify parent component about the file selection
    onImageChange?.('mobileBanner', file);
  };

  const truncateFileName = (text: string, maxLength: number): string => {
    if (!text) return '';

    // Already short enough.
    if (text.length <= maxLength) return text;

    const dotIdx = text.lastIndexOf('.');
    const extension = text.slice(dotIdx + 1);
    const extWithDots = `...${extension}`;

    // Characters left for the base (may be 0 or negative if ext is huge)
    const baseLen = Math.max(0, maxLength - extWithDots.length);
    const baseName = text.slice(0, baseLen);

    return `${baseName}${extWithDots}`;
  };

  return (
    <div className="banner-row" style={{
      backgroundColor: '#f8f9fa',
      border: '1px solid #707070',
      borderRadius: '12px',
      padding: '20px',
      marginBottom: '10px',
      display: 'flex',
      justifyContent: 'center'
    }}>
      <div className="banner-content">
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>brand</FormLabel>
          <Autocomplete
            options={brands}
            getOptionLabel={(option) => option.name}
            value={brands.find(b => b.name === banner.brand) || null}
            onChange={handleBrandChange}
            renderInput={(params) => (
              <TextField
                {...params}
                fullWidth
                variant="outlined"
                size="small"
                InputProps={{
                  ...params.InputProps,
                  style: inputStyle
                }}
                inputProps={{
                  ...params.inputProps,
                  style: { fontSize: 11, minHeight: 28 }
                }}
              />
            )}
            ListboxProps={{
              style: {
                maxHeight: 120,
                fontSize: 11,
                overflowY: 'auto',
                borderRadius: 8,
                boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
              }
            }}
          />
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>desktop banner</FormLabel>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <Button variant="contained" component="label" className="upload-button" style={{ fontSize: 10, height: 28 }}>
              UPLOAD
              <input
                type="file"
                hidden
                accept=".jpg,.jpeg,.png"
                onChange={(e) => {
                  if (e.target.files?.[0]) {
                    handleDesktopBannerChange(e.target.files[0]);
                  }
                }}
              />
            </Button>
            {desktopBannerFileName && (
              <div style={{ display: 'flex', alignItems: 'center', fontSize: 11 }}>
                <span style={{ color: '#000', textDecoration: 'underline' }}>
                  {truncateFileName(desktopBannerFileName, 12)}
                </span>
              </div>
            )}
          </div>
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>mobile banner</FormLabel>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <Button variant="contained" component="label" className="upload-button" style={{ fontSize: 10, height: 28 }}>
              UPLOAD
              <input
                type="file"
                hidden
                accept=".jpg,.jpeg,.png"
                onChange={(e) => {
                  if (e.target.files?.[0]) {
                    handleMobileBannerChange(e.target.files[0]);
                  }
                }}
              />
            </Button>
            {mobileBannerFileName && (
              <div style={{ display: 'flex', alignItems: 'center', fontSize: 11 }}>
                <span style={{ color: '#000', textDecoration: 'underline' }}>
                  {truncateFileName(mobileBannerFileName, 12)}
                </span>
              </div>
            )}
          </div>
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>start date</FormLabel>
          <DatePickerInput
            name="startDate"
            control={control}
            variant="outlined"
            size="small"
            label=""
            InputProps={{ style: inputStyle }}
          />
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>end date</FormLabel>
          <DatePickerInput
            name="endDate"
            control={control}
            variant="outlined"
            size="small"
            label=""
            InputProps={{ style: inputStyle }}
          />
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>link</FormLabel>
          <Controller
            name="link"
            control={control}
            render={({ onChange, onBlur, value, name, ref }) => (
              <TextField
                onChange={e => {
                  onChange(e);
                  parentSetValue && parentSetValue('link', e.target.value);
                }}
                onBlur={onBlur}
                value={value}
                name={name}
                inputRef={ref}
                fullWidth
                variant="outlined"
                size="small"
                InputProps={{ style: inputStyle }}
              />
            )}
          />
        </div>
      </div>
      {onRemove && typeof index === 'number' && (
        <div className="banner-actions" style={{ marginLeft: 'auto', paddingLeft: '10px' }}>
          <IconButton
            onClick={() => onRemove(index, banner)}
            style={{ color: '#757575' }}
          >
            <DeleteIcon />
          </IconButton>
        </div>
      )}
    </div>
  );
};

export default BannerRow;