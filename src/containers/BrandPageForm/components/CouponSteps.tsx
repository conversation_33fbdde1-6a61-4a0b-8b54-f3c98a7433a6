import React from 'react';
import { useTranslation } from 'react-i18next';
import { FormLabel, FormControl, Grid } from '@material-ui/core';
import CheckBoxRoundedIcon from '@material-ui/icons/CheckBoxRounded';

import { FormCheckBox, FormTextField } from 'components/FormInputs';

import styles from './CouponSteps.module.scss';

const maxLength = 256;

interface CouponStepsProps {
  control: any;
  watch: any;
  errors: any;
}

const CouponSteps = ({ control, errors, watch }: CouponStepsProps) => {
  const { t }: { t: any } = useTranslation();

  const isBrandInstructionUpdatedWatcher = watch('isBrandInstructionUpdated');

  return (
    <>
      <div className={styles.title}>
        <FormLabel>{t('redemptionInstruction')}</FormLabel>
      </div>
      <Grid container spacing={5}>
        <Grid item sm={6} xs={12}>
          <Grid item sm={12} xs={12}>
            <FormControl fullWidth>
              <FormCheckBox
                control={control}
                name="isBrandInstructionUpdated"
                label={t('showCustomSteps')}
                style={{ marginLeft: '-12px' }}
                checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
              />
            </FormControl>
          </Grid>

          {['1', '2', '3'].map((step) => (
            <Grid item xs={12}>
              <FormTextField
                control={control}
                name={`step${step}`}
                label={`${t('step')} ${step}`}
                multiline={true}
                rows={4}
                variant="outlined"
                rules={{
                  required:
                    isBrandInstructionUpdatedWatcher &&
                    t('stepRequired', { step }),
                  maxLength: {
                    value: maxLength,
                    message: t('stepLessThan', { step, maxLength }),
                  },
                }}
                disabled={!isBrandInstructionUpdatedWatcher}
                errors={errors}
              />
            </Grid>
          ))}
        </Grid>
      </Grid>
    </>
  );
};

export default CouponSteps;
