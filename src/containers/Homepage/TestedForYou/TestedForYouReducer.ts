import * as TestedForYouTypes from './TestedForYouTypes';
import { TestedForYou } from 'repository/testedForYou';

interface State {
    loading: boolean;
    error: any;
    testedForYous: TestedForYou[];
    selectedTestedForYou: TestedForYou | null;
    total: number;
    lastAction?: string;
}

const initialState: State = {
    loading: false,
    error: null,
    testedForYous: [],
    selectedTestedForYou: null,
    total: 0,
    lastAction: undefined,
};

export default function reducer(state = initialState, action: any) {
    switch (action.type) {
        case TestedForYouTypes.GET_TESTED_FOR_YOUS:
        case TestedForYouTypes.GET_TESTED_FOR_YOU:
        case TestedForYouTypes.CREATE_TESTED_FOR_YOU:
        case TestedForYouTypes.UPDATE_TESTED_FOR_YOU:
        case TestedForYouTypes.DELETE_TESTED_FOR_YOU:
        case TestedForYouTypes.UPLOAD_TESTED_FOR_YOU_IMAGE:
            return {
                ...state,
                loading: true,
                error: null,
                lastAction: action.type,
            };

        case TestedForYouTypes.GET_TESTED_FOR_YOUS_SUCCESS:
            return {
                ...state,
                loading: false,
                testedForYous: action.payload,
                total: action.payload.length,
                lastAction: action.type,
                error: null,
            };

        case TestedForYouTypes.GET_TESTED_FOR_YOU_SUCCESS:
            return {
                ...state,
                loading: false,
                selectedTestedForYou: action.payload,
                lastAction: action.type,
                error: null,
            };

        case TestedForYouTypes.CREATE_TESTED_FOR_YOU_SUCCESS:
            return {
                ...state,
                loading: false,
                testedForYous: [...state.testedForYous, action.payload],
                total: state.total + 1,
                lastAction: action.type,
                error: null,
            };

        case TestedForYouTypes.UPDATE_TESTED_FOR_YOU_SUCCESS:
            return {
                ...state,
                loading: false,
                testedForYous: state.testedForYous.map((item) =>
                    item.id === action.payload.id ? action.payload : item
                ),
                lastAction: action.type,
                error: null,
            };

        case TestedForYouTypes.DELETE_TESTED_FOR_YOU_SUCCESS:
            return {
                ...state,
                loading: false,
                testedForYous: state.testedForYous.filter((item) => item.id !== action.payload),
                total: state.total - 1,
                lastAction: action.type,
                error: null,
            };

        case TestedForYouTypes.UPLOAD_TESTED_FOR_YOU_IMAGE_SUCCESS:
            return {
                ...state,
                loading: false,
                lastAction: action.type,
                error: null,
            };

        case TestedForYouTypes.GET_TESTED_FOR_YOUS_ERROR:
        case TestedForYouTypes.GET_TESTED_FOR_YOU_ERROR:
        case TestedForYouTypes.CREATE_TESTED_FOR_YOU_ERROR:
        case TestedForYouTypes.UPDATE_TESTED_FOR_YOU_ERROR:
        case TestedForYouTypes.DELETE_TESTED_FOR_YOU_ERROR:
        case TestedForYouTypes.UPLOAD_TESTED_FOR_YOU_IMAGE_ERROR:
            return {
                ...state,
                loading: false,
                error: action.payload,
                lastAction: action.type,
            };

        default:
            return state;
    }
}
