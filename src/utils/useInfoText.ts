import { useTranslation } from 'react-i18next';

export const useInfoText = (
  translationKeys: Array<string>,
  setCurrentInfoText: (infoText) => void
) => {
  const { t }: { t: any } = useTranslation();

  const clickListenersByKey: Record<string, () => void> = {};

  translationKeys.forEach((key) => {
    clickListenersByKey[key] = () => setCurrentInfoText(t(key));
  });

  return clickListenersByKey;
};
