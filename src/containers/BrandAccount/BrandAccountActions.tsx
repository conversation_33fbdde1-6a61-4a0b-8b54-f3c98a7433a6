import * as BrandAccountService from '../../repository/brand-account';
import {
  AccountUpgradeRequest,
  ReachBoostUpgradeRequest,
} from '../../repository/brand-account';
import {
  GET_BRAND_USER,
  GET_BRAND_USER_ERROR,
  GET_BRAND_USER_SUCCESS,
  GET_BRAND_PACKAGE,
  GET_BRAND_PACKAGE_SUCCESS,
  GET_BRAND_PACKAGE_ERROR,
  SEND_REQUEST,
  SEND_REQUEST_ERROR,
  SEND_REQUEST_SUCCESS,
  CLEAR_STATE,
  SEND_REACH_BOOST_REQUEST,
  SEND_REACH_BOOST_REQUEST_SUCCESS,
  SEND_REACH_BOOST_REQUEST_ERROR,
} from './BrandAccountTypes';

export const getBrandAccountData = () => {
  return async (dispatch) => {
    dispatch({
      type: GET_BRAND_USER,
    });
    try {
      let [brandPackageData, brandHistoryData, brandUsersData] =
        await Promise.all([
          BrandAccountService.getBrandPackageForBrandAdmin(),
          BrandAccountService.getBrandHistoryAsBrandAdmin(),
          BrandAccountService.getBrandUsersAsBrandAdmin(),
        ]);

      const brandUserData = {
        ...brandPackageData.data,
        history: brandHistoryData.data,
        brandUsers: brandUsersData.data,
      };

      dispatch({
        type: GET_BRAND_USER_SUCCESS,
        payload: brandUserData,
      });
    } catch (error) {
      dispatch({
        type: GET_BRAND_USER_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getBrandPackageForBrandAdmin = () => {
  return async (dispatch) => {
    dispatch({
      type: GET_BRAND_PACKAGE,
    });
    try {
      const brandPackage =
        await BrandAccountService.getBrandPackageForBrandAdmin();
      dispatch({
        type: GET_BRAND_PACKAGE_SUCCESS,
        payload: brandPackage.data,
      });
    } catch (error) {
      dispatch({
        type: GET_BRAND_PACKAGE_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const sendUpgradeRequest = (request: AccountUpgradeRequest) => {
  return async (dispatch) => {
    dispatch({
      type: SEND_REQUEST,
    });
    try {
      await BrandAccountService.sendUpgradeRequest(request);
      await dispatch(getBrandAccountData());

      dispatch({
        type: SEND_REQUEST_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: SEND_REQUEST_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const sendReachBoostUpgradeRequest = (
  request: ReachBoostUpgradeRequest
) => {
  return async (dispatch) => {
    dispatch({
      type: SEND_REACH_BOOST_REQUEST,
    });
    try {
      await BrandAccountService.sendReachBoostUpgradeRequest(request);
      await dispatch(getBrandAccountData());

      dispatch({
        type: SEND_REACH_BOOST_REQUEST_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: SEND_REACH_BOOST_REQUEST_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearState = () => ({ type: CLEAR_STATE });
