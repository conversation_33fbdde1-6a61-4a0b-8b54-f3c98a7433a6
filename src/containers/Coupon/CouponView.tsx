import React, { useState, useCallback } from 'react';
import { Link, useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import SearchIcon from '@material-ui/icons/Search';
import {
  Grid,
  Button,
  TextField,
  Dialog,
  DialogActions,
} from '@material-ui/core';
import { debounce } from 'utils/debouncer';
import * as couponService from 'repository/coupon';
import * as RoleManager from 'services/role';

import { PageTitle, BoxWrap, CustomDialogTitle } from 'components';

import './Coupon.scss';

const checkCouponCreateCredits = async () => {
  try {
    const response = await couponService.getCouponCreationCreditsLeft();

    if (response.data === 'PROCEED') {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};

export const CouponView = ({
  onFilterChange,
  filter,
  brandPackage,
  children,
}) => {
  const { t }: { t: any } = useTranslation();
  const history = useHistory();

  const [userQuery, setUserQuery] = useState('');
  const [showNoCouponCreationsLeft, setShowNoCouponCreationsLeft] =
    useState(false);

  const secondaryTitle = brandPackage
    ? `(${brandPackage.usedCoupons}/${brandPackage.coupons})`
    : '';

  const delayedQuery = useCallback(
    debounce((q) => {
      onFilterChange({
        ...filter,
        search: q,
      });
    }, 500),
    [filter]
  );

  const onChange = (e) => {
    setUserQuery(e.target.value);
    delayedQuery(e.target.value);
  };

  const closeNoCouponCreationsLeftDialog = () => {
    setShowNoCouponCreationsLeft(false);
  };

  const handleCreateCouponClick = async () => {
    if (RoleManager.isAbleTo('brand_coupon', 'create')) {
      const hasCouponCreationCreditsLeft = await checkCouponCreateCredits();
      if (hasCouponCreationCreditsLeft) {
        history.push('/dashboard/brand-coupons/create');
      } else {
        setShowNoCouponCreationsLeft(true);
      }
    } else {
      history.push('/dashboard/coupons/create');
    }
  };

  return (
    <div className="page coupon">
      <PageTitle title={t('couponManagement')} secondaryTitle={secondaryTitle}>
        <Button
          onClick={handleCreateCouponClick}
          color="primary"
          variant="contained"
          className="primary-button"
          style={{ height: '32px', paddingLeft: '5rem', paddingRight: '5rem' }}
        >
          {t('createCoupon')}
        </Button>
      </PageTitle>
      <BoxWrap>
        <BoxWrap.Toolbar>
          <Grid container spacing={1} alignItems="flex-end">
            <Grid item>
              <SearchIcon />
            </Grid>
            <Grid item>
              <TextField
                id="input-with-icon-grid"
                label={t('searchCoupon')}
                onChange={onChange}
                value={userQuery}
              />
            </Grid>
          </Grid>
        </BoxWrap.Toolbar>
        {children}
      </BoxWrap>

      <Dialog
        open={showNoCouponCreationsLeft}
        onClose={closeNoCouponCreationsLeftDialog}
        aria-labelledby="responsive-dialog-title"
        disableBackdropClick
        disableEscapeKeyDown
        maxWidth="md"
      >
        <div className="no-coupon-creations-dialog">
          <CustomDialogTitle
            showCloseButton={true}
            onClose={closeNoCouponCreationsLeftDialog}
          >
            {t('noCouponsLeft')} <br /> {t('pleaseBuyMoreOrUpgrade')}
          </CustomDialogTitle>
          <DialogActions>
            <Button
              component={Link}
              autoFocus
              to={'/dashboard/brand-account'}
              color={'primary'}
              variant="contained"
              className="dialog-button"
            >
              {t('here')}
            </Button>
          </DialogActions>
        </div>
      </Dialog>
    </div>
  );
};
