import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { useHistory, Prompt } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';
import CheckBoxRoundedIcon from '@material-ui/icons/CheckBoxRounded';

import {
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@material-ui/core';
import { BoxWrap, InputError } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';

import * as userActions from '../../UserForm/UserFormActions';
import { emailRegex } from 'services/config';
import { useTranslation } from 'react-i18next';
import { FormCheckBox } from 'components/FormInputs';

const isNewUser = (id) => id === 'create';

export const AdminForm = ({ user, brandPages }) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const { id } = useParams();
  const { t }: { t: any } = useTranslation();
  const [emailNotValid, setEmailNotValid]: any = useState(false);
  const [isFormSubmitted, setIsFormSubmitted] = useState(false);
  const { addMessage } = useAPIError();
  const userForm = useSelector((state: any) => state.userForm);

  const {
    register,
    handleSubmit,
    control,
    errors,
    formState: { isDirty },
  } = useForm({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: {
      ...user,
      ...(isNewUser(id) && { roleId: 4 }),
    },
    criteriaMode: 'all',
  });

  const resendMail = useCallback(
    (event) => {
      event.stopPropagation();

      dispatch(userActions.resendActivationMail(id));
    },
    [dispatch, id]
  );

  useEffect(() => {
    window.addEventListener('beforeunload', (event) => {
      if (isDirty && !isFormSubmitted) {
        event.preventDefault();
        event.returnValue = t(`areYouSureYouWantToLeave`);
      }
    });
  }, []);

  const redirectOnSaveOrCancel = () => {
    const customRedirectURL = localStorage.getItem(
      'redirectAfterBrandUserSaveTo'
    );
    if (customRedirectURL) {
      localStorage.removeItem('redirectAfterBrandUserSaveTo');
      history.push(customRedirectURL);
    } else {
      history.push('/dashboard/brand-users');
    }
  };

  useEffect(() => {
    if (userForm.error?.errorCode === 'usernameAlreadyExists') {
      setEmailNotValid(true);
    } else if (userForm.activationError) {
      addMessage(
        t(userForm.activationError.errorCode || 'unexpectedError'),
        'error'
      );
    } else if (userForm.error) {
      addMessage(t(userForm.error.errorCode || 'unexpectedError'), 'error');
    } else if (userForm.success || userForm.editSuccess) {
      redirectOnSaveOrCancel();
    }
  }, [userForm, addMessage]);

  const submit = (data: any) => {
    setIsFormSubmitted(true);
    
    const submitData = {
      firstName: data.firstName,
      surname: data.surname,
      roleId: data.roleId,
      username: data.username,
      brandId: data.brandId,
      restrictSeoFields: !!data.restrictSeoFields,
      enableCouponLink: !!data.enableCouponLink,
      stripeCustomerId: data.stripeCustomerId,
    };

    if (!isNewUser(id)) {
      dispatch(userActions.editUser(Number.parseInt(id), submitData));
    } else {
      dispatch(userActions.createUser(submitData));
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(submit)}>
        <BoxWrap>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <TextField
                inputRef={register({
                  required: t('firstNameRequired'),
                  maxLength: {
                    value: 64,
                    message: t('firstNameLess64'),
                  },
                })}
                label={t('firstName')}
                fullWidth={true}
                inputProps={{
                  name: 'firstName',
                }}
                helperText={
                  errors.firstName && (
                    <InputError message={errors.firstName.message} />
                  )
                }
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                inputRef={register({
                  required: t('lastNameRequired'),
                  maxLength: {
                    value: 64,
                    message: t('lastNameLess64'),
                  },
                })}
                label={t('lastName')}
                fullWidth={true}
                inputProps={{
                  name: 'surname',
                }}
                helperText={
                  errors.surname && (
                    <InputError message={errors.surname.message} />
                  )
                }
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                inputRef={register({
                  required: t('emailRequired'),
                  pattern: {
                    value: emailRegex,
                    message: t('emailNotValid'),
                  },
                  maxLength: {
                    value: 100,
                    message: t('emailLess100'),
                  },
                })}
                label={t('email')}
                fullWidth={true}
                inputProps={{
                  name: 'username',
                }}
                disabled={!isNewUser(id)}
                helperText={
                  (errors.username && (
                    <InputError message={errors.username.message} />
                  )) ||
                  (emailNotValid && (
                    <InputError message={t('emailAlreadyExists')} />
                  ))
                }
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel htmlFor="roleId">{t('role')}</InputLabel>
                <Controller
                  name="roleId"
                  control={control}
                  rules={{ required: true }}
                  render={({ value }) => (
                    <Select value={value} fullWidth={true} disabled>
                      <MenuItem value={1}>{t('admin')}</MenuItem>
                      <MenuItem value={2}>{t('contentManager')}</MenuItem>
                      <MenuItem value={4}>{t('brandAdmin')}</MenuItem>
                    </Select>
                  )}
                />
                <ErrorMessage
                  errors={errors}
                  name="roleId"
                  render={({ message }) => (
                    <InputError message={t('roleRequired')} />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel htmlFor="brandId">{t('brandPage')}</InputLabel>
                <Controller
                  name="brandId"
                  control={control}
                  rules={{ required: true }}
                  render={({ value, onChange }) => (
                    <Select value={value} onChange={onChange} fullWidth={true}>
                      {brandPages.map((brandPage) => (
                        <MenuItem value={brandPage.id}>
                          {brandPage.name}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
                <ErrorMessage
                  errors={errors}
                  name="brandId"
                  render={({ message }) => (
                    <InputError message={t('brandPageRequired')} />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                inputRef={register}
                name='stripeCustomerId'
                label={t('stripeCustomerId')}
                fullWidth={true}
                inputProps={{
                  name: 'stripeCustomerId',
                }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormCheckBox
                control={control}
                name="restrictSeoFields"
                label={t('restrictSeoFields')}
                style={{ marginLeft: '-12px', display: 'flex', alignItems: 'center' }}
                checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormCheckBox
                control={control}
                name="enableCouponLink"
                label={t('enableCouponLink')}
                style={{ marginLeft: '-12px', display: 'flex', alignItems: 'center' }}
                checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
              />
            </Grid>
          </Grid>
        </BoxWrap>
        <Button
          className="submit-button"
          type="submit"
          variant="contained"
          color="primary"
        >
          {isNewUser(id) ? t('create') : t('save')}
        </Button>

        <Button
          onClick={redirectOnSaveOrCancel}
          className={'btn btn-cancel'}
          color="primary"
        >
          {t('cancel')}
        </Button>
        {!isNewUser(id) &&
        (user?.status === 'ACTIVATION_EXPIRED' ||
          user?.status === 'ACTIVATION_EMAIL_SENT') ? (
          <Button
            onClick={resendMail}
            className="submit-button"
            variant="contained"
            color="secondary"
          >
            {t('resetActivationEmail')}
          </Button>
        ) : null}
      </form>

      <Prompt
        when={isDirty && !isFormSubmitted}
        message={t(`areYouSureYouWantToLeave`)}
      />
    </>
  );
};
