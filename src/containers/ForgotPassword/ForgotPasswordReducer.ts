import { RESET_START, RESET_SUCCESS, RESET_ERROR } from './ForgotPasswordTypes';

const INITIAL_STATE = {
  loading: false,
  error: false,
  success: false,
};

export default (state: any = INITIAL_STATE, action: any) => {
  switch (action.type) {
    case RESET_START:
      return { ...state, loading: true, error: null };

    case RESET_SUCCESS:
      return { ...state, loading: false, success: true };

    case RESET_ERROR:
      return { ...state, loading: false, error: action.payload };

    default:
      return state;
  }
};
