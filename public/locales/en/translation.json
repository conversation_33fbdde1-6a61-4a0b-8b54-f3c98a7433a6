{"userManagement": "User Management", "createUser": "Create user", "searchUsers": "Search users", "noResultsFound": "No results found", "name": "Name", "email": "Email", "status": "Status", "role": "Role", "actions": "Actions", "manage": "Manage", "sureDeleteUser": "Are you sure you want to delete the user?", "warningNoUndo": "Warning! there is no undo for this action.", "cancel": "Cancel", "ok": "OK", "successUpdateUser": "Your user has been successfully updated.", "accountVerificationEmailSent": "An account verification email has been sent.", "errorOccured": "error has occured.", "successDeleteUser": "Your user has been successfully deleted", "successDeleteConsumer": "Your consumer has been successfully deleted", "customersManagement": "Customers Management", "exportCustomers": "Export customers", "searchCustomers": "Search customers", "subscriptionStartDate": "Subscription start date", "sureDeleteConsumer": "Are you sure you want to delete the consumer?", "noUndo": "Warning! there is no undo for this action.", "brandName": "Brand Name", "brandWebsite": "BRAND URL", "sureDeleteBrand": "Are you sure you want to delete the brand?", "sureDisableBrand": "Are you sure you want to disable the brand?", "sureEnableBrand": "Are you sure you want to enable the brand?", "successUpdateBrand": "Your brand has been successfully updated.", "successCreatedBrand": "New brand has been successfully created.", "successDeleteBrand": "Your brand has been successfully deleted.", "successEnableBrand": "Your brand has been successfully enabled.", "successDisableBrand": "Your brand has been successfully disabled.", "brandManagement": "Brand Management", "createBrand": "Create brandpage", "searchBrands": "Search brands", "unexpectedError": "An unexpected error occurred", "brandNameRequired": "Brand name is required", "brandNameLength128": "Brand name should be less than 128 characters long", "brandWebsiteLength256": "Brand website link should be less than 256 characters long", "brandWebsiteValidUrl": "Brand website must be a valid URL", "example": "example", "slug": "Slug", "slugNoSpacesOrCharacters": "Slug should not contain spaces or special characters", "slugRequired": "Slug is required.", "slugLessThan64": "Slug should be less than 64 characters long", "slugExists": "Slug already exists", "linkTitle": "DISPLAYED URL", "linkTitleLess256": "Displayed Url should be less than 256 characters long", "metaTitleLess250": "Meta title should be less than 250 characters long", "metaDescription": "Meta Description", "metaDescriptionLess500": "Meta description should be less than 500 characters long", "brandPicture": "HEADER", "brandPictureSecondaryLabel": "(740 X 340PX; MAX. 3MB)", "brandPictureRequired": "Brand picture is required", "brandPictureNameLess221": "Brand picture name should be less than 221 characters long", "browseImage": "Browse Image...", "isPopular": "Is Popular?", "yes": "Yes", "no": "No", "isPopularIsRequired": "Is Popular is required", "brandLogo": "LOGO", "brandLogoSecondaryLabel": "(min. 800PX WIDE)", "brandLogoRequired": "Logo is required", "brandLogoLess221": "Logo name should be less than 221 characters long", "hideBrandForUsers": "Hide brand for users", "hideBrandOptionRequired": "Hide brand option is required", "section": "Section", "title": "Title", "brandTitleRequired": "Brand title is required", "brandTitleLess128": "Brand title should be less than 128 characters long", "media": "Media", "brandMediaValidUrl": "Brand media link must be a valid URL", "brandMediaLess256": "Brand media link should be less than 256 characters long", "description": "Description", "brandDescriptionRequired": "Brand description is required.", "brandDescriptionMinMaxLength": "Brand description should be at least 1800 characters long and less than 10.000 characters long", "showCTABtn": "Show CTA Button", "CTABtnLink": "CTA Button Link", "brandLinkLess256": "Brand link should be less than 256 characters long", "brandLinkValidUrl": "Brand link must be a valid URL", "hideSection": "Hide Section", "brandHeadlineRequired": "Brand headline is required", "brandHeadlineLess256": "Brand headline should be less than 256 characters long", "brandHeadline": "Brand Headline", "brandDescription": "Brand Description", "mediaLinkLess256": "Media link should be less than 256 characters long", "mediaLinkValidUrl": "Media link must be a valid URL", "account": "Account", "home": "Home", "website": "Website", "category": "Category", "interestedInWeVoucherProducts": "Interested in WeVoucher products", "address": "Address", "billingStreet": "Billing Street", "billingPostalCode": "Billing Postal Code", "billingCity": "Billing City", "billingCountry": "Billing Country", "contacts": "Contacts", "salutation": "Salutation", "firstName": "First Name", "lastName": "Last Name", "phone": "Phone", "mobilePhone": "Mobile Phone", "position": "Position", "id": "ID", "products": "Products", "productCode": "Product Code", "startDate": "Start Date", "duration": "Duration", "sureDeleteImage": "Are you sure you want to delete the image?", "save": "Save", "saveReqApproval": "Save and request approval", "editBrand": "Edit Brandpage", "createBrandHeadline": "Create Brand", "brand": "Brand", "validationDate": "Validation Date", "releaseDate": "Release Date", "amountCondition": "Amount Condition", "countDownEnabled": "Countdown Enabled", "all": "All", "rowsPerPage": "Rows per page:", "sureDeleteCoupon": "Are you sure you want to delete this coupon?", "successCouponUpdate": "Your coupon has been successfully updated.", "successCouponCreated": "New coupon has been successfully created.", "successCouponDeleted": "Your coupon has been successfully deleted.", "couponManagement": "Coupon Management", "createCoupon": "Create Coupon", "searchCoupon": "Search Coupon", "editCoupon": "Edit Coupon", "couponStatusActive": "Active", "couponStatusPending": "Pending", "couponStatusExpired": "Expired", "couponStatusSoldOut": "Sold out", "noFileSelected": "No file selected.", "eachCodeNotLongerThan50": "Each code should not be longer than 50 characters", "brandRequired": "Brand is required", "categoryRequired": "Category is required", "dateRequired": "Date is required", "amountConditionRequired": "Amount Condition is required", "amountConditionLess16": "Amount Condition should be less than 16 characters long", "couponRegulations": "Coupon Regulations", "couponRegulationsLimit": "(Max 400 characters)", "brandDescriptionLimit": "(Min 1800 characters and max 10.000 characters)", "brandHeadlineLimit": "(Max 128 characters)", "regulationsRequired": "Regulations is required", "discountType": "Discount Type", "discountTypePercentage": "Discount Type Percentage", "discountTypeAmount": "Discount Type Amount", "discountTypeFree": "Discount Type Free", "discountTypeFreeLimit": "(max 35/8 characters)", "discountTypeRequired": "Discount Type is required", "percentage": "Percentage", "percentageRequired": "Percentage is required", "amount": "Amount", "amountRequired": "Amount is required", "freeDescriptionRequired": "Free Description is required", "freeDescriptionLess35": "Free Description should be less than 35 characters long", "shortDescription": "Short Description", "shortDescriptionLess8": "Short Description should be less than 8 characters long", "codeType": "Code Type", "generic": "Generic", "link": "Link", "unique": "Unique", "stationary": "Stationary (barcode)", "csvUpload": "CSV upload", "barcodeUpload": "Barcode upload", "stationaryLimit": "(max 221 characters)", "codeTypeRequired": "Code Type is required", "couponCode": "Coupon Code", "couponCodeRequired": "Coupon code is required", "couponCodeLess50": "Coupon code should be less than 50 characters long", "buttonTextRequired": "Button text is required", "buttonTextLess50": "Button text should be less than 50 characters long", "buttonDescription": "Button Description", "buttonDescriptionRequired": "Button description is required", "noNegativeNumbersAccepted": "No negative numbers accepted", "availableDiscountCodes": "Available Discount Codes", "insertCouponCodes": "Insert Coupon Codes", "insertCouponCode": "Insert Coupon Code", "fileUploaded": "File Uploaded", "insertCSVWithUniqueCodes": "Insert .csv file with the unique codes", "insertImageWithStationaryCode": "Insert image with the stationary code", "imageNameLess221": "Image name should be less than 221 characters long", "availableForNewCustomers": "Only available for new customers", "showOnHomePage": "Show on home page", "enableVirtualExpiration": "Enable virtual expiration", "homepage": "Homepage", "bannerSlider": "<PERSON>", "couponOfTheWeek": "Coupon of the Week", "popularCoupons": "Popular Coupons", "popularBrand": "Popular Brand", "textBanner": "Text Banner", "testedForYou": "Tested for you", "noAutoSlide": "No auto-slide", "desktopBanner": "Desktop Banner", "mobileBanner": "Mobile Banner", "addBanner": "Add Banner", "addCoupon": "Add Coupon", "addNew": "Add New", "search": "Search", "add": "Add", "update": "Update", "button": "<PERSON><PERSON>", "text": "Text", "image": "Image", "textPreview": "Text Preview", "mode": "Mode", "schmuck": "Sc<PERSON><PERSON>", "enableVirtualExpirationInfo": "<h1>Virtual Expiration Feature</h1><p>The &quot;virtual expiration&quot; feature allows you to control how the expiration date of a voucher appears to the end customer, without changing the actual expiration date. This feature creates urgency by showing a shorter validity period, encouraging the customer to redeem the voucher sooner.</p><h2>How it works:</h2><ul><li>You set the actual expiration date of the voucher as usual (e.g., December 31).</li><li>Once the virtual expiration is enabled, the displayed expiration date for the end customer will be automatically extended at regular intervals based on your chosen rhythm.</li><li>This means that the customer sees a shorter validity period, which generates a sense of urgency.</li><li>However, the voucher remains valid and available until the true expiration date, even though the virtual expiration date is repeatedly updated.</li></ul>", "everyTwoWeeks": "Mid and End of month", "everyTwoWeeksInfo": "The displayed expiration date is extended to the middle and end of each month.", "endOfTheMonth": "End of the month", "endOfTheMonthInfo": "The expiration date is pushed to the end of each month.", "days": "Days", "until": "Until :", "virtualExpirationBetween1to100": "Virtual expiration should be between 1 to 100 days", "everyDaysInfo": "You can specify a custom number of days after which the virtual expiration date is extended.", "couponImage": "COUPON-PICTURE", "couponImageSecondaryLabel": "(800 x 800px)", "couponImageRequired": "Coupon image is required", "couponImageLess221": "Coupon image name should be less than 221 characters long", "uploadImageSecondaryLabel": "(JPG OR PNG; MAX 3MB)", "initialPeriodDuration": "Initial period duration", "initialAmount": "Initial amount", "recurring": "Recurring", "recurringAmount": "Recurring Amount", "currentlySubscribed": "Currently subscribed", "featured": "Featured", "sureDeleteProduct": "Are you sure you want to delete this product?", "successProductDeleted": "Your product has been successfully deleted.", "successProductUpdated": "Your product has been successfully updated.", "successProductCreated": "New product has been successfully created.", "successProductPublished": "Your product has been successfully published.", "successProductCreatedAndPublished": "New product has been successfully created and published.", "successProductUpdatedAndPublished": "New product has been successfully updated and published.", "productManagement": "Product Management", "internalNotes": "Internal notes", "productStatusActive": "Active", "productStatusDeactivated": "Deactivated", "productStatusDraft": "Draft", "createProduct": "Create Product", "searchProduct": "Search Product", "code": "Code", "uploadedOn": "Uploaded on", "numberOfUsagesLeft": "Number of usages left", "successCodesUploaded": "Codes successfully uploaded.", "uploadCodesForProduct": "Upload Codes for product", "uploadPromotionalCodes": "Upload promotional codes", "uploadCodes": "Upload Codes", "uploadSingleMembershipCode": "Upload a single membership code", "subscriptionDiscountCode": "Subscription discount code", "discountCodeRequired": "Discount code is required", "discountCodeLessThan": "Discount code should be less than {{maxLength}} characters long", "numberOfUsages": "Number of usages", "numberOfUsagesRequired": "Number of usages is required", "upload": "Upload", "productCodes": "Product codes", "searchCodes": "Search codes", "sureDeleteCategory": "Are you sure you want to delete the category?", "successCategoryUpdated": "Your category has been successfully updated.", "successCategoryCreated": "New category has been successfully created.", "successCategoryDeleted": "Your category has been successfully deleted.", "categoriesManagement": "Categories Management", "createCategory": "Create Category", "searchCategories": "Search categories", "categoryName": "Category Name", "categoryNameRequired": "Category name is required", "categoryNameLess128": "Category name should be less than 128 characters long", "categorySlug": "Category Slug", "categoryImage": "Category Image", "categorySlugRequired": "Category slug is required", "categorySlugLess128": "Category slug should be less than 128 characters long", "categoryImageRequired": "Category Image is required", "categoryIcon": "Category Icon", "categoryIconRequired": "Category icon is required", "categoryIconNameLess211": "Category icon name should be less than 221 characters long", "enableBanner": "Enable Banner", "bannerText": "Banner Text", "bannerTextRequired": "Banner Text is required", "bannerTextLess200": "Banner Text should be less than 200 characters long", "bannerRedirectUrl": "Banner Redirect URL", "bannerRedirectUrlRequired": "Banner Redirect URL is required", "bannerRedirectUrlLess256": "Banner Redirect URL should be less than 256 characters long", "bannerRedirectValidUrl": "Banner Redirect URL must be a valid URL", "bannerScheduledDate": "Banner Scheduled Date", "bannerScheduledDateRequired": "Banner Scheduled Date is required", "bannerExpiryDateRequired": "Banner Expiry Date is required", "bannerExpiryDate": "Banner Expiry Date", "brandBannerBackgroundColor": "Brand Banner Background Color", "brandBannerTextColor": "Brand Banner Text Color", "brandBannerBackgroundColorRequired": "Brand banner background color is required", "brandBannerTextColorRequired": "Brand banner text color is required", "hexColorEqualTo7": "Hex color should be equal to 7 characters", "editCategory": "Edit Category", "uploadHeaderImages": "Upload Header Images", "successImageUpload": "Image successfully uploaded!", "uploadingImage": "Uploading Image", "errorUploadingImage": "Error uploading image", "headerImages": "Header Images", "globalSetting": "BrandPage Banner", "successGlobalSettingUpdated": "BrandPage Banner Setting has been successfully updated.", "perCategory": "Per Category", "allCategories": "All categories", "exportToCSV": "export to Csv", "perBrandPage": "Per Brand Page", "brandPage": "BrandPage", "addOns": "Add-Ons", "brandPageBanner": "Brandpage Banner", "allBrandPages": "All brand pages", "userActivity": "User Activity", "exportSummarizedReport": "Export summarized report for the activity of all users", "newsletterSubscriptions": "Newsletter Subscriptions", "exportListAllUsersSubscribedToNewsletter": "Export list of all users subscribed to newsletter", "reports": "Reports", "lifetimeFreeMembership": "Lifetime free membership", "lifetimeMembership": "Lifetime membership", "initialPeriodEndsOn": "Initial period ends on", "thenSubscriptionEndsAutomatically": "Then, subscription ends automatically.", "nextPaymentDate": "Next payment date:", "paymentRefunded": "Payment refunded, subscription is temporarily inactive", "pending": "Pending", "suspended": "Suspended", "completed": "Completed", "successConsumerUpdate": "Consumer updated successfully", "successSubscriptionCanceled": "Subscription cancelled successfully", "emails": "Emails", "street": "Street", "houseNumber": "House Number", "postalCode": "Postal Code", "place": "Place", "notes": "Notes", "descriptionLess1000": "Description should be less 1000 characters long", "dateOfBirth": "Date of Birth", "favoriteBrands": "Favorite Brands", "subscribedToNewsletter": "Subscribed to newsletter", "subscriptionDetails": "Subscription details", "noActiveSubscription": "No active subscription", "productId": "Product ID", "productTitle": "Product title", "productSubtitle": "Product subtitle: ", "corporate": "Corporate: ", "subscriptionCode": "Subscription code: ", "subscriptionCancelledOn": "Subscription cancelled on: ", "subscriptionEndsOn": "Subscription ends on: ", "subscriptionExpiredOn": "Subscription expired on: ", "cancelSubscription": "Cancel subscription", "back": "Back", "consumerDetails": "Consumer Details", "dashboard": "Dashboard", "forgotYourPassword": "Forgot your password?", "dontDispairWeveGotYou": "Don't dispair, we've got you", "passwordReset": "Password reset!", "youCaowUseYourNewPassword": "You can now use your new password to log in to your account", "success": "Success", "verificationEmailSent": "Verification email for resetting password is sent.", "validEmailIsRequired": "Valid email is required", "resetPassword": "Reset password", "backToLoginScreen": "Back to login screen", "error": "Error", "tokenExpired": "Token expired!", "tokenInvalid": "Token invalid!", "unknownTokenError": "Unknown token error!", "backToHome": "Back to home", "couponManagementSystem": "Coupon Management System", "loginToYourAccount": "Login to your account", "error404": "Error 404", "pageNotFound": "Page not found", "titleRequired": "Title is required", "titleLess50": "Title should be less than 50 characters long", "subtitle": "Subtitle", "subtitleLess50": "Subtitle should be less than 50 characters long", "label": "Label", "labelLess50": "Label should be less than 50 characters long", "additionalInformation": "Additional information", "additionalInformationLess500": "Additional information should be less than 500 characters long", "descriptionLess500": "Description should be less than 500 characters long", "initialPeriod": "Initial period", "durationMonths": "Duration (Months)", "priceRequired": "Price is required", "hasRecurringPaymentAfterInitialPeriod": "Has recurring payment after initial period", "recurringMode": "Recurring mode", "invoicingIntervalMonths": "Invoicing interval (Months)", "recurringAmountPositive": "The recurring amount must be positive", "specialOffer": "Special Offer", "currentSubscribers": "Current subscribers:", "allTimeSubscribers": "All-time subscribers:", "productImage": "Product Image", "productImageRequired": "Product image is required", "productImageNameLess221": "Product image name should be less than 221 characters long", "saveActivate": "Save & activate", "editProduct": "Edit Product", "operationSuccessful": "Operation successful", "signUpToYourAccount": "Sign up to your account", "changeYourPassword": "Change your password", "firstNameRequired": "First name is required", "firstNameLess64": "First name should be less than 64 characters long", "lastNameRequired": "Last name is required", "lastNameLess64": "Last name should be less than 64 characters long", "emailRequired": "Email is required", "emailNotValid": "Email is not valid", "emailLess100": "Email should be less than 100 characters long", "emailAlreadyExists": "Email already exists", "admin": "Admin", "contentManager": "Content Manager", "roleRequired": "Role is required", "create": "Create", "resetActivationEmail": "Reset Activation Email", "editUser": "Edit User", "password": "Password", "validPasswordIsRequired": "Valid password is required", "login": "<PERSON><PERSON>", "forgotPassword": "Forgot password?", "newPassword": "New password", "confirmPassword": "Confirm password", "passwordsDontMatch": "Passwords don't match", "signUp": "Sign Up", "changePassword": "Change password", "users": "Users", "consumers": "Consumers", "coupons": "Coupons", "categories": "Categories", "passwordCreatedSuccessfully": "Successfully created password.", "passwordChangedSuccessfully": "Successfully changed password.", "customerPaysMonthly": "Then, customer pays {{recurringAmount}} Euro monthly.", "customerPaysMonthlyRecurring": "Then, customer pays {{recurringAmount}} Euro / {{recurringInterval}} months.", "adminPanel": "Admin panel", "ccAdmins": "CC admins", "priceList": "Price list", "customers": "Customers", "brandUsers": "Brand Users", "brandPages": "Brand Pages", "brandAdmin": "Brand admin", "amountCouponRequired": "Amount Coupons is required", "pleaseInsertPositiveNumberOr0": "Please insert positive number or 0", "amountCoupons": "Amount Coupons", "pricePerCouponRequired": "Price  per Coupon is required", "pricePerCoupon": "Price  per Coupon", "amountRefreshesRequired": "Amount Refreshes is required", "amountRefreshes": "Amount Refreshes", "pricePerRefreshRequired": "Price per Refresh is required", "pricePerRefresh": "Price Per Refresh", "freeImageVideo": "Free image video", "freeUnboxingVideo": "Free unboxing video", "pricePerImageVideoRequired": "Price  per Image Video is required", "pricePerUnboxingVideoRequired": "Price per Unboxing Video is required", "pricePerImageVideo": "Price per Image Video", "pricePerUnboxingVideo": "Price per Unboxing Video", "brandUserGreeting": "Hello {{userName}}!", "brandAccountIntro": "Welcome to the traffico app. Here you can manage all your content on <a href='https://captaincoupon.de/' style='color:black'>CaptainCoupon.de</a> Simply open the different pages from the menu on the left.", "helpInfo": "If you have any questions, please contact your personal Customer Success Manager or our project management team", "yourTarif": "Your Tarif", "manageBrandPage": "Manage Brand Page", "manageBrandPageInfo": "Here you can adjust general information about your brand", "manageCoupons": "Manage Coupons ({{usedCoupons}}/{{coupons}})", "manageCouponsInfo": "Here you can create new coupons or edit existing ones", "manageAddOns": "Add-ons", "manageAddOnsInfo": "Unlock additional features to reach more customers", "brandBanner": "Brandpage Banner", "brandBannerInfo": "Promote current promotions with a banner on your brand page", "information": "Information", "informationInfo": "Here you will find all the important information about couponing", "manageTarif": "<PERSON><PERSON>", "manageTarifInfo": "Here you can manage your tariff and add-ons", "comingSoon": "Coming soon", "couponUpdates": "Coupon updates", "imageVideo": "Image video", "unboxingVideo": "Unboxing video", "deactivateBrandPageRecommendation": "Deactivate Brandpage recommendations", "enableCouponCountdown": "Enable Coupon Countdown", "enableCountDown": "Enable Countdown", "reportAnalysis": "Report analysis", "reachBoost": "Reach Boost", "upgrade": "Upgrade", "requestMoreCoupons": "Buy more coupons", "requestMoreCouponUpdates": "Buy more coupon updates", "requestImageVideo": "Buy image video", "available": "Available", "sendRequest": "Send request", "date": "Date", "type": "Type", "resource": "Resource", "historyOrChangelog": "History / Changelog", "requested": "Requested", "received": "Received", "used": "Used", "added": "Added", "coupon": "Coupon", "couponUpdate": "Coupon update", "article": "Article", "price": "Price", "loading": "Loading...", "brandAccountRequestSuccess": "Your request has been received and our administrators will get back to you as soon as possible.", "brandAccountRequestError": "Unfortunately there has been an error regarding your request. Please try again or let us know if the issue persists.", "couponsInfoHtml": "You can manage and publish a limited number of <b>coupons</b> for your brand depending on your brand package. If you need more coupons, you can request them at any time.", "couponUpdatesInfoHtml": "You have a certain amount of <b>coupon updates</b> available for your brand that you can spend for text and image changes on your coupons.<br /><br />Creating a new coupon or updating an existing one uses a single coupon update.", "imageVideoInfoHtml": "Video content is a great way to promote your brand. Unlocking this feature enables you to include a image video on your brand page.", "close": "Close", "isVisibleOnBrandPage": "Is visible on brand page", "approveAndGolive": "Approve and go live", "saveChanges": "Save changes", "rememberMe": "Remember Me", "unsupportedFile": "Unsupported File", "datenschutzbestimmungenYouTube": "YouTube data privacy statement", "datenschutzbestimmungenVimeo": "Vimeo data privacy statement", "vimeoAktivierenModalTitle": "Activate Vimeo", "youtubeAktivierenModalTitle": "Activate YouTube", "vimeoAktivierenModalContent": "You need to accept Vimeo cookies so you can watch this video and similar videos at CaptainCoupon. Find more information in the ", "youtubeAktivierenModalContent": "You need to accept Vimeo cookies so you can watch this video and similar videos at CaptainCoupon. Find more information in the ", "invalidVideoUrl": "Invalid video URL", "metaTitle": "Meta Title", "none": "None", "unregistered": "Unregistered", "brandDescriptionLessThan": "Brand description should be less than {{maxLength}} characters long", "emailSuccessfullySent": "Email successfully sent", "brandPageRequired": "Brand Page is Required", "createBrandUser": "Create Brand User", "brandAdminSuccessfullyUpdated": "The brand admin has been successfully updated.", "brandUserSuccessfullyDeleted": "Brand user has been successfully deleted", "brandUserManagement": "Brand User Management", "searchUsersOrBrands": "Search users or brands", "activeSubscriptionNotFound": "User has no active subscription", "productPackageNotFound": "Product package not found", "consumerNotFound": "Consumer not found", "noRecurringPaymentFound": "No recurring payment found", "unknownError": "Unknown error occurred", "mrGender": "Mr.", "msGender": "Ms.", "diversGender": "Diverse", "noCouponsLeft": "No Coupons left", "pleaseBuyMoreOrUpgrade": "Please buy more or upgrade", "noCouponUpdatesBuyMore": "No Coupons Updates left. Please buy more.", "couponRegulationsLessThan": "Coupon regulations should be less than {{maxLength}} characters long", "areYouSure": "Are you sure?", "couponUpdatesLeft": "coupon updates left", "couponCreationsLeft": "coupons left", "formUpdateSuccess": "Update successful", "formCreateSuccess": "Creation successful", "formEditLink": "Edit", "brandDetails": "Brand details", "bookedPackage": "Booked package", "freeVideo": "Free video", "priceListForThisBrand": "PRICE LIST FOR THIS BRAND", "productCodesSuccessfulyUploaded": "Codes from {{fileName}} successfully uploaded.", "invalidCredentials": "INVALID CREDENTIALS", "internalServerError": "Internal Server Error", "brandSlugAlreadyExists": "Brand slug already exists", "brandNameAlreadyExists": "Brand name already exists", "productPackageAlreadyDeactivated": "productPackageAlreadyDeactivated", "productPackageNotActive": "Position is only available for active product packages", "positionAlreadyTaken": "Position already taken", "invalidRecurringAmount": "Recurring amount cannot be 0 if the product package is recurring", "cannotChangePaymentDetails": "Payment details cannot be changed for active or deactivated product packages", "codeUploadDisabledForDeactivatedProductPackages": "Cannot upload codes for deactivated product packages", "codeAlreadyExists": "Code already exists", "codeCannotBeBlank": "Code cannot be blank", "subscriptionCodeLess45": "Subscription code must be less than 45 characters long", "numberOfUsagesForCodeCannotBeEmpty": "Invalid file format, number of usages must not be empty", "numberOfUsagesForCodeCannotBeText": "Invalid file format, number of usages must not be text", "numberOfUsagesForCodeCannotBeLessThanOne": "Invalid file format, number of usages for code must not be zero or negative number", "codeAlreadyExistsInList": "Code duplicate in the list", "userDisabled": "User is disabled", "loggedInUserNotFound": "Logged in user not found", "userNotFound": "User not found", "verificationTokenNotFound": "Verification token not found", "verificationTokenExpired": "Verification token expired", "brandPackageTemplateNotFound": "Brand package template not found", "categoryNotFound": "Category not found", "primaryEmailForConsumerNotFound": "Primary email for consumer not found", "subscriptionForConsumerNotFound": "Subscription for consumer not found", "activeSubscriptionForConsumerNotFound": "Active subscription for consumer not found", "recurringPaymentForConsumerNotFound": "Recurring payment for consumer not found", "unauthorized": "Unauthorized", "accessDenied": "Access denied", "invalidRequestBody": "Invalid request body", "characterLimitExceeded": "Character limit exceeded", "brandNotFound": "Brand not found", "couponNotFound": "Coupon not found", "discountCodeForCouponNotFound": "Discount code for coupon not found", "discountCodeAlreadyExists": "Discount code already exists", "brandAdminNoCouponUpdates": "Brand admin does not have any coupon updates left", "brandAdminNoCoupons": "Brand admin cannot create coupons anymore", "userRoleNotFound": "User role not found", "brandPackageRequiredForBrand": "Please fill the brand package details for the brand", "brandRequiredForBrandAdmin": "Please select brand while creating brand admin", "brandPageIncomplete": "Brand page cannot be visible because the page data is incomplete", "productPackageAlreadyActive": "Product package already active", "successSaveBrand": "Your brand is saved successfully", "priceListUpdateSuccess": "Price list update success", "productMarkedAsFeaturedSuccess": "Product successfully marked as featured", "productMarkedAsNotFeaturedSuccess": "Product successfully marked as not featured", "releaseDateMustBeBeforeValidationDate": "Release date must be before validation date", "brandPackageForBrandNotFound": "Brand package for brand not found", "brandForUserNotFound": "Brand or user not found", "upgradeRequestNotValid": "Upgrade request not valid", "couponBrandIdInfo": "The coupon will be visible on the brand page for this brand", "couponCategoryIdInfo": "Choose a category for this coupon so consumers can find it more easily", "couponReleaseDateInfo": "Your coupon will not be visible to consumers before this date", "couponAmountConditionInfo": "Enter the minimum order value required for this coupon", "couponValidationDateInfo": "Your coupon is valid until this date", "couponIsVisibleOnBrandPageInfo": "Should your coupon be visible on your brand page?", "couponIsCountDownEnabledInfo": "Should a countdown be shown on the coupon?", "couponRegulationsInfo": "Tell consumers which conditions apply when they want to use the coupon, for example minimum order value. ", "couponDiscountTypeAmountInfo": "Choose this option if your discount should be a fixed EUR value", "couponDiscountTypePercentageInfo": "Choose this option if your discount should be a percentage of the full order value", "couponDiscountTypeFreeInfo": "Choose this option if you want to define individual conditions for this coupon:<br/><ul><li><b>Description:</b> This text is shown as a primary headline in the coupon</li><li><b>Short description:</b> The coupon value to be shown in the top right corner of the coupon image, e.g. <i>25%</i>. If you leave this field empty, the coupon is presented as \"free\" with a gift icon</li></ul>", "couponDiscountCodeTypeGenericInfo": "Choose this option if you want to provide a single code with a limited amount of usages", "couponDiscountCodeTypeUniqueInfo": "Choose this if you want to provide a list of unique codes. Your CaptainCou<PERSON>n admin can tell you more about the required file format.", "couponDiscountCodeTypeStationaryInfo": "Choose this option if you want to upload a barcode as an image. We will embed the barcode in a printable version of the coupon which users can download from your brand page.", "couponAvailableForNewCustomersInfo": "Should your coupon only be available to consumers who use your service for the first time?", "couponImageInfo": "Supported formats: JPEG/PNG <br/><br/> Upload an interesting image to present your coupon", "brandNameInfo": "This is your brand name that will be shown to CaptainCoupon.de visitors.<br/>We recommend to use a short form without legal terms like \"GmbH\".", "brandWebsiteLinkInfo": "This URL is used as a link to your brand website on CaptainCoupon.de.<br/><br/>You may want to contact your website administrators so they can provide a link including a tracking parameter, e.g. <i>https://yourbrand.com?t=CaptainCoupon</i>", "brandSlugInfo": "A <i>slug</i> is a short string that will be attached to a CaptainCoupon.de URL to make it easier to find.<br/><br/>Example: <i>\"yourbrand\"</i> turns into <i>captaincoupon.de/brand/yourbrand</i>.<br/><br/>Please choose a short and concise slug without special characters.", "brandWebsiteLinkTitleInfo": "This is the link text to your brand website that we show on your CaptainCoupon.de brand page. We recommend to use your URL, but without \"https\" and tracking parameters.<br/><br/>Example: <i>www.yourbrand.com</i>", "brandMetaTitleInfo": "A short concise title describing your brand on CaptainCoupon.de. This text is used in a \"title\" meta tag and search engines may display it in their search results. <br></br> VARIABLES: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brandMetaDescriptionInfo": "Two or three sentences about your discount on CaptainCoupon.de, e.g. about the concrete saving for your customers. This text is used in a \"description\" meta tag and search engines may display it in their search results. <br></br> VARIABLES: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brandImageInfo": "Supported formats: JPEG/PNG <br/><br/> An image representing your brand, e.g. a sample product photo.<br/><br/>This image is shown on your  CaptainCoupon.de brand page and should be provided in landscape format.", "brandLogoInfo": "Supported formats: JPEG/PNG <br/><br/> Your brand logo.<br/><br/>This image will be shown on a white a background and should therefore have a white or transparent background itself.", "brandIsPopularInfo": "<i>(Field only for CC admins)</i>Should the brand page be listed in the section \"Beliebte Marken\" on the homepage?", "brandHideInfo": "Should the brand page be hidden for certain users?", "brandS1TitleInfo": "This text is shown as a title next to your unboxing video on your brand page and should only be one or two short sentences.", "brandS1MediaInfo": "The URL of your unboxing video on your brand page.<br/><br/>We currently support Vimeo and YouTube. Let us know if you have further questions - we're happy to help!", "brandS1LinkInfo,": "Users who click \"Find out more\" next to your video are taken to the website specified by this URL.", "brandS1BrandDescriptionInfo": "A description text that is shown below the video title. You can also decide to leave this field empty and only use the title.", "brandS2BrandHeadlineInfo": "This title text is shown on your brand page below the coupons (and below the unboxing video, if any). Ideally it contains your brand name and marketing claim. <br></br> VARIABLES: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brandS2BrandDescriptionInfo": "This description text is shown below the title on your brand page. A few short sentences help users find out about your brand and products and may be used by search engines as part of SEO. <br></br> VARIABLES: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brands3MediaLinkInfo": "The video URL for your image video which will be embedded towards the end of your brand page.<br/><br/>We currently support Vimeo and YouTube. Let us know if you have further questions - we're happy to help!", "blockUserInfo": "When blocked user can log in but won't have access to anything", "brandPricingFieldMustNotBeNegative": "Field must not be negative", "brandPricingFieldRequired": "Required Field", "manageBrandUsers": "Manage brand users", "incompleteBrandsMustBeHidden": "Incomplete brands must be hidden", "brandIncompleteBannerText": "Basic information is incomplete. Please fill all fields so this brand page can be made visible to customers", "brandNotApprovedBannerText": "Change on this page has not yet been approved. You can still edit all content and a traffico admin will be informed to review and publish your changes.", "expertModeText": "Expert mode is available from the subscription plan 'Professional' and after personal training.", "maxLengthErrorMessage": "Brand description should be less than {{maxLength}} characters long", "couponPreview": "Coupon Preview", "here": "Here", "logout": "logout", "brands": "Brands", "about": "About", "impressum": "Impressum", "privacyPolicy": "Privacy policy", "termsAndConditions": "Terms and conditions", "faq": "FAQ", "myList": "My list", "edit": "Edit", "BASIC": "BASIC", "PROFESSIONAL": "PROFESSIONAL", "ENTERPRISE": "ENTERPRISE", "youtubeOrVimeoLink": "YouTube or Vimeo link", "buyUnboxingVideo": "Buy unboxing video", "buyImageVideo": "Buy Image Video", "requestForUnboxingVideo": "Send request for unboxing video?", "requestForImageVideo": "Send request for image video?", "noUnboxingVideo": "Editing of the unboxing video is not allowed", "noImageVideo": "Editing of the image video is not allowed", "recurringIntervalIsRequired": "Recurring interval is required", "unboxingVideoTitleRequired": "Title for unboxing video is required", "unboxingVideoTitleLess128": "Title for unboxing video should be less than 128 characters", "unboxingVideoDescriptionRequired": "Description for unboxing video is required", "********************************": "Description for unboxing video should be less than {{maxLength}} characters long", "unboxingVideoInfoHtml": "Show your products with an exclusive unboxing video for your brand, presented by our brand ambassador <PERSON>.", "subjectUnboxingMailReq": "Change request regarding unboxing video for {{brandName}}", "bodyUnboxingMailReq": "I would like to request the following changes regarding the unboxing video:\n\n(Please insert your required changes here)\n\n\nBest regards,\n\n{{brandAdminName}}", "requestUnboxingChanges": "Request changes", "areYouSureYouWantToLeave": "Are you sure you want to leave?", "loggingOut": "Logging out...", "editSubscription": "Edit subscription", "percentageShouldBeBiggerThan0": "Percentage should be bigger than 0", "amountShouldBeBiggerThan0": "Amount should be bigger than 0", "releaseDateMustNotBeInPast": "Release date must not be in the past", "couponQualityFeedback": "COUPON QUALITY FEEDBACK", "feedbackMax300": "Feedback (max 300 characters)", "feedBackIsRequired": "Feedback text is a required field", "lessThan300": "Please insert less than 300 characters", "showCustomSteps": "Show custom steps", "redemptionInstruction": "Redemption instruction", "step": "Step", "stepRequired": "Step {{step}} is a required field", "stepLessThan": "Step {{step}} should be less than {{maxLength}} characters long", "maxDateMessage": "Date should not be after maximal date", "invalidDate": "Date is not valid", "validationDateMustNotBeInPast": "Validation date must not be in the past", "expiryDateMustBeAfterScheduledDate": "Expiry date must be after scheduled date", "couponStatusDraft": "Draft", "couponWebsite": "Coupon website", "couponWebsiteLength512": "Coupon website link should be less than 512 characters long", "couponWebsiteValidUrl": "Coupon website must be a valid URL", "couponWebsiteLinkInfo": "A URL leading to the website where the consumer can use the coupon code. When the consumer clicks the <i>Copy code</i> button, a new button is revealed which links to the provided URL.", "couponStatusDeactivated": "Deactivated", "reason": "Reason", "percentageShouldBeLowerOrEqualTo": "Percentage should be lower or equal to {{maxValue}}", "couponRegulationsMaxParagraphs": "Please provide no more than 9 paragraphs of text", "lowQuality": "Low quality", "mediumQuality": "Medium quality", "highQuality": "High quality", "notProvidedFeedback": "Captain<PERSON><PERSON><PERSON><PERSON> did not yet provide feedback for this coupon.", "sureCancelSubscription": "Are you sure you want to cancel this consumer's subscription?", "globalReachBoost": "Global Reach Boost", "on": "ON", "off": "OFF", "globalReachBoostActivated": "Global reach boost is activated", "globalReachBoostDeactivated": "Global reach boost is deactivated", "requestReachBoost": "Request Reach Boost", "reachBoostInfoHtml": "A <i>Reach Boost</i> campaign helps you increase your brand visibility by allowing CaptainCoupon.de visitors without an active membership to access coupon codes on your brand page:<br /><br /> <ol><li>You request a campaign for a certain timeframe.</li><li>We provide a special link that allows all visitors to access your coupon codes. </li><li>You can use the link in your online advertising campaigns. Visitors who use the link can then access coupons without an active membership.</li></ol><ul><li>You can request a new timeframe or a pause for the campaign at any time.</li><li>Outside of the campaign timeframe, the link is still functional, but requires an active membership as usual.</li></ul>", "active": "Active", "endDate": "End Date", "requestChanges": "Request changes", "approveChanges": "Approve changes", "startDateMustBeBeforeEndDate": "Start date must be before end date", "currentSettings": "Current settings", "requestedChanges": "Requested changes", "endDateMustNotBeInPast": "End date must not be in the past", "startDateMustNotBeInPast": "Start date must not be in the past", "inactive": "Inactive", "noActiveReachBoostCampaign": "You do not have a running Reach Boost campaign.", "noPendingReachBoostRequest": "There are no pending changes for your Reach Boost campaign.", "reachBoostActiveMsg": "Your campaign is active right now. Use the link above in your advertising campaign in the given timeframe or request changes in your account page.", "reachBoostWrongTimeRangeMsg": "Your campaign is currently not effective due to the provided time range. Please check start and end dates if you want to use the URL above in an advertising campaign.", "reachBoostErrorMsg": "You do not have a running Reach Boost campaign. You can request a new Reach Boost campaign in your account settings at any time.", "copyLink": "Copy Link", "targetUrl": "Target URL", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "successReachBoostRequest": "Thanks for your request. We will get back to you as soon as possible.", "changesRequested": "Changes requested", "editReachBoost": "Edit Reach <PERSON>", "successApprovedReachBoost": "Reach Boost request is approved successfully", "reachBoostApproveRequestNotValid": "Reach Boost approval request is not valid", "noPendingRequestsForApprove": "There are no pending Reach Boost requests for approval", "brandPageHidden": "Reach Boost cannot be requested for hidden brand page", "paywallDisabled": "Paywall disabled", "disablePaywall": "Disable paywall", "isBlocked": "Access blocked", "isVisible": "BrandPage Visibility", "loginToTraffico": "Log in to traffico now", "notYetACustomer": "Not yet a traffico customer?", "testNow": "Test now", "useCustomTheme": "Use custom theme", "homePageTitle": "Homepage title", "homePageBodyText": "Homepage body text", "homePageBodyTextRequired": "Homepage body text is required", "homePageTitleRequired": "Homepage title is required", "customization": "Customization", "backgroundColorRequired": "Main background color is required", "useCustomColours": "Use custom colours", "mainBackgroundColor": "Main background color", "homePageTitleLessThan": "Homepage title should be less than {{maxLength}} characters long", "homePageBodyTextLessThan": "Homepage body text should be less than {{maxLength}} characters long", "white": "White", "black": "Black", "textLogoButtonsColor": "Text, logo and buttons on colored backgrounds", "preview": "Preview", "brandVisibility": "Visible on brands overview", "bannerEnabled": "Banner enabled", "blockUser": "Block User Access", "blockedMessage": "Your access is blocked. Please contact our Customer Success Team.", "stripeBlockedMessage": "You do not have access to the online plan management. Please contact our Customer Success Team.", "noActiveSubscriptionMessage": "You don‘t have an active traffico subscription?", "notLiveMessage": "Your brand page is not live. Please fill out all fields and upload your content. A traffico admin will then review your brand page and set it live. If you have any questions or need assistance, please write us a message here in the chat. We are happy to help you!", "stripeCustomerId": "Stripe Customer ID", "restrictSeoFields": "Restrict SEO fields", "enableCouponLink": "Enable coupon link", "editMetaFields": "<PERSON>", "disableNewsletter": "Disable newsletter", "restricted": "Restricted", "expertMode": "Only available in expert mode", "empty": "  aaa", "buttonText": "Button text", "activate": "activate", "buttonLinkDescription": "This voucher can only be redeemed via this link. Click on 'activate' to use the voucher directly."}