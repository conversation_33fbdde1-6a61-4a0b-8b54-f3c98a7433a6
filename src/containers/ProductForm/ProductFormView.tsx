import React, { useEffect } from 'react';
import { useParams } from 'react-router';
import { Redirect } from 'react-router-dom';
import { useSelector } from 'react-redux';

import { AdminForm } from './Forms';
import { PageTitle, Loading } from 'components';
import * as productActions from './ProductFormActions';
import { useTranslation } from 'react-i18next';

import './ProductForm.scss';

const isIdValid = (id) => id !== 'create';

export const ProductFormView = ({ loading }) => {
  const { t }: { t: any } = useTranslation();
  const { id } = useParams();

  const activeProduct = useSelector(
    (state: any) => state.productsForm.activeProduct
  );
  const productError = useSelector((state) => state.productsForm.error);

  useEffect(() => {
    if (isIdValid(id)) {
      productActions.getProduct(id);
    }
  }, [id]);

  return (
    <div className="coupon-create-page">
      <PageTitle
        title={isIdValid(id) ? t('editProduct') : t('createProduct')}
      />
      {loading && <Loading />}
      {productError && productError.errorCode === 'productPackageNotFound' ? (
        <Redirect to="/404" />
      ) : (
        (activeProduct || !isIdValid(id)) && (
          <AdminForm product={activeProduct} />
        )
      )}
    </div>
  );
};
