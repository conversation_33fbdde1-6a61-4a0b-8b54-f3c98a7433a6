import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { ProductView } from './ProductView';
import * as productActions from './ProductActions';
import { ProductTable } from './components/ProductTable';
import { Loading } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { useTranslation } from 'react-i18next';

const Product = () => {
  const dispatch = useDispatch();
  const products = useSelector((state: any) => state.products);
  const productsForm = useSelector((state: any) => state.productsForm);
  const productsError = useSelector((state: any) => state.products.error);
  const productsFormError = useSelector(
    (state: any) => state.productsForm.error
  );

  const { featuredSuccess, notFeaturedSuccess, deleteSuccess } = products;
  const {
    editSuccess,
    createSuccess,
    activateProductSuccess,
    editAndActivateProductSuccess,
    createAndActivateProductSuccess,
  } = productsForm;

  const { t }: { t: any } = useTranslation();
  const { addMessage } = useAPIError();

  const [filter, setFilter] = useState({
    pageSize: products?.filter?.pageSize || 10,
    page: products?.filter?.page || 0,
    sort: products?.filter?.sort || {
      field: 'position',
      dir: 'ASC',
    },
    search: '',
  });

  const onFilterChange = (data) => {
    const filterUpdate = { ...filter, ...data };
    setFilter(filterUpdate);
    dispatch(productActions.getProducts(filterUpdate));
    dispatch(productActions.changeFilter(filterUpdate));
  };

  const onDeleteProduct = (product) => {
    dispatch(productActions.deleteProduct(product));
  };

  const onSetProductFeatured = async (data) => {
    dispatch(productActions.setProductFeatured({ ...data }));
  };

  const onMarkProduct = async (id) => {
    dispatch(productActions.markProductFeatured(id));
  };

  useEffect(() => {
    if (deleteSuccess) {
      addMessage(t('successProductDeleted'), 'warning');
    }
    if (productsError) {
      addMessage(t(productsError.errorCode), 'error');
    }
    if (featuredSuccess) {
      addMessage(t('productMarkedAsFeaturedSuccess'), 'success');
    }
    if (notFeaturedSuccess) {
      addMessage(t('productMarkedAsNotFeaturedSuccess'), 'success');
    }
    if (editSuccess) {
      addMessage(t('successProductUpdated'), 'success');
    }
    if (createSuccess) {
      addMessage(t('successProductCreated'), 'success');
    }
    if (activateProductSuccess) {
      addMessage(t('successProductPublished'), 'success');
    }
    if (createAndActivateProductSuccess) {
      addMessage(t('successProductCreatedAndPublished'), 'success');
    }
    if (editAndActivateProductSuccess) {
      addMessage(t('successProductUpdatedAndPublished'), 'success');
    }
    if (productsFormError) {
      addMessage(t(productsFormError.errorCode || 'errorOccured'), 'error');
    }
    dispatch(productActions.clearProduct());
    dispatch(productActions.getProducts(filter));
  }, [
    dispatch,
    deleteSuccess,
    productsError,
    featuredSuccess,
    notFeaturedSuccess,
    productsFormError,
  ]);

  return (
    <ProductView onFilterChange={onFilterChange} filter={filter}>
      {products.data && (
        <ProductTable
          products={products.data}
          count={products.count}
          onFilterChange={onFilterChange}
          filter={filter}
          onDeleteProduct={onDeleteProduct}
          onMarkProduct={onMarkProduct}
          onSetProductFeatured={onSetProductFeatured}
        >
          {products.loading && <Loading />}
        </ProductTable>
      )}
    </ProductView>
  );
};

export default Product;
