import React, { useEffect, useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { NavLink, useHistory, Link } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  Radio,
} from '@material-ui/core';
import CheckCircleRoundedIcon from '@material-ui/icons/CheckCircleRounded';
import CheckBoxRoundedIcon from '@material-ui/icons/CheckBoxRounded';

import { CardHorizontal } from 'components/CardHorizontal';
import { ErrorMessage } from '@hookform/error-message';

import DefaultLogo from '../../../assets/images/default_logo.png';

import {
  BoxWrap,
  InputError,
  ConfirmSaveCouponDialog,
  CustomDialog,
  InfoButton,
} from 'components';
import {
  CurrencyTextInput,
  RichTextInput,
  RadioButtonGroup,
  SelectMenu,
  UploadFormField,
  DatePickerInput,
  FormTextField,
  FormCheckBox,
} from 'components/FormInputs';
import CouponQualityFeedbackBox from '../components/CouponQualityFeedbackBox';

import * as couponActions from '../CouponFormActions';
import { getBrandPagesMap } from '../../BrandPages/BrandPagesActions';
import { getCategoriesMap } from '../../Categories/CategoriesActions';
import {
  getBrandPage,
  clearBrandPage,
} from '../../BrandPageForm/BrandPageFormActions';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import 'react-quill/dist/quill.snow.css';

import { getBrandPageAsBrandAdmin } from 'repository/brand-page';
import * as couponService from 'repository/coupon';
import * as RoleManager from 'services/role';
import { validateUrl } from 'services/validation';
import * as CONSTANTS from 'utils/constants';
import {
  isRichTextEmpty,
  isIdValid,
  parseUniqueCodes,
  formatDateToISOString,
  parseDate,
} from 'utils/helpers';

import './AdminForm.scss';

type FormValues = {
  id: string;
  brandId: string;
  categoryId: string;
  validationDate: string;
  releaseDate: string;
  amountCondition: string;
  couponRegulations: string;
  discountType: string;
  discountValue: number;
  percentDiscountValue: number;
  amountDiscountValue: number;
  freeDescription: string;
  shortDescription: string;
  shortCouponDescription: string;
  discountCodeType: string;
  couponCode: string;
  maxAvailableNumber: number;
  uniqueCodes: any;
  isForNewCustomers: string;
  isVisible: boolean;
  isCountDownEnabled: boolean;
  showOnHomePage: boolean;
  showOnHomePageUntilDate: string;
  image: string;
  qualityFeedbackText: string;
  couponQuality: any;
  couponLink: string;
  softRenewalPeriod: string;
  enableSoftRenewal: boolean;
  softRenewalNumberOfDays: number;
  buttonText: string;
  linkDescription: string;
};

const gridStyle = { marginTop: '30px', marginBottom: '10px' };
const discountTypeField = { marginTop: '2px', marginBottom: '15px' };

const getSelectOptions = (isUserBrandAdmin, brandPageData, brandPagesData) => {
  if (isUserBrandAdmin) {
    return [brandPageData, ...(brandPagesData || [])];
  } else {
    return brandPagesData;
  }
};

function getSoftRenewalDate(softRenewalPeriodWatcher, softRenewalNumberOfDaysWatcher) {
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();

  switch (softRenewalPeriodWatcher) {
    case CONSTANTS.softRenewalStatus.EVERY_TWO_WEEKS:
      const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      const halfMonthDay = Math.ceil(daysInMonth / 2);
      return today.getDate() < halfMonthDay
        ? new Date(currentYear, currentMonth, halfMonthDay)
        : new Date(currentYear, currentMonth, daysInMonth);

    case CONSTANTS.softRenewalStatus.END_OF_MONTH:
      return new Date(currentYear, currentMonth + 1, 0); // Last day of the current month

    case CONSTANTS.softRenewalStatus.EVERY_X_DAYS:
      return new Date(today.getTime() + softRenewalNumberOfDaysWatcher * 24 * 60 * 60 * 1000);

    default:
      throw new Error('Invalid soft renewal period');
  }
}


export const AdminForm = ({ coupon, couponInfoTexts, brandPackage }) => {
  const { t }: { t: any } = useTranslation();

  const couponQualityDefaultValue = {
    title: 'NONE',
    label: t('none'),
    color: 'gray',
    text: t('notProvidedFeedback'),
  };

  const today = new Date();
  let tomorrow = new Date();
  tomorrow.setDate(today.getDate() + 1);
  const [defaultValues] = useState(() =>
    coupon
      ? {
          ...coupon,
          couponCode: coupon?.codes?.length ? coupon.codes[0].code : '',
          percentDiscountValue:
            coupon.discountType === CONSTANTS.PERCENTAGE
              ? coupon.discountValue
              : '',
          amountDiscountValue:
            coupon.discountType === CONSTANTS.AMOUNT
              ? coupon.discountValue
              : '',
          freeDescription:
            coupon.discountType === CONSTANTS.FREE
              ? coupon.freeDescription
              : '',
          shortDescription:
            coupon.discountType === CONSTANTS.FREE
              ? coupon.shortDescription
              : '',
          shortCouponDescription: coupon.shortCouponDescription || '',
          isForNewCustomers: coupon.isForNewCustomers
            ? coupon.isForNewCustomers.toString()
            : 'false',
          isVisible: coupon.isVisible,
          isCountDownEnabled: coupon.isCountDownEnabled,
          showOnHomePage: coupon.showOnHomePage || false,
          maxAvailableNumber:
            coupon?.codes?.length &&
            coupon.codes[0].maxAvailableNumber !== 2147483647
              ? coupon.codes[0].maxAvailableNumber
              : '',
          discountCodeType: [
            CONSTANTS.STATIONARY,
            CONSTANTS.UNIQUE,
            CONSTANTS.GENERIC,
            CONSTANTS.LINK
          ].includes(coupon.discountCodeType)
            ? coupon.discountCodeType
            : '',
          discountType: [
            CONSTANTS.PERCENTAGE,
            CONSTANTS.AMOUNT,
            CONSTANTS.FREE,
          ].includes(coupon.discountType)
            ? coupon.discountType
            : '',
          enableSoftRenewal: coupon.enableSoftRenewal,
          softRenewalPeriod: coupon.softRenewalPeriod,
          showOnHomePageUntilDate: coupon.showOnHomePageUntilDate
            ? parseDate(coupon.showOnHomePageUntilDate)
            : null,
          validationDate: parseDate(coupon.validationDate),
          releaseDate: coupon.releaseDate
            ? parseDate(coupon.releaseDate)
            : today,
          couponQuality: coupon.couponQuality || couponQualityDefaultValue,
          couponLink: coupon.couponLink,
          buttonText: coupon?.codes?.length ? coupon.codes[0]?.buttonText : '',
          linkDescription: coupon?.codes?.length ? coupon.codes[0]?.linkDescription : '',
        }
      : {
          discountType: '',
          couponLink: '',
          discountCodeType: '',
          freeDescription: '',
          shortDescription: '',
          shortCouponDescription: '',
          isForNewCustomers: 'false',
          isVisible: true,
          isCountDownEnabled: false,
          showOnHomePage: false,
          showOnHomePageUntilDate: null,
          couponRegulations: '<p><br></p>',
          validationDate: tomorrow,
          releaseDate: today,
          brandId: '',
          couponQuality: couponQualityDefaultValue,
          enableSoftRenewal: false,
          buttonText: '',
          linkDescription: '',
        }
  );

  const formValues = useForm<FormValues>({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: defaultValues,
  });

  const {
    handleSubmit,
    setValue,
    watch,
    formState: { dirtyFields },
    control,
    errors,
    trigger,
  } = formValues;

  formValues.register('couponQuality');

  const discountCodeTypeWatcher = watch('discountCodeType');
  const discountTypeWatcher = watch('discountType');
  const showOnHomePageWatcher = watch('showOnHomePage');
  const enableSoftRenewalWatcher = watch('enableSoftRenewal');
  const validationDateWatcher = watch('validationDate');
  const selectedBrandId = watch('brandId');
  const isVisibleWatcher = watch('isVisible');
  const isCountDownEnabledWatcher = watch('isCountDownEnabled');
  const softRenewalPeriodWatcher = watch('softRenewalPeriod');
  const softRenewalNumberOfDaysWatcher = watch('softRenewalNumberOfDays');

  const dispatch = useDispatch();
  const history = useHistory();

  const categories = useSelector((state: any) => state.categories);
  const selectedBrand = useSelector(
    (state: any) => state.brandPageForm.activeBrandPage
  );
  const couponsForm = useSelector((state: any) => state.couponsForm);
  const brandPages = useSelector((state: any) => state.brandPages);
  const mapBrandPagesError = useSelector(
    (state: any) => state.brandPages.errorMap
  );
  const userProfile = useSelector((state: any) => state.user?.userProfile);
  const enableCouponLink = !!userProfile?.enableCouponLink || RoleManager.isAbleTo('coupon', 'enableCouponLink')

  let { id } = useParams();
  const { addMessage } = useAPIError();

  const NONE_SELECTED = t('noFileSelected');

  const parseFileName = (path) => {
    if (!path) return NONE_SELECTED;
    return path.split('/').slice(-1)[0].split('-').slice(2).join('');
  };

  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const [brandLogo, setBrandLogo] = useState();
  const [imageFile, setImageFile] = useState<any>();
  const [isBrandCountDownEnabled, setIsBrandCountDownEnabled] = useState(false);
  const [isVirtualExpirationEnabled, setIsVirtualExpirationEnabled] = useState(false);
  const [imageFileName, setImageFileName] = useState(
    coupon?.image ? parseFileName(coupon.image) : NONE_SELECTED
  );

  const [isStatCodeUpdated, setIsStatCodeUpdated] = useState(false);
  const [stationaryCodeFile, setStationaryCodeFile] = useState<any>();
  const [stationaryCodeFileName, setStationaryCodeFileName] = useState(
    coupon?.codes ? parseFileName(coupon?.codes[0].barcodeImage) : NONE_SELECTED
  );

  const [uniqueCodes, setUniqueCodes]: any = useState([]);
  const [areUniqueCodesUploaded, setAreUniqueCodesUploaded] = useState(false);
  const [uniqueCodesError, setUniqueCodesError] = useState<string>('');

  const [typeForDelete, setTypeForDelete] = useState('');
  const [richTextContents, setRichTextContents]: any = useState('');
  const [delConfOpen, setDelConfOpen] = useState(false);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [submitData, setSubmitData] = useState(defaultValues);

  const [hasUpdateCreditsLeft, setHasUpdateCreditsLeft] = useState(true);
  const [brandPageData, setBrandPageData] = useState({ id: '', name: '' });

  // const shouldShowCouponFeedbackBox =
  //   (RoleManager.isAbleTo('coupon', 'add_feedback') ||
  //     RoleManager.isAbleTo('brand_coupon', 'view_feedback')) &&
  //   isIdValid(id);

  const shouldShowCouponFeedbackBox = false;

  const checkCouponUpdateCreditsLeft = async () => {
    try {
      const response = await couponService.getCouponUpdateCreditsLeft();

      if (response.data !== 'PROCEED') {
        setHasUpdateCreditsLeft(false);
      }
    } catch (error) {
      setHasUpdateCreditsLeft(false);
    }
  };

  const getBrandData = async () => {
    try {
      const response = await getBrandPageAsBrandAdmin();
      const {
        data: { id, name, logo, isCountDownEnabled, enableSoftRenewal },
      } = response;

      setBrandLogo(logo);
      setIsBrandCountDownEnabled(isCountDownEnabled);
      setIsVirtualExpirationEnabled(enableSoftRenewal);
      setValue('brandId', id);
      setBrandPageData({ id, name });
    } catch (error) {
      addMessage(
        t(error?.response?.data?.errorCode || 'errorOccured'),
        'error'
      );
    }
  };

  useEffect(() => {
    if (RoleManager.isAbleTo('brand_coupon', 'create')) {
      getBrandData();
      if (isIdValid(id)) {
        checkCouponUpdateCreditsLeft();
      }
    }
  }, []);

  const imageName = isImageUpdated
    ? imageFileName
    : coupon?.image || '/coupon_image_placeholder.png';
  const imageUrl = process.env.REACT_APP_SHARED_MODULE_IMAGE_PATH + imageName;

  const discountType = watch('discountType');
  let discountValue;
  switch (discountType) {
    case 'AMOUNT':
      discountValue = watch('amountDiscountValue');
      break;
    case 'PERCENTAGE':
      discountValue = watch('percentDiscountValue');
      break;
    case 'FREE':
      discountValue = watch('freeDescription');
      break;
  }

  const couponForCardHorizontal = {
    ...defaultValues,
    ...watch(),
    validationDate:
      new Date(validationDateWatcher).toString() !== 'Invalid Date'
        ? new Date(validationDateWatcher)
        : defaultValues?.validationDate || tomorrow,
    image: imageUrl,
    discountValue: discountValue,
    isForNewCustomers: watch('isForNewCustomers') === 'true',
    brandLogo: selectedBrand?.logo
      ? process.env.REACT_APP_SHARED_MODULE_IMAGE_PATH + selectedBrand.logo
      : brandLogo
      ? process.env.REACT_APP_SHARED_MODULE_IMAGE_PATH + brandLogo
      : DefaultLogo,

    // remaining time is difference between validationTime and current Time in seconds
    remainingTime: Math.floor(
      (new Date(validationDateWatcher).getTime() - new Date().getTime()) / 1000
    )
  };

  if(enableSoftRenewalWatcher) {
    if(softRenewalPeriodWatcher === coupon?.softRenewalPeriod && softRenewalNumberOfDaysWatcher === coupon?.softRenewalNumberOfDays && coupon?.softRenewalDate) {
      couponForCardHorizontal.validationDate = new Date(coupon.softRenewalDate);
    } else {
      if(!softRenewalPeriodWatcher || !softRenewalNumberOfDaysWatcher) {
        couponForCardHorizontal.validationDate = new Date(validationDateWatcher);
      } else {
        couponForCardHorizontal.validationDate = getSoftRenewalDate(softRenewalPeriodWatcher, softRenewalNumberOfDaysWatcher);
      }
    }
    couponForCardHorizontal.remainingTime = Math.floor(
      (couponForCardHorizontal.validationDate.getTime() - new Date().getTime()) / 1000
    )
  }

  useEffect(() => {
    if (RoleManager.isAbleTo('brand_pages', 'view')) {
      dispatch(getBrandPagesMap());
    }
    dispatch(getCategoriesMap());
  }, [dispatch]);

  useEffect(() => {
    if (
      RoleManager.isAbleTo('brand_pages', 'view') &&
      !brandPages?.map?.length &&
      !brandPages?.loadingMap
    ) {
      dispatch(getBrandPagesMap());
    }
  }, [dispatch, brandPages]);

  useEffect(() => {
    if (!categories?.map?.length && !categories?.loadingMap) {
      dispatch(getCategoriesMap());
    }
  }, [dispatch, categories]);

  useEffect(() => {
    if (couponsForm.error?.errorCode === 'characterLimitExceeded') {
      setUniqueCodesError(t('eachCodeNotLongerThan50'));
    } else if (couponsForm.error) {
      addMessage(t(couponsForm.error.errorCode || 'unexpectedError'), 'error');
    } else if (mapBrandPagesError) {
      addMessage(t(mapBrandPagesError.errorCode || 'unexpectedError'), 'error');
    } else if (
      couponsForm.success ||
      couponsForm.editSuccess ||
      couponsForm.createSuccess
    ) {
      history.push(redirectRoute);
    }
  }, [couponsForm, mapBrandPagesError, addMessage]);

  const toggleDeleteConfirmationPopup = () => {
    setDelConfOpen((delConfOpen) => !delConfOpen);
  };

  useEffect(() => {
    if (selectedBrandId && RoleManager.isAbleTo('brand_pages', 'view')) {
      dispatch(getBrandPage(parseInt(selectedBrandId), true));
    }
  }, [dispatch, selectedBrandId]);

  const onMediaDelete = () => {
    if (typeForDelete === 'image') {
      setIsImageUpdated(true);
      setImageFile(null);
      setImageFileName(NONE_SELECTED);
    } else if (typeForDelete === 'statCode') {
      setIsStatCodeUpdated(true);
      setStationaryCodeFile(null);
      setStationaryCodeFileName(NONE_SELECTED);
    }
  };

  const onSaveClick = (data) => {
    setSubmitData(data);
    setSaveDialogOpen(true);
  };

  const releaseDateValidator = (releaseDate) => {
    const dayInMs = 86400000;
    const releaseOn = new Date(releaseDate).getTime() + dayInMs;
    const validateOn = new Date(validationDateWatcher).getTime() + dayInMs;
    const today = new Date().getTime();
    if (releaseDate.toString() === 'Invalid Date') {
      return t('invalidDate');
    } else if (
      releaseOn < today &&
      !(
        isIdValid(id) &&
        parseDate(coupon.releaseDate) === parseDate(releaseDate)
      )
    ) {
      return t('releaseDateMustNotBeInPast');
    } else if (releaseOn >= validateOn) {
      return t('releaseDateMustBeBeforeValidationDate');
    } else {
      return true;
    }
  };

  const validationDateValidator = () => {
    const dayInMs = 86400000;
    const validateOn = new Date(validationDateWatcher).getTime() + dayInMs;
    const today = new Date().getTime();
    if (validationDateWatcher.toString() === 'Invalid Date') {
      return t('invalidDate');
    } else if (
      validateOn < today &&
      ![
        CONSTANTS.couponStatus.EXPIRED,
        CONSTANTS.couponStatus.SOLD_OUT,
      ].includes(coupon?.status)
    ) {
      return t('validationDateMustNotBeInPast');
    } else {
      return true;
    }
  };

  const redirectRoute = RoleManager.isAbleTo('brand_coupon', 'view')
    ? '/dashboard/brand-coupons'
    : '/dashboard/coupons';

  const showSaveButton = () => {
    if (
      !isIdValid(id) ||
      hasUpdateCreditsLeft ||
      coupon.status === CONSTANTS.couponStatus.DRAFT
    ) {
      return true;
    }
    const changedFields = Object.keys(dirtyFields);
    if (changedFields.length === 1 && changedFields.includes('isVisible')) {
      return true;
    }
    return false;
  };

  const submit = (data, imageLocation, statCodeLocation) => {
    if (isImageUpdated) {
      dispatch(couponActions.deleteMedia(coupon?.image));
    }

    if (isStatCodeUpdated) {
      dispatch(couponActions.deleteMedia(coupon?.codes[0].barcodeImage));
    }

    const payload: any = {
      brandId: data.brandId,
      categoryId: data.categoryId,
      couponLink: data.couponLink,
      validationDate: formatDateToISOString(data.validationDate),
      releaseDate: formatDateToISOString(data.releaseDate, 'T00:00:00.000Z'),
      amountCondition: data.amountCondition,
      couponRegulations: data.couponRegulations,
      discountType: data.discountType,
      discountValue: 0,
      freeDescription: null,
      shortDescription: null,
      shortCouponDescription: data.shortCouponDescription,
      discountCodeType: data.discountCodeType,
      enableSoftRenewal: data.enableSoftRenewal,
      softRenewalPeriod: data.softRenewalPeriod,
      softRenewalNumberOfDays: data.softRenewalNumberOfDays,
      codes: [],
      isVisible: data.isVisible,
      isCountDownEnabled: data.isCountDownEnabled,
      isForNewCustomers: data.isForNewCustomers === 'true',
      image: imageLocation,
      showOnHomePage: data.showOnHomePage,
      showOnHomePageUntilDate:
      data.showOnHomePage && data.showOnHomePageUntilDate
        ? formatDateToISOString(data.showOnHomePageUntilDate)
        : null,
      couponQuality: data.couponQuality,
    };

    if (data.discountType === CONSTANTS.PERCENTAGE) {
      payload.discountValue = Number.parseFloat(data.percentDiscountValue);
    } else if (data.discountType === CONSTANTS.AMOUNT) {
      payload.discountValue = Number.parseFloat(data.amountDiscountValue);
    } else if (data.discountType === CONSTANTS.FREE) {
      payload.freeDescription = data.freeDescription;
      payload.shortDescription = data.shortDescription || null;
    }
    switch (data.discountCodeType) {
      case CONSTANTS.GENERIC:
        payload.codes.push({
          type: data.discountCodeType,
          code: data.couponCode,
          maxAvailableNumber: Number.parseInt(data.maxAvailableNumber),
        });
        break;
      case CONSTANTS.UNIQUE:
        payload.codes = uniqueCodes.map((code) => ({
          code: code,
          type: CONSTANTS.UNIQUE,
        }));
        break;
      case CONSTANTS.STATIONARY:
        payload.codes.push({
          type: data.discountCodeType,
          barcodeImage: statCodeLocation,
        });
        break;
      case CONSTANTS.LINK:
        payload.codes.push({
          type: data.discountCodeType,
          buttonText: data.buttonText,
          linkDescription: data.linkDescription,
        });
        break;
    }

    if (data.couponQuality.title === 'NONE') {
      payload.couponQuality = {
        title: data.couponQuality.title,
      };
    } else {
      payload.couponQuality = {
        title: data.couponQuality.title,
        text: data.couponQuality.text,
        color: feedbackColor,
      };
    }
    if (isIdValid(id)) {
      dispatch(couponActions.editCoupon(id, payload));
    } else {
      dispatch(couponActions.createCoupon(payload));
    }
  };

  const couponCodeValidate = useCallback(
    function (couponCode) {
      return discountCodeTypeWatcher !== CONSTANTS.GENERIC || !!couponCode;
    },
    [discountCodeTypeWatcher]
  );

  const buttonTextValidate = useCallback(
    function (buttonText) {
      return discountCodeTypeWatcher !== CONSTANTS.LINK || !!buttonText
    },
    [discountCodeTypeWatcher]
  );

  const buttonDescriptionValidate = useCallback(
    function (buttonDescription) {
      return discountCodeTypeWatcher !== CONSTANTS.LINK || !!buttonDescription
    },
    [discountCodeTypeWatcher]
  );

  const uploadFile = async (file, type) => {
    if (!file.length) {
      return;
    }
    if (imageFile) {
      dispatch(couponActions.deleteMedia('/uploads/' + imageFileName));
    }

    if (type === 'couponImg') {
      await handleUpload(file);
    } else if (type === 'stationary') {
      setIsStatCodeUpdated(true);
      setStationaryCodeFile(file[0]);
      setStationaryCodeFileName(file[0].name);
    }
  };

  const handleUpload = async (temporaryImageFile: any = null) => {
    const data = submitData;
    let imageLocation = coupon?.image;
    let statCodeLocation = coupon?.codes ? coupon?.codes[0].barcodeImage : '';

    if (temporaryImageFile) {
      try {
        const formData = new FormData();
        formData.append('file', temporaryImageFile[0]);
        const response = await couponService.uploadFile(formData, coupon?.id);
        imageLocation = response.data.fileName;
        setIsImageUpdated(true);
        setImageFile(temporaryImageFile[0]);
        setImageFileName(imageLocation);
      } catch {
        addMessage(t('errorOccured'), 'error');
        return;
      }
    } else {
      if (!isIdValid(id) || imageFile) {
        try {
          const formData = new FormData();
          formData.append('file', imageFile);
          const response = await couponService.uploadFile(formData, coupon?.id);
          imageLocation = response.data.fileName;
        } catch {
          addMessage(t('errorOccured'), 'error');
          return;
        }
      }

      if (
        (!isIdValid(id) || stationaryCodeFile) &&
        discountCodeTypeWatcher === CONSTANTS.STATIONARY
      ) {
        try {
          const formData = new FormData();
          formData.append('file', stationaryCodeFile);
          const response = await couponService.uploadFile(formData, coupon?.id);
          statCodeLocation = response.data.fileName;
        } catch {
          addMessage(t('errorOccured'), 'error');
          return;
        }
      }
      submit(data, imageLocation, statCodeLocation);
    }
  };

  const couponQuality = watch('couponQuality');

  const qualityFeedbackOptions = [
    {
      title: 'NONE',
      label: t('none'),
      id: '1',
      color: 'gray',
    },
    { title: 'LOW', label: t('lowQuality'), id: '2', color: 'red' },
    { title: 'MEDIUM', label: t('mediumQuality'), id: '3', color: 'yellow' },
    { title: 'HIGH', label: t('highQuality'), id: '4', color: 'green' },
  ];

  const [feedbackColor, setFeedbackColor] = useState(couponQuality.color);

  useEffect(() => {
    setFeedbackColor(couponQuality.color);
    if (couponQuality?.title) {
      let selectedFeedbackOption = qualityFeedbackOptions.find(
        (feedbackOption) => feedbackOption.title == couponQuality.title
      );
      setFeedbackColor(selectedFeedbackOption?.color);
    }
  }, [couponQuality]);

  useEffect(() => {
    return () => {
      dispatch(clearBrandPage());
    };
  }, []);

  return (
    <>
      {isIdValid(id) && !hasUpdateCreditsLeft && (
        <div className="no-updates-banner">
          <h3>{t('noCouponUpdatesBuyMore')}</h3>
          <Button
            className="clickhere-button"
            variant="contained"
            component={Link}
            to={'/dashboard/brand-account'}
          >
            {t('here')}
          </Button>
        </div>
      )}

      <form onSubmit={handleSubmit(onSaveClick)}>
        <BoxWrap>
          <Grid container spacing={7}>
            <Grid item sm={6} xs={12}>
              <FormLabel>
                  {t('brand')}
                  <InfoButton
                    className="float-right"
                    onClick={couponInfoTexts['couponBrandIdInfo']}
                  />
              </FormLabel>
              <SelectMenu
                  name="brandId"
                  control={control}
                  rules={{ required: true }}
                  selectOptions={getSelectOptions(
                    RoleManager.isAbleTo('brand_coupon', 'edit'),
                    brandPageData,
                    brandPages?.map
                  )}
                  variant="outlined"
                  className="rounded-select-20"
                  disabled={RoleManager.isAbleTo('brand_coupon', 'edit')}
                  itemKeyProperty="id"
                  itemValueProperty="id"
                  itemNameProperty="name"
                />
                {brandPages.errorMap ? (
                  <InputError message={t(brandPages.errorMap.errorCode)} />
                ) : null}
                <ErrorMessage
                  errors={errors}
                  name="brandId"
                  render={({ message }) => (
                    <InputError message={t('brandRequired')} />
                  )}
                />
            </Grid>

            <Grid item sm={6} xs={12}>
              <FormLabel>
                  {t('category')}
                  <InfoButton
                    className="float-right"
                    onClick={couponInfoTexts['couponCategoryIdInfo']}
                  />
              </FormLabel>
              <SelectMenu
                  name="categoryId"
                  control={control}
                  rules={{ required: true }}
                  selectOptions={categories?.map}
                  variant="outlined"
                  className="rounded-select-20"
                  itemKeyProperty="id"
                  itemValueProperty="id"
                  itemNameProperty="name"
                />
                {categories.errorMap ? (
                  <InputError message={t(categories.errorMap.errorCode)} />
                ) : null}
                <ErrorMessage
                  errors={errors}
                  name="categoryId"
                  render={({ message }) => (
                    <InputError message={t('categoryRequired')} />
                  )}
                />
            </Grid>
          </Grid>

          <Grid container spacing={7}>
            <Grid item sm={6} style={{ alignItems: 'center' }}>
              <FormLabel>
                {t('releaseDate')}
                <InfoButton
                  className="float-right"
                  onClick={couponInfoTexts['couponReleaseDateInfo']}
                />
              </FormLabel>
              <DatePickerInput
                name="releaseDate"
                control={control}
                variant='outlined'
                size="small"
                className="rounded-input-20 date-picker-input"
                rules={{
                  required: t('dateRequired'),
                  validate: releaseDateValidator,
                }}
                invalidDateMessage=""
                maxDateMessage={t('maxDateMessage')}
                label={''}
              />
              <ErrorMessage
                errors={errors}
                name="releaseDate"
                render={({ message }) => <InputError message={message} />}
              />
            </Grid>
            <Grid item sm={6}>
              <FormLabel>
                {t('amountCondition')}
                <InfoButton
                  className="float-right"
                  onClick={couponInfoTexts['couponAmountConditionInfo']}
                />
              </FormLabel>
              <FormTextField
                control={control}
                label={''}
                name="amountCondition"
                margin="none"
                size="small"
                variant="outlined"
                className="rounded-input-20"
                rules={{
                  required: t('amountConditionRequired'),
                  maxLength: {
                    value: 16,
                    message: t('amountConditionLess16'),
                  },
                }}
                errors={errors}
              />
            </Grid>
          </Grid>
          <Grid container spacing={7}>
            <Grid item sm={6} style={{paddingBottom: '0px' }}>
              <Grid item sm={12} style={{ display: 'flex' }}>
                <div style={{ width: '100%' }}>
                  <FormLabel>
                    {t('validationDate')}
                    <InfoButton
                      className="float-right"
                      onClick={couponInfoTexts['couponValidationDateInfo']}
                    />
                  </FormLabel>
                  <DatePickerInput
                    name="validationDate"
                    control={control}
                    variant='outlined'
                    size="small"
                    className="rounded-input-20 date-picker-input"
                    rules={{
                      required: t('dateRequired'),
                      validate: validationDateValidator,
                    }}
                    invalidDateMessage=""
                    maxDateMessage={t('maxDateMessage')}
                    label={''}
                  />

                  <ErrorMessage
                    errors={errors}
                    name="validationDate"
                    render={({ message }) => <InputError message={message} />}
                  />
                </div>
              </Grid>
              {
                (RoleManager.isAbleTo('admin_menu', 'view') || isBrandCountDownEnabled) &&
                <Grid item sm={12} style={{ display: 'flex' }}>
                  <FormCheckBox
                    control={control}
                    name="isCountDownEnabled"
                    label={t('enableCountDown')}
                    className="form-check-label d-flex"
                    checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                    style={{ alignItems: 'center', display: 'flex' }}
                  >
                    <InfoButton
                      onClick={couponInfoTexts['couponIsCountDownEnabledInfo']}
                    />
                  </FormCheckBox>
                </Grid>
              }
            </Grid>
            <Grid item sm={6} style={{ display: 'inherit', paddingBottom: '0px' }} alignItems='center'>
              <FormCheckBox
                control={control}
                name="isVisible"
                label={t('isVisibleOnBrandPage')}
                className="form-check-label d-flex"
                checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                style={{ alignItems: 'center', display: 'flex' }}
              >
                <InfoButton
                  onClick={couponInfoTexts['couponIsVisibleOnBrandPageInfo']}
                />
              </FormCheckBox>
            </Grid>
            {(RoleManager.isAbleTo('brand_page', 'viewEnableVirtualExpiration') || isVirtualExpirationEnabled) && (
              <Grid item sm={12} xl={12} style={{ paddingTop: '0px' }}>
                <div style={{ display: 'flex' }}>
                  <FormCheckBox
                    control={control}
                    name="enableSoftRenewal"
                    label={t('enableVirtualExpiration')}
                    className="form-check-label d-flex"
                    checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                    style={{ alignItems: 'center', display: 'flex' }}
                  >
                    <InfoButton
                      onClick={couponInfoTexts['enableVirtualExpirationInfo']}
                    />
                  </FormCheckBox>
                </div>

                {enableSoftRenewalWatcher && (
                  <RadioButtonGroup
                    name="softRenewalPeriod"
                    control={control}
                    handleOnChange={() =>
                      setTimeout(
                        () =>
                          trigger([
                            'softRenewalPeriod'
                          ]),
                        0
                      )
                    }
                    rules={{ required: true }}
                    >
                    <fieldset className='fieldset' style={{ width: '100%' }}>
                      <Grid container spacing={3}>
                        <Grid item sm={4} xl={4} md={4}>
                          <FormControlLabel
                            value={CONSTANTS.softRenewalStatus.EVERY_TWO_WEEKS}
                            control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                            label={t('everyTwoWeeks')}
                            className='form-sub-label'
                          />
                          <InfoButton
                            onClick={
                              couponInfoTexts['everyTwoWeeksInfo']
                            }
                            alignRight={true}
                          />
                        </Grid>
                        <Grid item sm={3} xl={3} md={3}>
                          <FormControlLabel
                            value={CONSTANTS.softRenewalStatus.END_OF_MONTH}
                            control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                            label={t('endOfTheMonth')}
                            className='form-sub-label'
                          />
                          <InfoButton
                            onClick={
                              couponInfoTexts['endOfTheMonthInfo']
                            }
                            alignRight={true}
                          />
                        </Grid>
                        <Grid item sm={5} xl={5} md={5}>
                          <Grid container alignItems="center" spacing={1}>
                            <Grid item>
                              <FormControlLabel
                                value={CONSTANTS.softRenewalStatus.EVERY_X_DAYS}
                                control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                                label={''}
                                className='form-sub-label'
                              />
                            </Grid>
                            <Grid item>
                              <Controller
                                name="softRenewalNumberOfDays"
                                control={control}
                                defaultValue={0}
                                errors={errors}
                                rules={{
                                  required: softRenewalPeriodWatcher === CONSTANTS.softRenewalStatus.EVERY_X_DAYS,
                                  validate: (value) => {
                                    if (softRenewalPeriodWatcher === CONSTANTS.softRenewalStatus.EVERY_X_DAYS && (value <= 0 || value > 100)) {
                                      return t('virtualExpirationBetween1to100');
                                    }
                                    return true;
                                  },
                                }}
                                helperText={errors.softRenewalNumberOfDays?.message}
                                render={({ onChange, onBlur, value, ref }) => (
                                  <FormTextField
                                    name="softRenewalNumberOfDays"
                                    label=""
                                    type="number"
                                    variant="outlined"
                                    className="rounded-input-20 mt-0"
                                    inputRef={ref}
                                    onChange={onChange}
                                    onBlur={onBlur}
                                    control={control}
                                    hideError={true}
                                    errors={errors}
                                    disabled={softRenewalPeriodWatcher !== CONSTANTS.softRenewalStatus.EVERY_X_DAYS}
                                    rules={{
                                      required: softRenewalPeriodWatcher === CONSTANTS.softRenewalStatus.EVERY_X_DAYS,
                                      validate: (value) => {
                                        if (softRenewalPeriodWatcher === CONSTANTS.softRenewalStatus.EVERY_X_DAYS && (value <= 0 || value > 100)) {
                                          return t('virtualExpirationBetween1to100');
                                        }
                                        return true;
                                      },
                                    }}
                                  />
                                )}
                                />
                            </Grid>
                            <Grid item style={{ fontWeight: 500 }}>
                              {t('days')}
                            </Grid>
                            <Grid item>
                              <InfoButton
                                onClick={
                                  couponInfoTexts['everyDaysInfo']
                                }
                                alignRight={true}
                              />
                            </Grid>
                            {
                              watch('softRenewalPeriod') === CONSTANTS.softRenewalStatus.EVERY_X_DAYS && !!errors.softRenewalNumberOfDays && (
                                <InputError
                                  message={errors.softRenewalNumberOfDays.message}
                                  key="nameHelperM"
                                />
                              )
                            }
                          </Grid>
                        </Grid>
                      </Grid>
                    </fieldset>
                  </RadioButtonGroup>
                )}
              </Grid>
            )}
            <Grid item sm={6}>
              <FormLabel>
                {t('shortDescription')}
                <InfoButton
                  className="float-right"
                  onClick={couponInfoTexts['couponShortDescriptionInfo']}
                />
              </FormLabel>
              <FormTextField
                control={control}
                name="shortCouponDescription"
                label={''}
                margin="none"
                multiline={true}
                rows={4}
                style={{ marginTop: '10px' }}
                size="small"
                className="rounded-input-10"
                variant="outlined"
                rules={{
                  maxLength: {
                    value: 500,
                    message: t('shortDescriptionLess500'),
                  },
                }}
                errors={errors}
                helperText={
                  errors.shortCouponDescription && (
                    <InputError message={errors.shortCouponDescription.message} />
                  )
                }
              />
            </Grid>

            <Grid item sm={6}>
              <FormLabel>
                {t('couponWebsite')}
                <InfoButton
                  className="float-right"
                  onClick={couponInfoTexts['couponWebsiteLinkInfo']}
                />
              </FormLabel>
              <FormTextField
                control={control}
                name="couponLink"
                label={''}
                margin="none"
                size="small"
                variant="outlined"
                className="rounded-input-20"
                rules={{
                  maxLength: {
                    value: 256,
                    message: t('couponWebsiteLength512'),
                  },
                  validate: (url) => {
                    if (!validateUrl(url)) {
                      return t('couponWebsiteValidUrl');
                    }
                    return true;
                  },
                }}
                errors={errors}
                helperText={[
                  <i key="nameHelperI">
                    {t('example')} http://website.com
                    <br />
                  </i>,
                  errors.couponLink ? (
                    <InputError
                      message={errors.couponLink.message}
                      key="nameHelperM"
                    />
                  ) : null,
                ]}
              />
            </Grid>
          </Grid>

          <Grid container spacing={7} className="section-margin-2">
            <Grid item sm={12}>
              <FormLabel>
                {t('couponRegulations')}{' '}
                <span className="secondary-label pl-1">
                  {t('couponRegulationsLimit')}
                </span>
                <InfoButton
                  onClick={couponInfoTexts['couponRegulationsInfo']}
                />
              </FormLabel>

              <RichTextInput
                name="couponRegulations"
                control={control}
                validate={{
                  req: (value) => {
                    return (
                      richTextContents.trim().length > 0 ||
                      !isRichTextEmpty(value) ||
                      t('regulationsRequired')
                    );
                  },
                  maxLen: (value) => {
                    const MAX_DATA_LENGTH = 700;
                    const MAX_LENGTH = 400;

                    const maxLengthErrorMessage = t(
                      'couponRegulationsLessThan',
                      {
                        maxLength: 400,
                      }
                    );
                    return value?.length > MAX_DATA_LENGTH
                      ? `${maxLengthErrorMessage}.`
                      : richTextContents.length > MAX_LENGTH
                      ? maxLengthErrorMessage
                      : true;
                  },
                  maxLines: (value) => {
                    const noOfParagraps = (
                      value.match(/<\/p>|<\/h1>|<\/h2>|<\/h3>|<\/li>|<br>/g) ||
                      []
                    ).length;
                    return noOfParagraps > 9
                      ? t('couponRegulationsMaxParagraphs')
                      : true;
                  },
                }}
                onChangeHandler={(onChange) =>
                  (content, delta, source, editor) => {
                    onChange(content);
                    setRichTextContents(editor.getText());
                    setValue('couponRegulations', content);
                  }}
                theme="snow"
                style={{ marginTop: '10px', marginBottom: '10px' }}
              />

              {errors.couponRegulations && (
                <ErrorMessage
                  errors={errors}
                  name="couponRegulations"
                  render={({ message }) => <InputError message={message} />}
                />
              )}
            </Grid>
          </Grid>

          <FormControl
            component="fieldset"
            fullWidth={true}
            className="section-margin-3"
          >
            <FormLabel component="legend">{t('discountType')}</FormLabel>
            <RadioButtonGroup
              name="discountType"
              control={control}
              handleOnChange={() =>
                setTimeout(
                  () =>
                    trigger([
                      'discountType',
                      'percentDiscountValue',
                      'amountDiscountValue',
                      'freeDescription',
                    ]),
                  0
                )
              }
              rules={{ required: true }}
            >
              <Grid container spacing={7}>
                <Grid item sm={4} style={{ display: 'inherit' }}>
                  <FormControlLabel
                    value="PERCENTAGE"
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                    label={t('discountTypePercentage')}
                    className='form-sub-label'
                  />
                  <InfoButton
                    onClick={
                      couponInfoTexts['couponDiscountTypePercentageInfo']
                    }
                    alignRight={true}
                  />
                </Grid>
                <Grid item sm={4} style={{ display: 'inherit' }}>
                  <FormControlLabel
                    value="AMOUNT"
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                    label={t('discountTypeAmount')}
                    className='form-sub-label'
                  />
                  <InfoButton
                    onClick={couponInfoTexts['couponDiscountTypeAmountInfo']}
                    alignRight={true}
                  />
                </Grid>
                <Grid item sm={4} style={{ display: 'inherit' }}>
                  <FormControlLabel
                    value="FREE"
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                    label={t('discountTypeFree')}
                    className='form-sub-label'
                  />
                  <span className="secondary-label">
                    {t('discountTypeFreeLimit')}
                  </span>
                  <InfoButton
                    onClick={couponInfoTexts['couponDiscountTypeFreeInfo']}
                    alignRight={true}
                  />
                </Grid>
              </Grid>
            </RadioButtonGroup>

            {![CONSTANTS.PERCENTAGE, CONSTANTS.AMOUNT, CONSTANTS.FREE].includes(
              discountTypeWatcher
            ) && (
              <ErrorMessage
                errors={errors}
                name="discountType"
                render={({ message }) => (
                  <InputError message={t('discountTypeRequired')} />
                )}
              />
            )}
          </FormControl>

          <Grid container spacing={7}>
            <Grid item sm={4}>
              <FormTextField
                control={control}
                label={''}
                style={discountTypeField}
                type="number"
                max="100"
                disabled={discountTypeWatcher !== CONSTANTS.PERCENTAGE}
                onChange={(event) => {
                  setValue(
                    'percentDiscountValue',
                    Number.parseFloat(event.target.value)
                  );
                }}
                name="percentDiscountValue"
                variant="outlined"
                className="rounded-input-20"
                rules={{
                  required:
                    discountTypeWatcher === CONSTANTS.PERCENTAGE &&
                    t('percentageRequired'),
                  min: {
                    value: 0.00001,
                    message: t('percentageShouldBeBiggerThan0'),
                  },
                  max: {
                    value: 100,
                    message: t('percentageShouldBeLowerOrEqualTo', {
                      maxValue: '100',
                    }),
                  },
                }}
                errors={errors}
              />
            </Grid>
            <Grid item sm={4}>
              <Controller
                name="amountDiscountValue"
                control={control}
                label={''}
                disabled={discountTypeWatcher !== CONSTANTS.AMOUNT}
                decimalScale={2}
                thousandSeparator={'.'}
                decimalSeparator={','}
                hasFixedDecimalScale={true}
                isNumericString={true}
                as={CurrencyTextInput}
                className='rounded-input-20'
                rules={{
                  required:
                    discountTypeWatcher === CONSTANTS.AMOUNT &&
                    t('amountRequired'),
                  min: {
                    value: 0.00001,
                    message: t('amountShouldBeBiggerThan0'),
                  },
                }}
              />
              {errors.amountDiscountValue && (
                <ErrorMessage
                  errors={errors}
                  name="amountDiscountValue"
                  render={({ message }) => <InputError message={message} />}
                />
              )}
            </Grid>
            <Grid item sm={4}>
              <FormLabel className='description-label'>
                {t('description')}
              </FormLabel>
              <FormTextField
                control={control}
                label={''}
                style={discountTypeField}
                disabled={discountTypeWatcher !== CONSTANTS.FREE}
                name="freeDescription"
                variant="outlined"
                className="rounded-input-20"
                rules={{
                  required:
                    discountTypeWatcher === CONSTANTS.FREE &&
                    t('freeDescriptionRequired'),
                  maxLength: {
                    value: 35,
                    message: t('freeDescriptionLess35'),
                  },
                }}
                errors={errors}
              />
              <FormLabel className='description-label'>
                {t('shortDescription')}
              </FormLabel>
              <FormTextField
                control={control}
                label={''}
                style={discountTypeField}
                disabled={discountTypeWatcher !== CONSTANTS.FREE}
                name="shortDescription"
                variant="outlined"
                className="rounded-input-20"
                rules={{
                  maxLength: {
                    value: 8,
                    message: t('shortDescriptionLess8'),
                  },
                }}
                helperText={
                  errors.shortDescription &&
                  discountTypeWatcher === CONSTANTS.FREE && (
                    <InputError message={errors.shortDescription.message} />
                  )
                }
                errors={errors}
              />
            </Grid>
          </Grid>

          <FormControl
            component="fieldset"
            fullWidth={true}
            margin="normal"
            className="section-margin-3"
          >
            <FormLabel component="legend">{t('codeType')}</FormLabel>
            <RadioButtonGroup
              name="discountCodeType"
              control={control}
              handleOnChange={() =>
                setTimeout(
                  () =>
                    trigger([
                      'discountCodeType',
                      'couponCode',
                      'maxAvailableNumber',
                      'uniqueCodes',
                      'stationaryCode',
                      'buttonText',
                      'linkDescription',
                    ]),
                  0
                )
              }
              rules={{ required: true }}
            >
              <Grid container spacing={7}>
                <Grid className='pb-0 display-inherit' item sm={4}>
                  <FormControlLabel
                    value="GENERIC"
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />}/>}
                    label={t('generic')}
                    className='form-sub-label'
                  />
                  <InfoButton
                    onClick={
                      couponInfoTexts['couponDiscountCodeTypeGenericInfo']
                    }
                    alignRight={true}
                  />
                </Grid>
                <Grid className='pb-0 display-inherit' item sm={4}>
                  <FormControlLabel
                    value="UNIQUE"
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                    label={t('unique')}
                    className='form-sub-label'
                  />
                  <InfoButton
                    onClick={
                      couponInfoTexts['couponDiscountCodeTypeUniqueInfo']
                    }
                    alignRight={true}
                  />
                </Grid>
                <Grid className='pb-0 display-inherit' item sm={4}>
                  <FormControlLabel
                    value="STATIONARY"
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                    label={t('stationary')}
                    className='form-sub-label'
                  />
                  <InfoButton
                    onClick={
                      couponInfoTexts['couponDiscountCodeTypeStationaryInfo']
                    }
                    alignRight={true}
                  />
                </Grid>
              </Grid>
            </RadioButtonGroup>
            {![
              CONSTANTS.STATIONARY,
              CONSTANTS.UNIQUE,
              CONSTANTS.GENERIC,
              CONSTANTS.LINK
            ].includes(discountCodeTypeWatcher) && (
              <ErrorMessage
                errors={errors}
                name="discountCodeType"
                render={({ message }) => (
                  <InputError message={t('codeTypeRequired')} />
                )}
              />
            )}
          </FormControl>
          <Grid container spacing={7}>
            <Grid item sm={4}>
              <fieldset className='fieldset'>
                <legend> {t('generic')} </legend>
                <FormTextField
                  control={control}
                  label={''}
                  placeholder={t('couponCode')}
                  disabled={discountCodeTypeWatcher !== CONSTANTS.GENERIC}
                  name="couponCode"
                  variant="outlined"
                  className='rounded-input-20 mt-0'
                  rules={{
                    validate: (value) => {
                      if (!couponCodeValidate(value))
                        return t('couponCodeRequired');
                      return true;
                    },
                    maxLength: {
                      value: 50,
                      message: t('couponCodeRequired'),
                    },
                  }}
                  helperText={
                    errors.couponCode &&
                    discountCodeTypeWatcher === CONSTANTS.GENERIC && (
                      <InputError message={errors.couponCode.message} />
                    )
                  }
                  errors={errors}
                />
                <FormTextField
                  control={control}
                  type="number"
                  rules={{
                    required: false,
                    min: {
                      value: 1,
                      message: t('noNegativeNumbersAccepted'),
                    },
                  }}
                  label={''}
                  placeholder={t('availableDiscountCodes')}
                  disabled={discountCodeTypeWatcher !== CONSTANTS.GENERIC}
                  name="maxAvailableNumber"
                  variant="outlined"
                  className='rounded-input-20 mt-0'
                  onChange={(event) => {
                    setValue(
                      'maxAvailableNumber',
                      Number.parseInt(event.target.value)
                    );
                  }}
                  helperText={
                    errors.maxAvailableNumber && (
                      <InputError message={errors.maxAvailableNumber.message} />
                    )
                  }
                  errors={errors}
                />
              </fieldset>
            </Grid>
            <Grid item sm={4}>
              <fieldset className='fieldset'>
                <legend> {t('csvUpload')}</legend>
                {discountCodeTypeWatcher === CONSTANTS.UNIQUE ? (
                  <UploadFormField
                    name="uniqueCodes"
                    control={control}
                    acceptedFiles=".csv"
                    buttonLabel={t('insertCouponCodes')}
                    onDrop={async ([file]) => {
                      var reader = new FileReader();
                      reader.onload = (e) => {
                        setUniqueCodes(parseUniqueCodes(e?.target?.result));
                        setAreUniqueCodesUploaded(true);
                        setTimeout(() => trigger('uniqueCodes'), 0);
                      };
                      reader.readAsText(file);
                    }}
                    rules={{
                      validate: () =>
                        discountCodeTypeWatcher !== CONSTANTS.UNIQUE ||
                        (isIdValid(id) &&
                          coupon.discountCodeType === CONSTANTS.UNIQUE) ||
                        !!uniqueCodes?.length,
                    }}
                  >
                    <div
                      style={{
                        display: 'inline-block',
                        marginLeft: '10px',
                        height: '20px',
                      }}
                    >
                      {areUniqueCodesUploaded && uniqueCodesError === '' && (
                        <p> {t('fileUploaded')}</p>
                      )}
                    </div>
                  </UploadFormField>
                ) : (
                  <Button disabled={true} className='upload-chip-disabled'>{t('insertCouponCodes')}</Button>
                )}
              </fieldset>
              <ErrorMessage
                errors={errors}
                name="uniqueCodes"
                render={({ message }) => (
                  <InputError message={t('insertCSVWithUniqueCodes')} />
                )}
              />
              {uniqueCodesError !== '' && (
                <InputError message={uniqueCodesError} />
              )}
            </Grid>
            <Grid item sm={4}>
              <fieldset className='fieldset'>
                <legend> {t('barcodeUpload')} </legend>
                {discountCodeTypeWatcher === CONSTANTS.STATIONARY ? (
                  <UploadFormField
                    name="stationaryCode"
                    control={control}
                    acceptedFiles=".png,.jpg,.jpeg,.gif"
                    buttonLabel={t('insertCouponCode')}
                    showDeleteButton={stationaryCodeFileName !== NONE_SELECTED}
                    onDrop={(file) => {
                      uploadFile(file, 'stationary');
                      setTimeout(() => trigger('stationaryCode'), 0);
                    }}
                    onDelete={() => {
                      toggleDeleteConfirmationPopup();
                      setTypeForDelete('statCode');
                    }}
                    rules={{
                      validate: () => {
                        if (
                          (discountCodeTypeWatcher === CONSTANTS.STATIONARY &&
                            stationaryCodeFileName === NONE_SELECTED) ||
                          (isIdValid(id) &&
                            coupon.discountCodeType === CONSTANTS.STATIONARY &&
                            stationaryCodeFileName === NONE_SELECTED)
                        ) {
                          return t('insertImageWithStationaryCode');
                        } else if (stationaryCodeFileName.length > 221) {
                          return t('imageNameLess221');
                        }
                        return true;
                      },
                    }}
                  >
                    <div
                      style={{
                        display: 'inline-block',
                        marginLeft: '10px',
                      }}
                    >
                      {stationaryCodeFileName}
                    </div>
                  </UploadFormField>
                ) : (
                  <Button disabled={true} className='upload-chip-disabled'> {t('insertCouponCodes')}</Button>
                )}
              </fieldset>
              <ErrorMessage
                errors={errors}
                name="stationaryCode"
                render={({ message }) => <InputError message={message} />}
              />
            </Grid>
            {enableCouponLink && (
              <Grid item sm={4}>
              <FormControlLabel
                value="LINK"
                control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />}/>}
                label={t('link')}
                className='form-sub-label'
                onClick={() => setValue('discountCodeType', 'LINK')}
                checked={discountCodeTypeWatcher === CONSTANTS.LINK}
              />
              <InfoButton
                onClick={
                  couponInfoTexts['couponDiscountCodeTypeGenericInfo']
                }
                alignRight={true}
              />
              <fieldset className='fieldset mt-1'>
                <legend> {t('description')}</legend>
                <FormLabel className='description-label'>
                  {t('buttonText')}
                </FormLabel>
                <FormTextField
                  control={control}
                  label={''}
                  placeholder={t('activate')}
                  disabled={discountCodeTypeWatcher !== CONSTANTS.LINK}
                  name="buttonText"
                  variant="outlined"
                  className='rounded-input-20 mt-0'
                  rules={{
                    validate: (value) => {
                      if (!buttonTextValidate(value))
                        return t('buttonTextRequired');
                      return true;
                    },
                    maxLength: {
                      value: 50,
                      message: t('buttonTextLess50'),
                    },
                  }}
                  helperText={
                    errors.couponCode &&
                    discountCodeTypeWatcher === CONSTANTS.LINK && (
                      <InputError message={errors.buttonText?.message} />
                    )
                  }
                  errors={errors}
                />
                <FormLabel className='description-label'>
                  {t('description')}
                </FormLabel>
                <FormTextField
                  control={control}
                  type="text"
                  rules={{
                    validate: (value) => {
                      if (!buttonDescriptionValidate(value))
                        return t('buttonDescriptionRequired');
                      return true;
                    }
                  }}
                  label={''}
                  placeholder={t('buttonLinkDescription')}
                  multiline={true}
                  disabled={discountCodeTypeWatcher !== CONSTANTS.LINK}
                  rows={4}
                  name="linkDescription"
                  variant="outlined"
                  className='rounded-input-multiline mt-0'
                  helperText={
                    errors.linkDescription && (
                      <InputError message={errors.linkDescription.message} />
                    )
                  }
                  errors={errors}
                />
              </fieldset>
            </Grid>
            )}
          </Grid>

          <Grid container spacing={7} className="section-margin-2">
            {discountCodeTypeWatcher !== CONSTANTS.STATIONARY && (
              <Grid item sm={5} xl={4}>
                <FormLabel component="legend">
                  {t('availableForNewCustomers')}
                  <InfoButton
                    onClick={
                      couponInfoTexts['couponAvailableForNewCustomersInfo']
                    }
                  />
                </FormLabel>
                <RadioButtonGroup
                  name="isForNewCustomers"
                  control={control}
                  rules={{ required: true }}
                >
                  <FormControlLabel
                    value={'true'}
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                    label={t('yes')}
                  />
                  <FormControlLabel
                    value={'false'}
                    control={<Radio checkedIcon={<CheckCircleRoundedIcon className='checkbox-icon' />} />}
                    label={t('no')}
                  />
                </RadioButtonGroup>
              </Grid>
            )}

            {RoleManager.isAbleTo('brand_page', 'viewShowOnHomePage') && (
              <Grid item sm={5} xl={4}>
                <FormCheckBox
                  control={control}
                  name="showOnHomePage"
                  className='form-check-label'
                  label={t('showOnHomePage')}
                />

                {showOnHomePageWatcher && (
                  <DatePickerInput
                    name="showOnHomePageUntilDate"
                    control={control}
                    rules={{}}
                    label={t('until')}
                    minDateMessage={t('dateShouldNotBeBeforeMinimalDate')}
                    className="rounded-input-20 date-picker-input"
                    disablePast={
                      !(
                        coupon?.status === CONSTANTS.couponStatus.EXPIRED ||
                        coupon?.status === CONSTANTS.couponStatus.SOLD_OUT
                      )
                    }
                  />
                )}
              </Grid>
            )}

          </Grid>

          <Grid container className="section-margin-2">
            <Grid item xs={6}>
              <FormLabel component="legend" style={gridStyle}>
                {t('couponImage')}
                <span className="secondary-label pl-1">
                  {t('couponImageSecondaryLabel')}
                </span>
                <InfoButton onClick={couponInfoTexts['couponImageInfo']} />
              </FormLabel>
            </Grid>
          </Grid>

          <Box display="flex" alignItems="center">
            <UploadFormField
              name="couponImage"
              control={control}
              showDeleteButton={imageFileName !== NONE_SELECTED}
              maxFileSize={3145728} //3MB
              acceptedFiles=".png,.jpg,.jpeg,.gif"
              buttonLabel={t('browseImage')}
              onDrop={(file) => {
                uploadFile(file, 'couponImg').then(() =>
                  setTimeout(() => trigger('couponImage'), 0)
                );
              }}
              onDelete={() => {
                toggleDeleteConfirmationPopup();
                setTypeForDelete('image');
              }}
              rules={{
                validate: () => {
                  if (imageFileName === NONE_SELECTED) {
                    return t('couponImageRequired');
                  } else if (imageFileName.length > 221) {
                    return t('couponImageLess221');
                  }
                  return true;
                },
              }}
            >
              <a
                style={{ display: 'inline-block', marginLeft: '10px', color: "#000000" }}
                href={!!coupon && coupon?.image}
                target='blank'
              >
                {imageFileName}
              </a>
            </UploadFormField>
          </Box>
          <br />
          <ErrorMessage
            errors={errors}
            name="couponImage"
            render={({ message }) => <InputError message={message} />}
          />
          <Grid
            container
            alignItems={shouldShowCouponFeedbackBox ? 'flex-start' : 'center'}
            className="section-margin-3"
          >
            <Grid item xs={12} justifyContent='center' style={{ textAlign: 'center' }}>
              <h2
                style={{
                  fontSize: '30px',
                  ...(!isIdValid(id)
                    ? { display: 'flex', justifyContent: 'center' }
                    : {}),
                }}
              >
                {t('couponPreview')}
              </h2>
            </Grid>
            <Grid
              item
              xs={12}
              lg={shouldShowCouponFeedbackBox ? 8 : 6}
              className={`${
                shouldShowCouponFeedbackBox
                  ? 'margin-right'
                  : 'preview-wrapper centered-block'
              }`}
              style={{
                maxWidth: '1000px',
              }}
            >
              <CardHorizontal
                key={(couponForCardHorizontal.remainingTime + Number(isCountDownEnabledWatcher))}
                coupon={couponForCardHorizontal}
                hasImage={imageFileName !== NONE_SELECTED}
                isAuth={true}
                className="card-preview"
                handleLikeClick={() => {}}
                isTouchDevice={false}
                isLiked={false}
                hasLongWordsHeadline={false}
                getCouponCode={() => {}}
                showToastMessage={() => {}}
                PDFDownloadButton={() => {}}
                code={''}
                codeURL={''}
                brandCountDownEnabled={(isBrandCountDownEnabled || RoleManager.isAbleTo('admin_menu', 'view')) && isCountDownEnabledWatcher}
              ></CardHorizontal>
            </Grid>

            {shouldShowCouponFeedbackBox && (
              <CouponQualityFeedbackBox
                feedbackColor={feedbackColor}
                canAddFeedback={RoleManager.isAbleTo('coupon', 'add_feedback')}
                canViewFeedback={RoleManager.isAbleTo(
                  'brand_coupon',
                  'view_feedback'
                )}
                control={control}
                qualityFeedbackOptions={qualityFeedbackOptions}
                couponQuality={couponQuality}
                errors={errors}
              />
            )}
          </Grid>
        </BoxWrap>

        <CustomDialog
          isOpen={delConfOpen}
          title={t('sureDeleteImage')}
          content={t('warningNoUndo')}
          closeLabel={t('cancel')}
          confirmLabel={t('ok')}
          onClose={toggleDeleteConfirmationPopup}
          onConfirm={() => {
            onMediaDelete();
            toggleDeleteConfirmationPopup();
          }}
        />

        <ConfirmSaveCouponDialog
          isOpen={saveDialogOpen}
          closeLabel={t('cancel')}
          title={t('areYouSure')}
          confirmLabel={
            isVisibleWatcher ? t('approveAndGolive') : t('saveChanges')
          }
          onClose={() => setSaveDialogOpen(false)}
          onConfirm={() => {
            handleUpload();
            setSaveDialogOpen(false);
          }}
        >
          {RoleManager.isAbleTo('brand_coupon', 'view') && (
            <h4>
              - 1 {isIdValid(id) ? t('couponUpdate') : t('coupon')}
              <span style={{ fontWeight: 500 }}>
                {' '}
                (
                {isIdValid(id)
                  ? `${brandPackage?.couponUpdates} ${t('couponUpdatesLeft')}`
                  : `${brandPackage?.coupons - brandPackage?.usedCoupons} ${t(
                      'couponCreationsLeft'
                    )}`}
                )
              </span>
            </h4>
          )}
          <span className="preview-wrapper">
            <CardHorizontal
              coupon={couponForCardHorizontal}
              isAuth={true}
              className="card-preview"
              handleLikeClick={() => {}}
              isTouchDevice={false}
              isLiked={false}
              hasLongWordsHeadline={false}
              getCouponCode={() => {}}
              showToastMessage={() => {}}
              PDFDownloadButton={() => {
                // Handle PDF download logic here
              }}
              brandCountDownEnabled={(isBrandCountDownEnabled || RoleManager.isAbleTo('admin_menu', 'view')) && isCountDownEnabledWatcher}
            ></CardHorizontal>
          </span>
        </ConfirmSaveCouponDialog>

        <Box display="flex" alignItems="center" style={{ marginTop: '30px' }}>
          {showSaveButton() && (
            <Button
              className="submit-button primary-button"
              type="submit"
              variant="contained"
              color="primary"
            >
              {t('save')}
            </Button>
          )}

          <Button
            component={NavLink}
            to={redirectRoute}
            className="cancel-button"
            color="primary"
            style={{ marginLeft: '10px' }}
          >
            {t('cancel')}
          </Button>
        </Box>
      </form>

      {/* uncomment when the info video needs to be shown */}

      {/* <div style={{ height: '800px', marginTop: '40px', marginBottom: '20px' }}>
        <h1> Info Video </h1>
        <VideoPlayer videoUrl="https://vimeo.com/209060279" />
      </div> */}
    </>
  );
};
