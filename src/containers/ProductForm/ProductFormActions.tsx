import {
  <PERSON><PERSON><PERSON>_PRODUCT,
  C<PERSON>AR_PRODUCT_SUCCESS,
  CREATE_PRODUCT,
  CREATE_PRODUCT_ERROR,
  CREATE_PRODUCT_SUCCESS,
  EDIT_PRODUCT,
  EDIT_PRODUCT_ERROR,
  EDIT_PRODUCT_SUCCESS,
  GET_PRODUCT,
  GET_PRODUCT_ERROR,
  GET_PRODUCT_SUCCESS,
  DELETE_MEDIA,
  DELETE_MEDIA_SUCCESS,
  DELETE_MEDIA_ERROR,
  ACTIVATE_PRODUCT,
  ACTIVATE_PRODUCT_SUCCESS,
  ACTIVATE_PRODUCT_ERROR,
  CREATE_AND_ACTIVATE_PRODUCT,
  CREATE_AND_ACTIVATE_PRODUCT_SUCCESS,
  CREATE_AND_ACTIVATE_PRODUCT_ERROR,
  EDIT_AND_ACTIVATE_PRODUCT,
  EDIT_AND_ACTIVATE_PRODUCT_SUCCESS,
  EDIT_AND_ACTIVATE_PRODUCT_ERROR,
} from './ProductFormTypes';

import * as ProductService from 'repository/product';

export const deleteMedia = (mediaFile) => {
  return async (dispatch) => {
    dispatch({
      type: DELETE_MEDIA,
    });
    try {
      const response = await ProductService.deleteFile(mediaFile);

      dispatch({
        type: DELETE_MEDIA_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: DELETE_MEDIA_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const createProduct = (product: any) => {
  return async (dispatch) => {
    dispatch({
      type: CREATE_PRODUCT,
    });

    try {
      await ProductService.createProduct(product);
      dispatch({
        type: CREATE_PRODUCT_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: CREATE_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const createAndActivateProduct = (product: any) => {
  return async (dispatch) => {
    dispatch({
      type: CREATE_AND_ACTIVATE_PRODUCT,
    });

    try {
      const createResponse = await ProductService.createProduct(product);
      const activateResponse = await ProductService.activateProduct(
        createResponse.data.id
      );
      dispatch({
        type: CREATE_AND_ACTIVATE_PRODUCT_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: CREATE_AND_ACTIVATE_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const editProduct = (id: number, product: any) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_PRODUCT,
    });

    try {
      await ProductService.editProduct(id, product);
      dispatch({
        type: EDIT_PRODUCT_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: EDIT_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const editAndActivateProduct = (id: number, product: any) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_AND_ACTIVATE_PRODUCT,
    });

    try {
      const createResponse = await ProductService.editProduct(id, product);
      const activateResponse = await ProductService.activateProduct(
        createResponse.data.id
      );
      dispatch({
        type: EDIT_AND_ACTIVATE_PRODUCT_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: EDIT_AND_ACTIVATE_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const activateProduct = (id: number) => {
  return async (dispatch) => {
    dispatch({
      type: ACTIVATE_PRODUCT,
    });

    try {
      await ProductService.activateProduct(id);
      dispatch({
        type: ACTIVATE_PRODUCT_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: ACTIVATE_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getProduct = (id: number) => {
  return async (dispatch) => {
    dispatch({
      type: GET_PRODUCT,
    });
    try {
      const [product, numberOfSubscribers] = await Promise.all([
        ProductService.getProduct(id),
        ProductService.getProductSubscriptions(id),
      ]);

      dispatch({
        type: GET_PRODUCT_SUCCESS,
        payload: {
          ...product.data,
          ...numberOfSubscribers.data,
        },
      });
    } catch (error) {
      dispatch({
        type: GET_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearProduct = () => ({
  type: CLEAR_PRODUCT,
});

export const removeError = () => ({
  type: CREATE_PRODUCT_ERROR,
  payload: null,
});

export const clearSuccess = () => ({
  type: CLEAR_PRODUCT_SUCCESS,
  payload: null,
});
