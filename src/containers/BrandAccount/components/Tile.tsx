import { Box, Container, IconButton, Typography } from '@material-ui/core';
import InfoIcon from '@material-ui/icons/Info';
import { makeStyles } from '@material-ui/styles';
import clsx from 'clsx';
import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

const AVAILABLE_FOREGROUND_COLOR = '#57A3F0';
const AVAILABLE_BACKGROUND_COLOR = '#F2F9FE';
const NOT_AVAILABLE_FOREGROUND_COLOR = '#B7B7B7';
const NOT_AVAILABLE_BACKGROUND_COLOR = '#F1F1F1';
const NOT_ACTIVE_FOREGROUND_COLOR = '#A3CEF9';

const useStyles = makeStyles({
  root: {
    marginLeft: 8,
    marginRight: 8,
    marginBottom: 64,
    padding: 0,
    '&:first-of-type': {
      marginLeft: 0,
    },
    '&:last-of-type': {
      marginRight: 0,
    },
  },
  box: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    color: AVAILABLE_FOREGROUND_COLOR,
    backgroundColor: AVAILABLE_BACKGROUND_COLOR,
    textAlign: 'center',
    border: `1px solid ${AVAILABLE_FOREGROUND_COLOR}`,
    borderRadius: 5,
  },
  boxNotAvailable: {
    color: NOT_AVAILABLE_FOREGROUND_COLOR,
    backgroundColor: NOT_AVAILABLE_BACKGROUND_COLOR,
    border: 0,
  },
  boxNotActive: {
    color: NOT_ACTIVE_FOREGROUND_COLOR,
    border: `1px solid ${NOT_ACTIVE_FOREGROUND_COLOR}`,
  },
  icon: {
    height: '85px',
    marginBottom: '7px',
    marginTop: '7px',
    fontSize: '3rem',
  },
  iconNotActive: {
    opacity: 0.35,
  },
  infoButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    color: AVAILABLE_FOREGROUND_COLOR,
  },
  notAvailableLabel: {
    lineHeight: '2.5rem',
    fontSize: '1rem',
    height: '38px',
  },
  value: {
    height: '38px',
  },
});

interface Props {
  label: string;
  value?: string;
  icon: string;
  isAvailable?: boolean;
  isActive?: boolean;
  bottomElement?: ReactNode;
  onInfoClick?(): void;
}

const Tile = ({
  label,
  value,
  icon,
  isActive = true,
  isAvailable = true,
  bottomElement,
  onInfoClick,
}: Props) => {
  const classes = useStyles();
  const { t } = useTranslation();

  return (
    <Container className={classes.root}>
      <Box
        className={clsx(
          classes.box,
          !isActive && classes.boxNotActive,
          !isAvailable && classes.boxNotAvailable
        )}
      >
        <img
          src={icon}
          alt="tile-icon"
          className={clsx(classes.icon, !isActive && classes.iconNotActive)}
        />
        <Typography variant="h6" component="h4">
          {label}
        </Typography>
        {isAvailable ? (
          <Typography variant="h6" component="h4" className={classes.value}>
            {value !== undefined ? value : '-'}
          </Typography>
        ) : (
          <Typography
            className={classes.notAvailableLabel}
            variant="h6"
            component="h4"
          >
            {t('comingSoon')}
          </Typography>
        )}

        {isAvailable && onInfoClick && (
          <IconButton onClick={onInfoClick} className={classes.infoButton}>
            <InfoIcon />
          </IconButton>
        )}
      </Box>
      {bottomElement}
    </Container>
  );
};

export default Tile;
