import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import { useParams } from 'react-router';
import { Link, useHistory } from 'react-router-dom';
import * as productActions from '../ProductFormActions';
import * as ProductService from 'repository/product';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { useTranslation } from 'react-i18next';

import {
  Button,
  FormLabel,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Radio,
  FormControlLabel,
} from '@material-ui/core';
import { ErrorMessage } from '@hookform/error-message';
import DeleteIcon from '@material-ui/icons/Delete';
import { makeStyles } from '@material-ui/styles';

import { BoxWrap, InputError } from 'components';
import {
  CheckBoxInput,
  FormTextField,
  RadioButtonGroup,
} from 'components/FormInputs';
import { UploadBox } from 'components/UploadBox/UploadBox';

import { FormCheckBox, ColorPickerInput } from 'components/FormInputs';

import 'react-quill/dist/quill.snow.css';

const isIdValid = (id) => id !== 'create';
const gridStyle = { marginTop: '30px', marginBottom: '10px' };

type accentColorType = 'BLACK' | 'WHITE';

type FormValues = {
  id: string;
  title: string;
  initialAmount: number;
  duration: number;
  recurring: boolean;
  recurringAmount?: number;
  position: number;
  subtitle: string;
  additionalInformation: string;
  recurringInterval?: number;
  imageLink: string;
  subHeadline: string;
  label: string;
  description: string;
  status: string;
  corporate: boolean;
  specialOffer: boolean;
  useCustomTheme: boolean;
  homePageTitle: string;
  homePageBodyText: string;
  useCustomColours: boolean;
  accentBackgroundColor: string;
  accentColor: accentColorType;
};

interface StyleProps {
  accentBackgroundColor: string;
  accentColor: accentColorType;
}

const recurringIntervalOptions = Array(12)
  .fill(1)
  .map((x, y) => x + y);

export const SubscriptionStatuses = {
  ACTIVE: 'ACTIVE',
  DEPRECATED: 'DEACTIVATED',
  DRAFT: 'DRAFT',
};

const useStyles = makeStyles({
  hasRecurringCheckbox: {
    marginLeft: '-12px',
    marginBottom: '-12px',
  },
  useCustomThemeCheckbox: {
    marginLeft: '-12px',
    marginTop: '0px',
    marginBottom: '10px',
  },
  useCustomColoursCheckbox: {
    marginLeft: '-12px',
    marginTop: '0px',
    marginBottom: '10px',
  },
  backgroundColorBtn: (props: StyleProps) => ({
    height: '25px',
    width: '40px',
    border: 'none',
    padding: '5px',
    marginTop: '15px',
    marginBottom: '5px',
    display: 'block',
    backgroundColor: props.accentBackgroundColor,
  }),
  previewWrapper: (props: StyleProps) => ({
    marginTop: '15px',
    height: '90px',
    width: '100%',
    position: 'relative',
    backgroundColor: props.accentBackgroundColor,
  }),
  previewContentWrapper: {
    paddingLeft: '12%',
    paddingRight: '12%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  navLink: (props: StyleProps) => ({
    marginRight: '40px',
    color: props.accentColor,
  }),
  logo: {
    textAlign: 'center',
    width: '120px',
    height: '46px',
  },
  logoWrapper: {
    display: 'flex',
    alignContent: 'center',
    justifyContent: 'center',
    height: '46px',
  },
  navBtn: (props: StyleProps) => ({
    color: props.accentColor === 'BLACK' ? 'WHITE' : 'BLACK',
    height: '30px',
    fontWeight: 'bold',
    paddingLeft: '20px',
    paddingRight: '20px',
    backgroundColor: props.accentColor,
    borderColor: props.accentColor,
    fontFamily: 'Montserrat',
  }),
  productImage: {
    display: 'inline-block',
    marginLeft: '10px',
  },
  colorInputOverlay: {
    position: 'fixed',
    top: '0px',
    right: '0px',
    bottom: '0px',
    left: '0px',
  },
  collaborationSymbol: {
    font: '40px Montserrat',
    fontWeight: 500,
  },
});

export const AdminForm = ({ product }) => {
  const { t }: { t: any } = useTranslation();
  const [defaultValues] = useState(() =>
    product
      ? {
          ...product,
          homePageTitle: product.useCustomTheme ? product.homePageTitle : '',
          homePageBodyText: product.useCustomTheme
            ? product.homePageBodyText
            : '',
          accentColor: product.useCustomColours ? product.accentColor : 'BLACK',
          accentBackgroundColor: product.useCustomColours
            ? product.accentBackgroundColor
            : '#81e9f0',
        }
      : {
          id: '',
          title: '',
          initialAmount: null,
          duration: null,
          recurring: false,
          recurringAmount: null,
          position: null,
          subtitle: '',
          additionalInformation: '',
          recurringInterval: 1,
          imageLink: '',
          subHeadline: '',
          label: '',
          description: '',
          status: '',
          corporate: false,
          specialOffer: false,
          useCustomTheme: false,
          homePageTitle: '',
          homePageBodyText: '',
          useCustomColours: false,
          accentBackgroundColor: '#81e9f0',
          accentColor: 'BLACK',
        }
  );

  const formValues = useForm<FormValues>({
    criteriaMode: 'all',
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: defaultValues,
  });

  const { watch, handleSubmit, setValue, control, errors, trigger } =
    formValues;
  const watchRecurring = watch('recurring');
  const watchDuration = watch('duration');
  const useCustomThemeWatcher = watch('useCustomTheme');
  const useCustomColoursWatcher = watch('useCustomColours');
  const backgroundColorWatcher = watch('accentBackgroundColor');
  const imageLinkWatcher = watch('imageLink');
  const colorWatcher = watch('accentColor');
  const corporateWatcher = watch('corporate');

  const classes = useStyles({
    accentBackgroundColor: backgroundColorWatcher,
    accentColor: colorWatcher,
  });

  const [, setInputVals]: any = useState(defaultValues); // only used to trigger validation and for read-only fields

  const NONE_SELECTED = t('noFileSelected');

  const parseFileName = (path) => {
    if (!path) return NONE_SELECTED;
    return path.split('/').slice(-1)[0].split('-').slice(2).join('');
  };

  const [shouldShowColorPicker, setShouldShowColorPicker] = useState(false);
  const [shouldPublishPackage, setShouldPublishPackage] = useState(false);
  const [delConfOpen, setDelConfOpen] = useState(false);
  const [productImage, setProductImage] = useState<any>(
    product?.imageLink || null
  );
  const [productImageName, setProductImageName] = useState(
    !!product && product?.imageLink
      ? parseFileName(product.imageLink)
      : NONE_SELECTED
  );
  const isActive = product?.status === SubscriptionStatuses.ACTIVE;
  const isDeprecated = product?.status === SubscriptionStatuses.DEPRECATED;

  let { id } = useParams();
  const { addMessage } = useAPIError();

  const dispatch = useDispatch();
  const history = useHistory();
  const productsForm = useSelector((state: any) => state.productsForm);
  const productsError = useSelector((state: any) => state.products.error);
  const productsFormError = useSelector(
    (state: any) => state.productsForm.error
  );

  const isDraft = (!isActive && !isDeprecated) || !isIdValid(id);

  useEffect(() => {
    if (!isIdValid(id)) {
      dispatch(productActions.clearProduct);
      setProductImageName(NONE_SELECTED);
    }
  }, []);

  useEffect(() => {
    product?.imageLink &&
      setProductImageName(parseFileName(product?.imageLink));
  }, [product, dispatch, id]);

  const toggleColorPicker = () => {
    setShouldShowColorPicker((showColorPicker) => !showColorPicker);
  };

  const handleDelConfClose = () => {
    setDelConfOpen(false);
  };

  const handleDelConfOpen = () => {
    setDelConfOpen(true);
  };

  const onMediaDelete = () => {
    dispatch(productActions.deleteMedia(productImage));
    setValue('imageLink', '');
    setProductImage(null);
    setProductImageName(NONE_SELECTED);
  };

  const uploadFile = async (file) => {
    if (!file?.length) return;

    dispatch(productActions.deleteMedia(productImage));
    setProductImage(file[0]);
    setProductImageName(file[0].name);

    try {
      const formData = new FormData();
      formData.append('file', file[0]);
      const response = await ProductService.uploadFile(formData);
      const imageLocation = response.data.fileName;
      setValue('imageLink', imageLocation);
    } catch {
      addMessage(t('errorOccured'), 'error');
      return;
    }
  };

  const getCCLogoElement = () => {
    return (
      <img
        className={classes.logo}
        src={
          colorWatcher === 'WHITE'
            ? '/images/logo-white.png'
            : '/images/logo.png'
        }
      />
    );
  };

  const getProductLogoElement = () => {
    if (imageLinkWatcher && imageLinkWatcher !== '') {
      return (
        <img
          className={classes.logo}
          src={
            process.env.REACT_APP_SHARED_MODULE_IMAGE_PATH + imageLinkWatcher
          }
        />
      );
    } else {
      getCCLogoElement();
    }
  };

  const getPreviewLogoSection = () => {
    const hasCustomTheme = useCustomThemeWatcher;
    const hasCorporate = corporateWatcher;

    if (hasCustomTheme) {
      return getProductLogoElement();
    } else {
      if (!hasCorporate) {
        return getCCLogoElement();
      } else {
        return (
          <>
            {getCCLogoElement()}
            <span className={classes.collaborationSymbol}>&#215;</span>
            {getProductLogoElement()}
          </>
        );
      }
    }
  };

  const submit = (data) => {
    const {
      additionalInformation,
      description,
      label,
      subHeadline,
      subtitle,
      ...productPackageData
    } = data;

    const payload: any = {
      ...(additionalInformation && {
        additionalInformation: additionalInformation,
      }),
      ...(description && {
        description: description,
      }),
      ...(label && {
        label: label,
      }),
      ...(subHeadline && {
        subHeadline: subHeadline,
      }),
      ...(subtitle && {
        subtitle: subtitle,
      }),
      ...productPackageData,
      imageLink: imageLinkWatcher,
      duration: parseInt(data.duration) || 99999,
      initialAmount: parseInt(data.initialAmount),
      recurringAmount: parseInt(data.recurringAmount) || 0,
      recurringInterval: data.recurringInterval,
      status: SubscriptionStatuses.DRAFT,
    };

    if (product?.status === SubscriptionStatuses.ACTIVE) {
      dispatch(productActions.editProduct(id, payload));
    } else {
      if (id && isIdValid(id)) {
        if (shouldPublishPackage) {
          dispatch(productActions.editAndActivateProduct(id, payload));
        } else {
          dispatch(productActions.editProduct(id, payload));
        }
      } else {
        if (shouldPublishPackage) {
          dispatch(productActions.createAndActivateProduct(payload));
        } else {
          dispatch(productActions.createProduct(payload));
        }
      }
    }
  };

  const onDurationChange = (event) => {
    const durationValue = event.target.value;
    // if we clear the value for duration, we should clear the values for recurring too
    if (durationValue === '') {
      setValue('recurring', false);
      setValue('recurringInterval', 1);
      setValue('recurringAmount', undefined);
    }
  };

  useEffect(() => {
    if (productsFormError) {
      addMessage(t(productsFormError.errorCode || 'errorOccured'), 'error');
    }
    if (
      productsForm.editSuccess ||
      productsForm.createSuccess ||
      productsForm.activateProductSuccess ||
      productsForm.createAndActivateProductSuccess ||
      productsForm.editAndActivateProductSuccess
    ) {
      history.push('/dashboard/memberships');
    }
  }, [dispatch, productsForm, productsError, productsFormError, addMessage]);

  return (
    <form onSubmit={handleSubmit(submit)}>
      <BoxWrap>
        <Grid container spacing={7}>
          <Grid item sm={6} xs={12}>
            <FormTextField
              name="title"
              label={t('title')}
              rules={{
                required: t('titleRequired'),
                maxLength: {
                  value: 50,
                  message: t('titleLess50'),
                },
              }}
              errors={formValues.errors}
              control={formValues.control}
            />
          </Grid>
          <Grid item sm={6} xs={12}>
            <FormTextField
              name="subtitle"
              label={t('subtitle')}
              rules={{
                maxLength: {
                  value: 50,
                  message: t('subtitleLess50'),
                },
              }}
              errors={formValues.errors}
              control={formValues.control}
            />
          </Grid>
        </Grid>
        <Grid container spacing={7}>
          <Grid item sm={6} xs={12}>
            <FormTextField
              name="label"
              label={t('label')}
              rules={{
                maxLength: {
                  value: 50,
                  message: t('labelLess50'),
                },
              }}
              errors={formValues.errors}
              control={formValues.control}
            />
          </Grid>
        </Grid>
        <Grid container spacing={7} className="mt-2">
          <Grid item sm={6} xs={12}>
            <FormLabel>{t('additionalInformation')}</FormLabel>
            <FormTextField
              name="additionalInformation"
              label=""
              multiline={true}
              rows={4}
              errors={formValues.errors}
              control={formValues.control}
              variant="outlined"
              rules={{
                maxLength: {
                  value: 500,
                  message: t('additionalInformationLess500'),
                },
              }}
              onChange={(event) =>
                setValue('additionalInformation', event.target.value)
              }
              helperText={
                errors.additionalInformation && (
                  <InputError message={errors.additionalInformation.message} />
                )
              }
            />
          </Grid>
          <Grid item sm={6} xs={12}>
            <FormLabel>{t('internalNotes')}</FormLabel>
            <FormTextField
              name="description"
              label=""
              multiline={true}
              rows={4}
              errors={formValues.errors}
              control={formValues.control}
              variant="outlined"
              rules={{
                maxLength: {
                  value: 500,
                  message: t('descriptionLess500'),
                },
              }}
              onChange={(event) => setValue('description', event.target.value)}
              helperText={
                errors.description && (
                  <InputError message={errors.description.message} />
                )
              }
            />
          </Grid>
        </Grid>

        <Grid container spacing={7}>
          <Grid item sm={6} xs={12}>
            <fieldset>
              <legend>{t('initialPeriod')}</legend>
              <FormTextField
                control={formValues.control}
                name="duration"
                label={t('durationMonths')}
                type="number"
                min="1"
                disabled={!isDraft}
                className="color-black"
                errors={formValues.errors}
                rules={{}}
                onChange={onDurationChange}
              />
              <FormTextField
                control={formValues.control}
                name="initialAmount"
                label={t('amount') + ' (€)'}
                type="number"
                min="0"
                disabled={!isDraft}
                className="color-black"
                errors={formValues.errors}
                rules={{
                  required: t('priceRequired'),
                }}
              />
            </fieldset>
          </Grid>
          <Grid item sm={6} xs={12}>
            <CheckBoxInput
              name="recurring"
              label={t('hasRecurringPaymentAfterInitialPeriod')}
              disabled={!isDraft || !watchDuration}
              formValues={formValues}
              className={classes.hasRecurringCheckbox}
              globalTrigger={() => setInputVals((iv) => ({ ...iv }))}
            />
            <fieldset>
              <legend>{t('recurringMode')}</legend>
              <FormControl fullWidth margin="normal">
                <InputLabel htmlFor="recurringInterval">
                  {t('invoicingIntervalMonths')}
                </InputLabel>
                <Controller
                  name="recurringInterval"
                  control={control}
                  rules={{ required: watchRecurring }}
                  render={({ onChange, value }) => (
                    <Select
                      fullWidth={true}
                      value={value}
                      inputProps={{
                        disabled: !watchRecurring || !isDraft,
                      }}
                      onChange={(event: any) => {
                        onChange(event.target.value);
                        setValue('recurringInterval', event.target.value);
                      }}
                    >
                      {recurringIntervalOptions.map(
                        (recurringIntervalOption) => (
                          <MenuItem
                            key={recurringIntervalOption}
                            value={recurringIntervalOption}
                          >
                            {recurringIntervalOption}
                          </MenuItem>
                        )
                      )}
                    </Select>
                  )}
                />
                <ErrorMessage
                  errors={errors}
                  name="recurringInterval"
                  render={({ message }) => (
                    <InputError message={t('recurringIntervalIsRequired')} />
                  )}
                />
              </FormControl>
              <FormTextField
                control={formValues.control}
                name="recurringAmount"
                label={t('amount') + ' (€)'}
                disabled={!watchRecurring || !isDraft}
                type="number"
                className="color-black"
                errors={formValues.errors}
                rules={{
                  required: watchRecurring && t('amountRequired'),
                  validate: (value: string) => {
                    if (watchRecurring && Number(value) < 1) {
                      return t('recurringAmountPositive');
                    }
                  },
                }}
              />
            </fieldset>
          </Grid>
        </Grid>
        <Grid container spacing={7}>
          <Grid item sm={12}>
            <fieldset>
              <legend>{t('customization')}</legend>
              <Grid container spacing={7}>
                <Grid item xs={12} className="pb-0">
                  <FormCheckBox
                    name="useCustomTheme"
                    label={t('useCustomTheme')}
                    control={control}
                    className={classes.useCustomThemeCheckbox}
                  />
                </Grid>
                <Grid item xs={6} className="pt-0 pb-0">
                  <FormTextField
                    name="homePageTitle"
                    label={t('homePageTitle')}
                    errors={formValues.errors}
                    control={formValues.control}
                    className="mt-0"
                    rules={{
                      required:
                        useCustomThemeWatcher && t('homePageTitleRequired'),
                      maxLength: {
                        value: 83,
                        message: t('homePageTitleLessThan', {
                          maxLength: 83,
                        }),
                      },
                    }}
                    disabled={!useCustomThemeWatcher}
                    onChange={(event) =>
                      setValue('homePageTitle', event.target.value)
                    }
                    helperText={
                      errors.homePageTitle && (
                        <InputError message={errors.homePageTitle.message} />
                      )
                    }
                  />
                </Grid>
                <Grid item xs={6} className="pt-0 pb-0">
                  <FormTextField
                    name="homePageBodyText"
                    label={t('homePageBodyText')}
                    errors={formValues.errors}
                    control={formValues.control}
                    className="mt-0"
                    rules={{
                      required:
                        useCustomThemeWatcher && t('homePageBodyTextRequired'),
                      maxLength: {
                        value: 117,
                        message: t('homePageBodyTextLessThan', {
                          maxLength: 117,
                        }),
                      },
                    }}
                    disabled={!useCustomThemeWatcher}
                    onChange={(event) =>
                      setValue('homePageBodyText', event.target.value)
                    }
                    helperText={
                      errors.homePageBodyText && (
                        <InputError message={errors.homePageBodyText.message} />
                      )
                    }
                  />
                </Grid>
                <Grid item xs={12} className="pb-0">
                  <FormCheckBox
                    name="useCustomColours"
                    label={t('useCustomColours')}
                    control={control}
                    className={classes.useCustomColoursCheckbox}
                  />
                </Grid>
                <Grid item xs={6} className="pt-0">
                  <FormLabel>{t('mainBackgroundColor')}</FormLabel>
                  <button
                    className={`${classes.backgroundColorBtn} btn`}
                    onClick={(event) => {
                      event.preventDefault();
                      toggleColorPicker();
                    }}
                    disabled={!useCustomColoursWatcher}
                  />

                  <ColorPickerInput
                    control={control}
                    name="accentBackgroundColor"
                    rules={{
                      required:
                        useCustomColoursWatcher && t('backgroundColorRequired'),
                    }}
                    isOpen={shouldShowColorPicker}
                    toggleIsOpen={toggleColorPicker}
                  />
                  <ErrorMessage
                    errors={errors}
                    name="accentBackgroundColor"
                    render={({ message }) => (
                      <InputError
                        message={message}
                        className={!shouldShowColorPicker ? 'display-none' : ''}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={6} className="pt-0 pb-0">
                  <FormLabel component="legend" className="pb-2">
                    {t('textLogoButtonsColor')}
                  </FormLabel>
                  <RadioButtonGroup
                    name="accentColor"
                    control={control}
                    rules={{ required: useCustomColoursWatcher }}
                  >
                    <FormControlLabel
                      disabled={!useCustomColoursWatcher}
                      value={'BLACK'}
                      control={<Radio />}
                      label={t('black')}
                    />
                    <FormControlLabel
                      disabled={!useCustomColoursWatcher}
                      value={'WHITE'}
                      control={<Radio />}
                      label={t('white')}
                    />
                  </RadioButtonGroup>
                </Grid>
                <Grid item xs={12} className="pt-0">
                  <FormLabel component="legend">{t('preview')}</FormLabel>
                  <div className={classes.previewWrapper}>
                    <div className={classes.previewContentWrapper}>
                      <div>
                        <span className={classes.navLink}>Gutscheincodes</span>
                        <span className={classes.navLink}>Kategorien</span>
                        <span className={classes.navLink}>Marken</span>
                      </div>

                      <div className={classes.logoWrapper}>
                        {getPreviewLogoSection()}
                      </div>
                      <div>
                        <span className={classes.navLink}>Mein account</span>
                        <button className={classes.navBtn}>Abmelden</button>
                      </div>
                    </div>
                  </div>
                </Grid>
              </Grid>
            </fieldset>
          </Grid>
        </Grid>

        <Grid container spacing={7}>
          <Grid item sm={6} xs={12}>
            <CheckBoxInput
              name="corporate"
              label={t('corporate')}
              formValues={formValues}
              globalTrigger={() => setInputVals((iv) => ({ ...iv }))}
            />
            <CheckBoxInput
              name="specialOffer"
              label={t('specialOffer')}
              formValues={formValues}
              globalTrigger={() => setInputVals((iv) => ({ ...iv }))}
            />
          </Grid>
          <Grid item sm={6} xs={12}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                {t('currentSubscribers')}{' '}
                <b>{product?.currentSubscribers || 0}</b>
              </Grid>
              <Grid item xs={12}>
                {t('allTimeSubscribers')}{' '}
                <b>{product?.allTimeSubscribers || 0}</b>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid>
          <Grid>
            <FormLabel component="legend" style={gridStyle}>
              {t('productImage')}
            </FormLabel>
            <Controller
              name="imageLink"
              control={control}
              rules={{
                validate: () => {
                  if (productImageName === NONE_SELECTED) {
                    return t('productImageRequired');
                  } else if (productImageName.length > 221) {
                    return t('productImageNameLess221');
                  }
                  return true;
                },
              }}
              as={
                <>
                  <UploadBox
                    acceptedFiles=".png,.jpg,.jpeg,.gif"
                    onDrop={(file) => {
                      uploadFile(file);
                      setTimeout(() => trigger('imageLink'), 0);
                    }}
                  >
                    {t('browseImage')}
                  </UploadBox>
                  <div className={classes.productImage}>{productImageName}</div>
                  {productImageName !== NONE_SELECTED && (
                    <IconButton
                      aria-label="delete"
                      color="secondary"
                      disabled={!isDraft}
                      onClick={handleDelConfOpen}
                    >
                      <DeleteIcon />
                    </IconButton>
                  )}
                </>
              }
            />
            <br />
            <ErrorMessage
              errors={errors}
              name="imageLink"
              render={({ message }) => <InputError message={message} />}
            />
          </Grid>
        </Grid>
      </BoxWrap>

      <Dialog
        open={delConfOpen}
        onClose={handleDelConfClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteImage')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('warningNoUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleDelConfClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              onMediaDelete();
              handleDelConfClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>

      <Button
        className="submit-button"
        type="submit"
        variant="contained"
        color="primary"
      >
        {t('save')}
      </Button>

      <Button
        component={Link}
        to="/dashboard/memberships/"
        className={'btn btn-cancel'}
        color="primary"
      >
        {t('cancel')}
      </Button>

      {!isActive && (
        <Button
          className="submit-button"
          type="submit"
          variant="contained"
          color="primary"
          onClick={() => setShouldPublishPackage(true)}
        >
          {t('saveActivate')}
        </Button>
      )}
    </form>
  );
};
