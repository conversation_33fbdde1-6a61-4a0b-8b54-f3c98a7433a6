import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { BrandUsersView } from './BrandUsersView';
import { getUsers, deleteUser, changeFilter } from '../User/UserActions';
import { BrandUsersTable } from './components/BrandUsersTable';
import { Loading } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { CLEAR_USER_ERROR_MESSAGES } from 'containers/UserForm/UserFormTypes';
import { CLEAR_USERS } from 'containers/User/UserTypes';
import { useTranslation } from 'react-i18next';

export default () => {
  const dispatch = useDispatch();
  const users = useSelector((state: any) => state.user);
  const userForm = useSelector((state: any) => state.userForm);
  const userDeleteSuccess = useSelector(
    (state: any) => state.user.deleteSuccess
  );

  const { addMessage } = useAPIError();
  const { t }: { t: any } = useTranslation();

  const [filter, setFilter] = useState({
    pageSize: users?.filter?.pageSize || 10,
    page: users?.filter?.page || 0,
    sort: users?.filter?.sort || {
      field: 'username',
      dir: 'DESC',
    },
    filters: [
      {
        field: 'roleId',
        operator: 'EQUAL',
        value: 4,
      },
    ],
    search: '',
  });

  const onFilterChange = (data) => {
    const filterUpdate = { ...filter, ...data };
    setFilter(filterUpdate);
    dispatch(getUsers(filterUpdate));
    dispatch(changeFilter(filterUpdate));
  };

  useEffect(() => {
    if (userDeleteSuccess) {
      addMessage(t('brandUserSuccessfullyDeleted'), 'warning');
    }
    if (userForm.editSuccess) {
      addMessage(t('brandAdminSuccessfullyUpdated'), 'success');
    }
    if (userForm.success) {
      addMessage(t('accountVerificationEmailSent'), 'success');
    }
    if (userForm.error) {
      addMessage(t(userForm.error?.errorCode || 'errorOccured'), 'error');
    }

    dispatch({
      type: CLEAR_USERS,
    });
    dispatch({
      type: CLEAR_USER_ERROR_MESSAGES,
    });
    dispatch(getUsers(filter));
  }, [dispatch, userDeleteSuccess, addMessage]);

  const handleDelete = (selectedUser) => {
    dispatch(deleteUser(selectedUser));
  };

  return (
    <BrandUsersView onFilterChange={onFilterChange} filter={filter}>
      {users.data && (
        <BrandUsersTable
          users={users.data}
          pagination={users.pagination}
          handleDelete={handleDelete}
          filter={filter}
          onFilterChange={onFilterChange}
        >
          {users.loading && <Loading />}
        </BrandUsersTable>
      )}
    </BrandUsersView>
  );
};
