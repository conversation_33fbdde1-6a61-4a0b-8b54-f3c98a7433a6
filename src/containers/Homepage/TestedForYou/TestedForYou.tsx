import React, { useState, useEffect, useCallback } from 'react';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import TestedForYouRow from './TestedForYouRow';
import './TestedForYou.scss';

import * as TestedForYouActions from './TestedForYouActions';

interface TestedForYouItem {
  id?: string | number;
  brandId?: string | number; // Made optional
  textPreview?: string;
  logo?: string;
  image?: string;
  imageLink?: string;
  content?: string;
  buttonText?: string;
  buttonLink?: string;
  heading?: string;
}

interface Brand {
  id: string;
  name: string;
}

interface TestedForYouProps {
  brands: Brand[];
}

const TestedForYou: React.FC<TestedForYouProps> = ({ brands: _brands = [] }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // Use Redux state with useSelector
  const testedForYouState = useSelector((state: any) => state.testedForYou);

  // Local state
  const [mappedItems, setMappedItems] = useState<TestedForYouItem[]>([
    {
      id: undefined,
      textPreview: '',
      logo: '',
      image: '',
      imageLink: '',
      content: '',
      buttonText: '',
      buttonLink: '',
      heading: ''
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; id: string | number | null }>({ open: false, id: null });
  const [imageChanges, setImageChanges] = useState<Record<number, TestedForYouActions.TestedForYouImageChanges>>({});
  const [lastProcessedAction, setLastProcessedAction] = useState<string | undefined>(undefined);

  // Function to fetch tested for you data
  const fetchTestedForYous = useCallback(() => {
    // Dispatch the action to fetch tested for you data
    dispatch(TestedForYouActions.getTestedForYous({
      page: 0,
      size: 100
    }));
  }, [dispatch]);

  // Effect to fetch data on component mount
  useEffect(() => {
    fetchTestedForYous();
  }, [fetchTestedForYous]);

  // Effect to map testedForYou data from Redux to component state
  useEffect(() => {
    if (testedForYouState?.testedForYous?.length > 0) {
      // Map the API response to the component's state format
      const mapped = testedForYouState.testedForYous.map((item: any) => ({
        id: item.id,
        textPreview: item.textPreview || item.text || '', // Support both textPreview and text for backward compatibility
        logo: item.logo || '',
        image: item.image || '',
        imageLink: item.imageLink || '',
        content: item.content || item.textPreview || item.text || '', // Use textPreview/text as fallback
        buttonText: item.buttonText || '',
        buttonLink: item.buttonLink || '',
        heading: item.heading || ''
      }));

      setMappedItems(mapped);
    } else if (!testedForYouState?.loading && testedForYouState?.testedForYous?.length === 0) {
      // If no data and not loading, set a default empty item
      setMappedItems([{
        id: undefined,
        textPreview: '',
        logo: '',
        image: '',
        imageLink: '',
        content: '',
        buttonText: '',
        buttonLink: '',
        heading: ''
      }]);
    }
  }, [testedForYouState]);

  // Effect to handle Redux state changes for success/error messages
  useEffect(() => {
    // Get state values
    const error = testedForYouState?.error;
    const isLoading = testedForYouState?.loading;
    const lastAction = testedForYouState?.lastAction;

    // Only process messages after the loading state has finished and we have a lastAction
    if (!isLoading && lastAction) {
      // Handle error messages
      if (error) {
        // Check if the error is related to a save operation
        const isSaveOperation =
          lastAction.includes('CREATE_ERROR') ||
          lastAction.includes('UPDATE_ERROR') ||
          lastAction.includes('DELETE_ERROR');

        if (isSaveOperation) {
          setSnackbar({
            open: true,
            message: 'An error occurred while saving Tested For You content',
            severity: 'error'
          });
        }
      }
      // Handle success messages
      else {
        // Check if the action is related to a successful save operation
        const isSaveSuccess =
          lastAction.includes('CREATE_SUCCESS') ||
          lastAction.includes('UPDATE_SUCCESS') ||
          lastAction.includes('DELETE_SUCCESS');

        if (isSaveSuccess && lastAction !== lastProcessedAction) {
          // Store the current success action to avoid showing the same message multiple times
          setLastProcessedAction(lastAction);

          let message = 'Operation completed successfully';

          if (lastAction.includes('CREATE_SUCCESS')) {
            message = 'Tested For You item created successfully';
          } else if (lastAction.includes('UPDATE_SUCCESS')) {
            message = 'Tested For You item updated successfully';
          } else if (lastAction.includes('DELETE_SUCCESS')) {
            message = 'Tested For You item deleted successfully';
          }

          setSnackbar({
            open: true,
            message,
            severity: 'success'
          });
        }
      }
    }
  }, [testedForYouState, lastProcessedAction]);

  const handleAddItem = () => {
    // Always add a new item regardless of the state of the last item
    setMappedItems([
      ...mappedItems,
      {
        id: undefined,
        textPreview: '',
        logo: '',
        image: '',
        imageLink: '',
        content: '',
        buttonText: '',
        buttonLink: '',
        heading: ''
      }
    ]);
  };

  const handleDeleteItem = (id: string | number | undefined, idx?: number) => {
    if (!id) {
      // If no id, remove from frontend immediately
      setMappedItems(items => items.filter((_, i) => i !== idx));
      return;
    }
    setDeleteDialog({ open: true, id: id ?? null });
  };

  const confirmDelete = () => {
    try {
      setLoading(true);

      if (deleteDialog.id) {
        dispatch(TestedForYouActions.deleteTestedForYou(Number(deleteDialog.id)));
      }

      // Close the dialog
      setDeleteDialog({ open: false, id: null });
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to delete item',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleItemChange = (index: number, data: Partial<TestedForYouItem>) => {
    setMappedItems(items => items.map((item, i) => i === index ? { ...item, ...data } : item));
  };

  // Handle image change for logo and image
  const handleImageChange = (index: number, type: 'logo' | 'image', file: File) => {
    setImageChanges(prev => ({
      ...prev,
      [index]: {
        ...prev[index],
        [type]: { file }
      }
    }));
  };

  const validateItems = () => {
    const missingFields = mappedItems.some(item => {
      return !item.content?.toString().trim() && !item.textPreview?.toString().trim();
    });

    if (missingFields) {
      setSnackbar({
        open: true,
        message: 'Please fill the content field.',
        severity: 'error'
      });
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateItems()) {
      return;
    }

    try {
      setLoading(true);

      // Process all items
      await Promise.all(mappedItems.map(async (item, index) => {
        const changes = imageChanges[index];

        // Upload images if needed
        let logoFileName = item.logo || '';
        let imageFileName = item.image || '';

        if (changes?.logo?.file) {
          try {
            logoFileName = await dispatch(TestedForYouActions.uploadTestedForYouImage(
              changes.logo.file,
              item.id ? Number(item.id) : undefined,
              'testedForYouLogo'
            ));
          } catch (error) {
            console.error('Failed to upload logo:', error);
          }
        }

        if (changes?.image?.file) {
          try {
            imageFileName = await dispatch(TestedForYouActions.uploadTestedForYouImage(
              changes.image.file,
              item.id ? Number(item.id) : undefined,
              'testedForYouImage'
            ));
          } catch (error) {
            console.error('Failed to upload image:', error);
          }
        }

        // For create operation
        if (!item.id) {
          const createPayload = {
            textPreview: item.textPreview || item.content || '',
            logo: logoFileName,
            image: imageFileName,
            imageLink: item.imageLink || '',
            content: item.content || item.textPreview || '',
            buttonText: item.buttonText || '',
            buttonLink: item.buttonLink || '',
            heading: item.heading || ''
          };

          await dispatch(TestedForYouActions.createTestedForYou(createPayload));
        }
        // For update operation
        else {
          const updatePayload = {
            id: Number(item.id),
            textPreview: item.textPreview || item.content || '',
            logo: logoFileName,
            image: imageFileName,
            imageLink: item.imageLink || '',
            content: item.content || item.textPreview || '',
            buttonText: item.buttonText || '',
            buttonLink: item.buttonLink || '',
            heading: item.heading || ''
          };

          await dispatch(TestedForYouActions.updateTestedForYou(updatePayload));
        }
      }));

      // Reset image changes after successful save
      setImageChanges({});

      // Refresh data from server
      fetchTestedForYous();

    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to save items',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="tested-for-you-section">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 className="section-title" style={{ margin: 0 }}>{t('testedForYou')}</h2>
        <Button
          variant="contained"
          className="save-button"
          onClick={handleSave}
          disabled={testedForYouState?.loading || loading}
        >
          {(testedForYouState?.loading || loading) ? t('saving') : t('save').toUpperCase()}
        </Button>
      </div>



      <div>
        {mappedItems.map((item, idx) => (
          <TestedForYouRow
            key={item.id || idx}
            item={item}
            onDelete={() => handleDeleteItem(item.id, idx)}
            onChange={data => handleItemChange(idx, data)}
            onImageChange={(type, file) => handleImageChange(idx, type, file)}
          />
        ))}
        <Button
          type="button"
          variant="contained"
          className="add-button"
          style={{
            textTransform: 'capitalize',
            height: 28,
            backgroundColor: '#000',
            color: 'white',
            borderRadius: '15px',
            fontSize: 12
          }}
          onClick={handleAddItem}
          disabled={testedForYouState?.loading || loading}
        >
          + {t('addTestedForYou')}
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, id: null })}>
        <DialogTitle>{t('Delete Item?')}</DialogTitle>
        <DialogContent>{t('Are you sure you want to delete this item?')}</DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, id: null })}>{t('Cancel')}</Button>
          <Button color="secondary" onClick={confirmDelete}>{t('Delete')}</Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          style={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default TestedForYou;
