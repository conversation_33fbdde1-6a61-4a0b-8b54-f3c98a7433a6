import API from 'services/api';

export const uploadFile = async (file: File, testedForYouId?: number, imageType: string = 'logo') => {
    const url = `/media/upload?entityType=TestedForYou${testedForYouId ? `&testedForYouId=${testedForYouId}` : ''}&imageType=${imageType}`;
    const formData = new FormData();
    formData.append('file', file);
    return await API.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
};

export const deleteFile = async (file: string) => {
    return await API.delete(`/media/delete?s3FileName=${file}`);
};

export interface TestedForYou {
    id?: number;
    brandId?: number; // Made optional
    textPreview?: string;
    logo?: string;
    image?: string;
    imageLink?: string;
    content?: string;
    buttonText?: string;
    buttonLink?: string;
    heading?: string;
}

export interface TestedForYouListRequest {
    page: number;
    size: number;
    filters?: Record<string, any>;
}

export interface TestedForYouResponse {
    data: TestedForYou[];
    total: number;
}

export const getTestedForYous = async (request: TestedForYouListRequest): Promise<TestedForYouResponse> => {
    const response = await API.post('/tested-for-you/list', request);
    return response.data;
};

export const getTestedForYouById = async (testedForYouId: number): Promise<TestedForYou> => {
    const response = await API.get(`/tested-for-you/${testedForYouId}`);
    return response.data;
};

export const createTestedForYou = async (data: TestedForYou): Promise<TestedForYou> => {
    const response = await API.post('/tested-for-you', data);
    return response.data;
};

export const updateTestedForYou = async (data: TestedForYou): Promise<TestedForYou> => {
    const response = await API.put('/tested-for-you', data);
    return response.data;
};

export const deleteTestedForYou = async (testedForYouId: number): Promise<void> => {
    await API.delete(`/tested-for-you/${testedForYouId}`);
};
