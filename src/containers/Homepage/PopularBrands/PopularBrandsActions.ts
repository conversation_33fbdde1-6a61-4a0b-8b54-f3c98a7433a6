import * as PopularBrandsTypes from './PopularBrandsTypes';
import * as PopularEntitiesAPI from 'repository/popularEntities';

export const getPopularBrands = (request?: PopularEntitiesAPI.PopularEntitiesListRequest) => async (dispatch) => {
    dispatch({ type: PopularBrandsTypes.GET_POPULAR_BRANDS });
    try {
        const response = await PopularEntitiesAPI.getPopularBrands(request);
        dispatch({ type: PopularBrandsTypes.GET_POPULAR_BRANDS_SUCCESS, payload: response });
        return response;
    } catch (error) {
        console.error('Error fetching popular brands:', error);
        dispatch({ type: PopularBrandsTypes.GET_POPULAR_BRANDS_ERROR, payload: error });
        // Return an empty response instead of throwing to prevent component errors
        return { brandIds: [] };
    }
};

export const updatePopularBrands = (data: PopularEntitiesAPI.PopularBrands) => async (dispatch) => {
    dispatch({ type: PopularBrandsTypes.UPDATE_POPULAR_BRANDS });
    try {
        await PopularEntitiesAPI.updatePopularBrands(data);
        dispatch({ type: PopularBrandsTypes.UPDATE_POPULAR_BRANDS_SUCCESS, payload: data });
        return data;
    } catch (error) {
        console.error('Error updating popular brands:', error);
        dispatch({ type: PopularBrandsTypes.UPDATE_POPULAR_BRANDS_ERROR, payload: error });
        throw error;
    }
};
