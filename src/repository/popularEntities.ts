import API from 'services/api';

export interface PopularCoupons {
    couponIds: number[];
}

export interface PopularBrands {
    brandIds: number[];
}

export interface PopularEntitiesListRequest {
    page?: number;
    pageSize?: number;
    sort?: Array<{field: string; dir: string}>;
    search?: string;
}

export interface PopularEntity {
    id: number;
    brandIds: number[];
    couponIds: number[];
}

export interface PopularEntitiesResponse {
    data: PopularEntity[];
    total: number;
}

export const updatePopularCoupons = async (data: PopularCoupons): Promise<void> => {
    await API.put('/popular-entities/coupons', data);
};

export const getPopularCoupons = async (request: PopularEntitiesListRequest = {}): Promise<PopularCoupons> => {
    const defaultRequest = {
        page: 0,
        pageSize: 10,
        sort: [{ field: 'id', dir: 'DESC' }],
        search: '',
        ...request
    };

    const response = await API.post('/popular-entities/list', defaultRequest);
    console.log('Popular entities API response:', response.data);

    // Check if the response has the expected format
    if (response.data && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
        // Always take the first item as specified
        const firstEntity = response.data.data[0];
        console.log('Using first entity from response:', firstEntity);

        if (firstEntity && Array.isArray(firstEntity.couponIds)) {
            return { couponIds: firstEntity.couponIds };
        }
    }

    // If the response format is different or empty, return an empty array
    console.log('No valid data found in response, returning empty couponIds array');
    return { couponIds: [] };
};

export const updatePopularBrands = async (data: PopularBrands): Promise<void> => {
    await API.put('/popular-entities/brands', data);
};

export const getPopularBrands = async (request: PopularEntitiesListRequest = {}): Promise<PopularBrands> => {
    const defaultRequest = {
        page: 0,
        pageSize: 10,
        sort: [{ field: 'id', dir: 'DESC' }],
        search: '',
        ...request
    };

    const response = await API.post('/popular-entities/list', defaultRequest);
    console.log('Popular entities API response for brands:', response.data);

    // Check if the response has the expected format
    if (response.data && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
        // Always take the first item as specified
        const firstEntity = response.data.data[0];
        console.log('Using first entity from response for brands:', firstEntity);

        if (firstEntity && Array.isArray(firstEntity.brandIds)) {
            return { brandIds: firstEntity.brandIds };
        }
    }

    // If the response format is different or empty, return an empty array
    console.log('No valid data found in response, returning empty brandIds array');
    return { brandIds: [] };
};
