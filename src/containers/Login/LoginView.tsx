import React from 'react';
import { IntroCard, LoginForm } from 'components';
import { useTranslation } from 'react-i18next';

import './Login.scss';

export const LoginView = ({ login, loginUser, clearErrors }) => {
  const { t }: { t: any } = useTranslation();
  return (
    <div className="login-page">
      <IntroCard
        title={t('couponManagementSystem')}
        subtitle={t('loginToYourAccount')}
      >
        <LoginForm
          login={login}
          loginUser={loginUser}
          clearErrors={clearErrors}
        />
      </IntroCard>
    </div>
  );
};
