.tested-for-you-section {
  .save-button {
    background-color: #2196f3;
    color: white;
    border-radius: 15px;
    padding: 3px 15px;
    font-size: 12px;
    text-transform: uppercase;
    min-width: 60px;
    height: 26px;

    &:hover {
      background-color: #1976d2;
    }
  }

  .editor-container {
    .quill-editor {
      .ql-toolbar {
        border-radius: 15px 15px 0px 0px;
        border: 1px solid #707070 !important;
        border-bottom: none !important;
      }

      .ql-container {
        border-radius: 0px 0px 15px 15px;
        border: 1px solid #707070 !important;
        min-height: 80px;
      }

      .ql-editor {
        min-height: 80px;
        height: 80px !important;
        font-size: 12px;
      }
    }
  }

  .add-button {
    background-color: #000;
    color: white;
    border-radius: 15px;
    padding: 3px 15px;
    font-size: 12px;
    margin-top: 10px;

    &:hover {
      background-color: #333;
    }
  }
}
