import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableFooter from '@material-ui/core/TableFooter';
import TableHead from '@material-ui/core/TableHead';
import TablePagination from '@material-ui/core/TablePagination';
import TableRow from '@material-ui/core/TableRow';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  HistoryActionType,
  HistoryItem,
  HistoryResourceType,
} from 'repository/brand-account';
import { Sorter } from '../../../components/Sorter/Sorter';

const TYPE_I18N_KEYS_BY_VALUE: Record<HistoryActionType, string> = {
  REQUEST: 'requested',
  ADD: 'added',
  USE: 'used',
};

const RESOURCE_I18N_KEYS_BY_VALUE: Record<HistoryResourceType, string> = {
  COUPON_USAGE: 'coupon',
  COUPON_UPDATE: 'couponUpdate',
  IMAGE_VIDEO: 'imageVideo',
  UNBOXING_VIDEO: 'unboxingVideo',
  REACH_BOOST: 'reachBoost',
};

export interface Filter {
  pageSize: number;
  page: number;
  sort: {
    field: keyof HistoryItem;
    dir: 'ASC' | 'DESC';
  };
}

const formatDate = (dateIsoString: string) =>
  new Date(dateIsoString).toLocaleDateString();

type HistoryItemSorter = (a: HistoryItem, b: HistoryItem) => number;

const createAlphabeticalSorter =
  (field: keyof HistoryItem) => (a: HistoryItem, b: HistoryItem) =>
    a[field].toString().localeCompare(b[field].toString());

const SORTERS_BY_FIELD: Record<keyof HistoryItem, HistoryItemSorter> = {
  // TODO should we change this with sorting on BE?
  id: createAlphabeticalSorter('id'),
  lastModified: (a: HistoryItem, b: HistoryItem) => {
    const dateA = new Date(a.lastModified);
    const dateB = new Date(b.lastModified);
    if (dateA > dateB) {
      return 1;
    }
    if (dateB > dateA) {
      return -1;
    }

    return 0;
  },
  amount: (a: HistoryItem, b: HistoryItem) => a.amount - b.amount,
  type: createAlphabeticalSorter('type'),
  action: createAlphabeticalSorter('action'),
};

export interface Props {
  data: HistoryItem[];
}

export const HistoryTable = ({ data }: Props) => {
  const { t }: { t: any } = useTranslation();

  const [filter, setFilter] = useState<Filter>({
    pageSize: 5,
    page: 0,
    sort: {
      field: 'lastModified',
      dir: 'DESC',
    },
  });

  const onFilterChange = (changes: Partial<Filter>) => {
    setFilter((prev) => ({ ...prev, ...changes }));
  };

  const handleChangePage = (event, newPage) => {
    onFilterChange({
      page: newPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    onFilterChange({
      pageSize,
      page: 0,
    });
  };

  const rowsToShow = useMemo(() => {
    const sortedRows = [...data];
    sortedRows.sort(SORTERS_BY_FIELD[filter.sort.field]);
    if (filter.sort.dir === 'DESC') {
      sortedRows.reverse();
    }

    let startIndex = 0;
    let endIndex = sortedRows.length;

    if (filter.pageSize !== -1) {
      startIndex = filter.page * filter.pageSize;
      endIndex = startIndex + filter.pageSize;
    }
    return sortedRows.slice(startIndex, endIndex);
  }, [filter, data]);
  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter
                label={t('date')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'lastModified', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('type')}
                handleSort={(direction) =>
                  onFilterChange({ sort: { field: 'type', dir: direction } })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('amount')}
                handleSort={(direction) =>
                  onFilterChange({ sort: { field: 'amount', dir: direction } })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('resource')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'action', dir: direction },
                  })
                }
              />
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rowsToShow?.map((row) => (
            <TableRow key={row.id}>
              <TableCell scope="row">{formatDate(row.lastModified)}</TableCell>
              <TableCell scope="row">
                {t(TYPE_I18N_KEYS_BY_VALUE[row.type])}
              </TableCell>
              <TableCell scope="row">
                {row.action !== 'REACH_BOOST' ? row.amount : ''}
              </TableCell>
              <TableCell scope="row">
                {t(RESOURCE_I18N_KEYS_BY_VALUE[row.action])}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={data.length}
              rowsPerPage={filter.pageSize}
              page={filter.page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  );
};
