{"userManagement": "Nutzerverwaltung", "createUser": "<PERSON><PERSON><PERSON> er<PERSON>", "searchUsers": "<PERSON><PERSON><PERSON> <PERSON>en", "noResultsFound": "<PERSON><PERSON> gefunden", "name": "Name", "email": "Email", "status": "Status", "role": "<PERSON><PERSON>", "actions": "Aktionen", "manage": "<PERSON><PERSON><PERSON><PERSON>", "sureDeleteUser": "Bist du dir sicher, dass du diese/n Nutzer/in löschen möchtest?", "warningNoUndo": "Warnung! Diese Aktion kann nicht rückgängig gemacht werden.", "cancel": "Abbrechen", "ok": "OK", "successUpdateUser": "Der/die Nutzer/in wurde erfolgreich aktualisiert.", "accountVerificationEmailSent": "Eine Email zur Kontoverifizierung wurde verschickt.", "errorOccured": "<PERSON>s ist ein Fehler aufgetreten.", "successDeleteUser": "Der/die Nutzer/in wurde erfolgreich gelöscht", "successDeleteConsumer": "Der/die Kunde/Kundin wurde erfolgreich gelö<PERSON>t", "customersManagement": "Userverwaltung", "exportCustomers": "Kunden exportieren", "searchCustomers": "<PERSON><PERSON> suchen", "subscriptionStartDate": "Startdatum Mitgliedschaft", "sureDeleteConsumer": "Bist du dir sicher, dass du diesen Kunden löschen möchtest?", "noUndo": "Warnung! Diese Aktion kann nicht rückgängig gemacht werden.", "brandName": "Brand-Name", "brandWebsite": "BRAND-URL", "sureDeleteBrand": "Bist du dir sicher, dass du die Brand Page löschen möchtest?", "sureDisableBrand": "Bist du dir sicher, dass du die Brand Page deaktivieren möchtest?", "sureEnableBrand": "Bist du dir sicher, dass du die Brand Page aktivieren möchtest?", "successUpdateBrand": "Die Brand Page wurde erfolgreich aktualisiert.", "successCreatedBrand": "Die Brand Page wurde erfolgreich erstellt.", "successDeleteBrand": "Die Brand Page wurde erfolgreich gelöscht.", "successEnableBrand": "Die Brand Page wurde erfolgreich aktiviert.", "successDisableBrand": "Die Brand Page wurde erfolgreich deaktiviert.", "brandManagement": "Brand-Page-<PERSON><PERSON><PERSON><PERSON><PERSON>", "createBrand": "Brand Page erstellen", "searchBrands": "Brands suchen", "unexpectedError": "Ein unerwarteter Fehler ist aufgetreten", "brandNameRequired": "Brand-Name wird <PERSON>", "brandNameLength128": "Der Brand-Name darf maximal 128 <PERSON><PERSON><PERSON> lang sein", "brandWebsiteLength256": "Landing-Page-URL darf maximal 256 <PERSON><PERSON><PERSON> lang sein", "brandWebsiteValidUrl": "Landing-Page-URL muss eine gültige URL sein", "example": "Beispiel", "slug": "Slug", "slugNoSpacesOrCharacters": "Slug darf keine Leer- oder Sonderzeichen enthalten", "slugRequired": "Slug wird <PERSON><PERSON><PERSON><PERSON>", "slugLessThan64": "Slug darf maximal 64 <PERSON><PERSON><PERSON> lang sein", "slugExists": "Slug existiert bereits", "linkTitle": "URL-DARSTELLUNG", "linkTitleLess256": "<PERSON>rl Darstellung darf maximal 256 <PERSON><PERSON><PERSON> lang sein", "metaTitleLess250": "Der Meta-Titel darf maximal 250 Zeichen lang sein", "metaDescription": "Meta-Beschreibung", "metaDescriptionLess500": "Die Meta-Beschreibung darf maximal 500 Zeichen lang sein", "brandPicture": "HEADER", "brandPictureSecondaryLabel": "(740 X 340PX; MAX. 3MB)", "brandPictureRequired": "Header wird <PERSON><PERSON><PERSON><PERSON>", "brandPictureNameLess221": "Der Name des Headeres darf maximal 221 Zeichen lang sein", "browseImage": "Bild hochladen...", "isPopular": "Auf Startseite anzeigen?", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "isPopularIsRequired": "<PERSON><PERSON> zu \"<PERSON>vorgeho<PERSON>?\" wird <PERSON><PERSON><PERSON><PERSON>", "brandLogo": "Logo", "brandLogoSecondaryLabel": "(mind. 800PX BREIT)", "brandLogoRequired": "Logo wird <PERSON>", "brandLogoLess221": "Der Name des Logos darf maximal 221 Zeichen lang sein", "hideBrandForUsers": "Brand für bestimmte Nutzer verstecken", "hideBrandOptionRequired": "<PERSON><PERSON> <PERSON> \"Brand für bestimmte Nutzer verstecken\" wird <PERSON><PERSON><PERSON>gt", "section": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Titel", "brandTitleRequired": "<PERSON>-<PERSON><PERSON><PERSON> wird <PERSON>", "brandTitleLess128": "<PERSON> Brand-Titel darf maximal 128 <PERSON><PERSON><PERSON> lang sein", "media": "Medien-Link", "brandMediaValidUrl": "Der Medien-Link muss eine gültige URL sein", "brandMediaLess256": "Der Medien-Link darf maximal 256 <PERSON><PERSON><PERSON> lang sein", "description": "Beschreibung", "brandDescriptionRequired": "Brand-Beschreibung wird benö<PERSON>gt.", "brandDescriptionMinMaxLength": "Die Brand-Beschreibung muss zwischen 1800 und 10.000 Zeichen lang sein.", "showCTABtn": "CTA-Button anzeigen", "CTABtnLink": "CTA-Button-Link", "brandLinkLess256": "Der Brand-Link darf maximal 256 <PERSON><PERSON><PERSON> lang sein", "brandLinkValidUrl": "Der Brand-Link muss eine gültige URL sein", "hideSection": "Abschnitt verbergen", "brandHeadlineRequired": "Brand-Überschrift wird benö<PERSON>gt", "brandHeadlineLess256": "Die Brand-Überschrift darf maximal 256 Zei<PERSON> lang sein", "brandHeadline": "Brand-Überschrift", "brandDescription": "Brand-Beschreibung", "mediaLinkLess256": "Der Medien-Link darf maximal 256 <PERSON><PERSON><PERSON> lang sein", "mediaLinkValidUrl": "Der Medien-Link muss eine gültige URL sein", "account": "Ko<PERSON>", "home": "Home", "website": "Website", "category": "<PERSON><PERSON><PERSON>", "interestedInWeVoucherProducts": "An WeVoucher-Produkten interessiert", "address": "<PERSON><PERSON><PERSON>", "billingStreet": "Straße Rechnung", "billingPostalCode": "<PERSON><PERSON><PERSON><PERSON> Rechnung", "billingCity": "<PERSON><PERSON><PERSON><PERSON>", "billingCountry": "Land Rechnung", "contacts": "Kontakte", "salutation": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "phone": "Telefonnr.", "mobilePhone": "Mobilnr.", "position": "Position", "id": "ID", "products": "Produkte", "productCode": "Produkt-Code", "startDate": "Startdatum", "duration": "<PERSON><PERSON>", "sureDeleteImage": "Bist du dir sicher, dass du dieses Bild löschen möchtest?", "save": "Speichern", "saveReqApproval": "Speichern und Freigabe anfordern", "editBrand": "<PERSON> bearbeiten", "createBrandHeadline": "Brand Page erstellen", "brand": "Brand", "validationDate": "Gültigkeitsdatum", "releaseDate": "Veröffentlichungsdatum", "amountCondition": "Mindestbestellwert", "countDownEnabled": "Countdown aktiviert", "all": "Alle", "rowsPerPage": "Zeilen pro Seite", "sureDeleteCoupon": "Bist du dir sicher, dass du diesen Coupon löschen möchtest?", "successCouponUpdate": "Der Coupon wurde erfolgreich aktualisiert.", "successCouponCreated": "Der Coupon wurde erfolgreich erstellt.", "successCouponDeleted": "Der Coupon wurde erfolgreich gelöscht.", "couponManagement": "Coupon-Verwaltung", "createCoupon": "Coupon erstellen", "searchCoupon": "Coupon suchen", "editCoupon": "Coupon bearbeiten", "couponStatusActive": "Aktiv", "couponStatusPending": "<PERSON><PERSON><PERSON>", "couponStatusExpired": "Abgelaufen", "couponStatusSoldOut": "Ausverkauft", "noFileSelected": "<PERSON><PERSON> ausgewählt", "eachCodeNotLongerThan50": "<PERSON><PERSON> einzelne Code darf maximal 50 Zeichen lang sein", "brandRequired": "<PERSON> wird ben<PERSON>tigt", "categoryRequired": "<PERSON><PERSON><PERSON> wird <PERSON>", "dateRequired": "<PERSON><PERSON> wird <PERSON><PERSON>", "amountConditionRequired": "Einlösebedingungen wird benötigt", "amountConditionLess16": "\"Einlösebedingungen\" darf maximal 16 Zeichen lang sein", "couponRegulations": "Einlösebedingungen", "couponRegulationsLimit": "(Max. 400 Zeichen)", "brandDescriptionLimit": "(Min 1800 Zeichen und max 10.000 Zeichen)", "brandHeadlineLimit": "(<PERSON><PERSON> 128 <PERSON><PERSON><PERSON>)", "regulationsRequired": "Bestimmungen werden benötigt", "discountType": "<PERSON><PERSON><PERSON>-Art", "discountTypePercentage": "Prozentual", "discountTypeAmount": "Betrag", "discountTypeFree": "Freitext", "discountTypeFreeLimit": "(Max. 35/8 <PERSON><PERSON><PERSON>)", "discountTypeRequired": "Rabatt-<PERSON> wird <PERSON>", "percentage": "Prozent", "percentageRequired": "Prozentangabe wird <PERSON>", "amount": "Betrag", "amountRequired": "<PERSON><PERSON> wird ben<PERSON><PERSON>", "freeDescriptionRequired": "Beschreibung für kostenlosen Rabatt wird benötigt", "freeDescriptionLess35": "Beschreibung für kostenlosen Rabatt darf maximal 35 Zeichen lang sein", "shortDescription": "Kurzbeschreibung", "shortDescriptionLess8": "Kurzbeschreibung darf maximal 8 Zeichen lang", "codeType": "Code-Art", "generic": "<PERSON><PERSON><PERSON>", "link": "Link", "unique": "Unique Codes", "stationary": "Stationär (Barcode)", "csvUpload": "CSV-Upload", "barcodeUpload": "Barcode-Upload", "stationaryLimit": "(<PERSON><PERSON> 221 <PERSON><PERSON><PERSON>)", "codeTypeRequired": "Code-<PERSON> wird <PERSON>", "couponCode": "Coupon-Code", "couponCodeRequired": "Coupon-Code wird ben<PERSON><PERSON>gt", "couponCodeLess50": "Coupon-Code darf maximal 50 Zeichen lang sein", "buttonTextRequired": "Button-Text wird <PERSON>gt", "buttonTextLess50": "Button-Text darf maximal 50 Zeichen lang sein", "buttonDescription": "Button-Beschreibung", "buttonDescriptionRequired": "Button-Beschreibung wird benö<PERSON>", "noNegativeNumbersAccepted": "Wert darf nicht negativ sein", "availableDiscountCodes": "Verfügbare Rabatt-Codes", "insertCouponCodes": "Coupon-Codes einfügen", "insertCouponCode": "Coupon-Code einfügen", "fileUploaded": "<PERSON>i wurde hoch<PERSON>aden", "insertCSVWithUniqueCodes": "CSV-Datei mit einzigartigen Codes einfügen", "insertImageWithStationaryCode": "Bild mit stationärem Code hochladen", "imageNameLess221": "Bild-Name darf maximal 221 <PERSON><PERSON>chen lang sein", "availableForNewCustomers": "Nur für Neukunden verfügbar", "showOnHomePage": "Auf Homepage anzeigen", "enableVirtualExpiration": "Virtuelles Ablaufdatum aktivieren", "enableVirtualExpirationInfo": "<h1>Künstliche Verknappung</h1><p>Mit der Funktion &quot;künstliche Verknappung&quot; kannst du steuern, wie das Ablaufdatum eines Gutscheins für den Endkunden angezeigt wird, ohne das tatsächliche Ablaufdatum zu ändern. Dadurch wird dem Endkunden suggeriert, dass der Gutschein bald abläuft, was ihn zum schnelleren Einlösen animiert.</p><h2>Wie es funktioniert:</h2><ul><li>Du stellst das tatsächliche Ablaufdatum des Gutscheins wie gewohnt ein (z. B. 31.12.).</li><li>Sobald die künstliche Verknappung aktiviert ist, wird das angezeigte Ablaufdatum für den Endkunden regelmäßig im gewählten Rhythmus automatisch nach hinten verschoben.</li><li><PERSON><PERSON> bedeu<PERSON>, dass der Endkunde eine verkürzte Gültigkeit des Gutscheins sieht, was einen Kaufdruck erzeugt.</li><li>Der Gutschein bleibt jedoch bis zum echten Ablaufdatum gültig und verfügbar, auch wenn das virtuelle Ablaufdatum immer wieder angepasst wird.</li></ul>", "everyTwoWeeks": "<PERSON><PERSON> und Ende des Monats", "everyTwoWeeksInfo": "Das angezeigte Ablaufdatum wird automatisch zur Monatsmitte und am Monatsende nach hinten verschoben.", "endOfTheMonth": "<PERSON><PERSON> des Monats", "endOfTheMonthInfo": "Das Ablaufdatum wird jeden Monat auf das Monatsende verschoben.", "days": "Tag/e", "until": "Bis:", "virtualExpirationBetween1to100": "Virtuelles Ablaufdatum muss zwischen 1 und 100 liegen", "everyDaysInfo": "Du kannst manuell festlegen, nach wie vielen Tagen das virtuelle Ablaufdatum verschoben werden soll.", "couponImage": "Coupon-Bild", "couponImageSecondaryLabel": "(800 x 800px)", "couponImageRequired": "Coupon-<PERSON><PERSON><PERSON> wird <PERSON>", "couponImageLess221": "Name des Coupon-Bildes darf maximal 221 Zeichen lang sein", "uploadImageSecondaryLabel": "(JPG oder PNG; max. 3MB)", "initialPeriodDuration": "Laufzeit", "initialAmount": "Preis Laufzeit (€)", "recurring": "Abo", "recurringAmount": "Preis Abo (€)", "currentlySubscribed": "Derzeit aktiv", "featured": "Featured", "sureDeleteProduct": "Bist du dir sicher, dass du dieses Produkt löschen möchtest?", "successProductDeleted": "Das Produkt wurde erfolgreich gelöscht.", "successProductUpdated": "Das Produkt wurde erfolgreich aktualisiert.", "successProductCreated": "Das Produkt wurde erfolgreich erstellt.", "successProductPublished": "Das Produkt wurde erfolgreich veröffentlicht.", "successProductCreatedAndPublished": "Das Produkt wurde erfolgreich erstellt und veröffentlicht.", "successProductUpdatedAndPublished": "Das Produkt wurde erfolgreich aktualisiert und veröffentlicht.", "productManagement": "Produkt-Verwaltung", "internalNotes": "Interne Notizen", "productStatusActive": "Aktiv", "productStatusDeactivated": "Deaktiviert", "productStatusDraft": "<PERSON><PERSON><PERSON><PERSON>", "createProduct": "Produkt erstellen", "searchProduct": "Produkt suchen", "code": "Code", "uploadedOn": "Hochgeladen am", "numberOfUsagesLeft": "<PERSON><PERSON><PERSON> verbleibender Verwendungen", "successCodesUploaded": "Codes erfolgreich hochgeladen", "uploadCodesForProduct": "Codes für Produkt hochladen", "uploadPromotionalCodes": "Promo-Codes hochladen", "uploadCodes": "Codes hochladen", "uploadSingleMembershipCode": "Code für einzelne Mitgliedschaft hochladen", "subscriptionDiscountCode": "Mitgliedschafts-Rabatt-Code hochladen", "discountCodeRequired": "Rabatt-Code wird ben<PERSON>gt", "discountCodeLessThan": "Rabatt-Code darf maximal {{maxLength}} <PERSON><PERSON><PERSON> lang sein", "numberOfUsages": "<PERSON><PERSON>hl Verwendungen", "numberOfUsagesRequired": "<PERSON><PERSON><PERSON> Verwendungen wird <PERSON>", "upload": "Hochladen", "productCodes": "Produkt-Codes", "searchCodes": "Codes suchen", "sureDeleteCategory": "Bist du dir sicher, dass du die Kategorie löschen möchtest?", "successCategoryUpdated": "Die Kategorie wurde erfolgreich aktualisiert.", "successCategoryCreated": "Die Kategorie wurde erfolgreich erstellt.", "successCategoryDeleted": "Die Kategorie wurde erfolgreich gelöscht.", "categoriesManagement": "Kategorie-Verwaltung", "createCategory": "<PERSON><PERSON><PERSON>", "searchCategories": "<PERSON><PERSON><PERSON>", "categoryName": "Kategorie-Name", "categoryNameRequired": "Kategorie-Name wird <PERSON>", "categorySlug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "categoryNameLess128": "Kategorie-Name darf maximal 128 <PERSON><PERSON><PERSON> lang sein", "categorySlugRequired": "Kategorie-Slug wird <PERSON><PERSON>", "categorySlugLess128": "Kategorie-Slug darf maximal 128 <PERSON><PERSON><PERSON> lang sein", "categoryImage": "Kategorie-Bild", "categoryImageRequired": "Kategorie-<PERSON><PERSON><PERSON> wird <PERSON>", "categoryIcon": "Kategorie-Icon", "categoryIconRequired": "Kategorie-<PERSON><PERSON> wird <PERSON>", "categoryIconNameLess211": "Nam des Kategorie-Icons darf maximal 211 <PERSON><PERSON><PERSON> lang sein", "enableBanner": "Banner aktivieren", "bannerText": "Banner-Text", "bannerTextRequired": "Banner-Text wird <PERSON>", "bannerTextLess200": "Banner-Text darf maximal 200 Zeichen lang sein", "bannerRedirectUrl": "Banner-Weiterleitungs-URL", "bannerRedirectUrlRequired": "Banner-Weiterleitungs-URL wird <PERSON><PERSON><PERSON><PERSON>", "bannerRedirectUrlLess256": "Banner-Weiterleitungs-URL darf maximal 256 <PERSON><PERSON><PERSON> lang sein", "bannerRedirectValidUrl": "Banner-Weiterleitungs-URL muss eine gültige URL sein", "bannerScheduledDate": "Banner-Planungsdatum", "bannerScheduledDateRequired": "Banner-Planungsdatum wird <PERSON>", "bannerExpiryDateRequired": "Banner-Ablaufdatum wird <PERSON>", "bannerExpiryDate": "Banner-Ablaufdatum", "brandBannerBackgroundColor": "Hintergrundfarbe für Brand-Banner", "brandBannerTextColor": "Textfarbe für Brand-Banner", "brandBannerBackgroundColorRequired": "Hintergrundfarbe für Brand-Banner wird benötigt", "brandBannerTextColorRequired": "Textfarbe für Brand-Banner wird benötigt", "hexColorEqualTo7": "Hex-Farbe muss 7 Zeichen lang sein", "editCategory": "<PERSON><PERSON><PERSON> bear<PERSON>", "uploadHeaderImages": "Header-<PERSON><PERSON><PERSON>", "successImageUpload": "Bild erfolgreich hochgeladen", "uploadingImage": "Bild wird hoch<PERSON>aden", "errorUploadingImage": "Fehler beim Hochladen des Bildes", "headerImages": "Header-Bilder", "globalSetting": "Brandpage Banner", "successGlobalSettingUpdated": "Die BrandPage-Bannereinstellung wurde erfolgreich aktualisiert.", "perCategory": "<PERSON>", "allCategories": "Alle Kategorien", "exportToCSV": "Als CSV exportieren", "perBrandPage": "<PERSON>", "brandPage": "BrandPage", "addOns": "Add-Ons", "brandPageBanner": "Brandpage Banner", "allBrandPages": "Alle Brand Pages", "userActivity": "Nutzeraktivität", "exportSummarizedReport": "Zusammenfassender Bericht aller Aktivitäten aller Nutzer", "newsletterSubscriptions": "Newsletter-Anmeldungen", "exportListAllUsersSubscribedToNewsletter": "Zusammenfassender Bericht der Aktivitäten aller Nutzer mit Newsletter-Anmeldung", "reports": "Berichte", "lifetimeFreeMembership": "Lebenslange kostenlose Mitgliedschaft", "lifetimeMembership": "Lebenslange Mitgliedschaft", "initialPeriodEndsOn": "Startphase endet am", "thenSubscriptionEndsAutomatically": "Mitgliedschaft endet dann automatisch", "nextPaymentDate": "Nächstes Zahlungsdatum", "paymentRefunded": "Zahlung zurückerstattet, Mitgliedschaft temporär inaktiv", "pending": "<PERSON><PERSON><PERSON><PERSON>", "suspended": "Ausgesetzt", "completed": "Abgeschlossen", "successConsumerUpdate": "Kunde erfolgreich aktualisiert", "successSubscriptionCanceled": "Mitgliedschaft erfolgreich beendet", "emails": "Emails", "street": "Straße", "houseNumber": "Hausnummer", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "place": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Anmerkungen", "descriptionLess1000": "Beschreibung darf maximal 1.000 Zeichen lang sein", "dateOfBirth": "Geburtsdatum", "favoriteBrands": "Lieblings-<PERSON>s", "subscribedToNewsletter": "<PERSON><PERSON><PERSON> Newsletter angemeldet", "subscriptionDetails": "Mitgliedschafts-Details", "noActiveSubscription": "Keine aktive Mitgliedschaft", "productId": "Produkt-ID:", "productTitle": "Produkt-Titel:", "productSubtitle": "Produkt-Untertitel", "corporate": "Mitarbeitervorteilsprogramm (Firma)", "subscriptionCode": "Mitgliedschafts-Code", "subscriptionCancelledOn": "Mitgliedschaft beendet am:", "subscriptionEndsOn": "Mitgliedschaft endet am:", "subscriptionExpiredOn": "Mitgliedschaft läuft ab am:", "cancelSubscription": "Mitgliedschaft beenden", "back": "Zurück", "consumerDetails": "User-Details", "dashboard": "Übersicht", "forgotYourPassword": "Passwort vergessen?", "dontDispairWeveGotYou": "Kein Problem!", "passwordReset": "Passwort zurücksetzen", "youCaowUseYourNewPassword": "Du kannst dich jetzt mit deinem neuen Passwort anmelden", "success": "Erfolg", "verificationEmailSent": "Eine Bestätigungsemail zum Zurücksetzen deines Passworts wurde verschickt", "validEmailIsRequired": "<PERSON>s wird eine gültige Email-<PERSON><PERSON><PERSON>", "resetPassword": "Passwort zurücksetzen", "backToLoginScreen": "Zurück zur Anmeldung", "error": "<PERSON><PERSON>", "tokenExpired": "Anmeldung abgelaufen", "tokenInvalid": "An<PERSON><PERSON>ng ungültig", "unknownTokenError": "Unbekannter Fehler beim An<PERSON>den", "backToHome": "Zurück zur Startseite", "couponManagementSystem": "Coupon-Management-System", "loginToYourAccount": "In deinem Konto anmelden", "error404": "<PERSON><PERSON> 404", "pageNotFound": "Seite wurde nicht gefunden", "titleRequired": "<PERSON>ite<PERSON> wird <PERSON>", "titleLess50": "Titel darf maximal 50 Zeichen lang sein", "subtitle": "Untertitel", "subtitleLess50": "Untertitel darf maximal 50 Zeichen lang sein", "label": "Label", "labelLess50": "Label darf maximal 50 Zeichen lang sein", "additionalInformation": "Zusätzliche Information", "additionalInformationLess500": "Zusätzliche Information darf maximal 500 Zeichen lang sein", "descriptionLess500": "Beschreibung darf maximal 500 Zeichen lang sein", "initialPeriod": "Laufzeit", "durationMonths": "<PERSON><PERSON> (Monate)", "priceRequired": "<PERSON><PERSON> wird <PERSON><PERSON>", "hasRecurringPaymentAfterInitialPeriod": "Abo-Modell nach initialer Laufzeit", "recurringMode": "Abonnement", "invoicingIntervalMonths": "A<PERSON><PERSON><PERSON>ngsinter<PERSON>l (Monate)", "recurringAmountPositive": "Der wiederkehrende Zahlungsbetrag muss positiv sein", "specialOffer": "Banderole Spezial-Angebot", "currentSubscribers": "Aktuelle Mitglieder:", "allTimeSubscribers": "Gesamtzahl Mitglieder:", "productImage": "Produkt-Bild", "productImageRequired": "Produkt-B<PERSON>d wird <PERSON>", "productImageNameLess221": "Name des Produkt-Bilds darf maximal 221 Zeichen lang sein", "saveActivate": "Speichern und aktivieren", "editProduct": "Produkt bearbeiten", "operationSuccessful": "<PERSON><PERSON><PERSON><PERSON> er<PERSON><PERSON>", "signUpToYourAccount": "<PERSON>de dich mit deinen Zugangsdaten an ", "changeYourPassword": "Passwort ändern", "firstNameRequired": "<PERSON><PERSON><PERSON> wird <PERSON>", "firstNameLess64": "<PERSON><PERSON><PERSON> darf maximal 64 <PERSON><PERSON><PERSON> lang sein", "lastNameRequired": "Nachname wird <PERSON>", "lastNameLess64": "Nachname darf maximal 64 <PERSON><PERSON><PERSON> lang sein", "emailRequired": "Email<PERSON><PERSON><PERSON><PERSON> wird <PERSON>", "emailNotValid": "Email-Adresse ist ungültig", "emailLess100": "Email-<PERSON><PERSON><PERSON> darf maximal 100 Zeichen lang sein", "emailAlreadyExists": "Email-Adresse existiert bereits", "admin": "Admin", "contentManager": "Content-Manager", "roleRequired": "<PERSON><PERSON> wird ben<PERSON><PERSON>gt", "create": "<PERSON><PERSON><PERSON><PERSON>", "resetActivationEmail": "Aktivierungs-Email erneut senden", "editUser": "<PERSON><PERSON>er bearbeiten", "password": "Passwort", "validPasswordIsRequired": "Es wird ein gültiges Passwort benötigt", "login": "Anmelden", "forgotPassword": "Passwort vergessen?", "newPassword": "Neues Passwort", "confirmPassword": "Passwort bestätigen", "passwordsDontMatch": "Passwörter stimmen nicht überein", "signUp": "Registrieren", "changePassword": "Passwort ändern", "users": "<PERSON><PERSON><PERSON>", "consumers": "User", "coupons": "Coupons", "categories": "<PERSON><PERSON><PERSON>", "passwordCreatedSuccessfully": "Passwort erfolgreich gesetzt", "passwordChangedSuccessfully": "Passwort erfolgreich geändert", "customerPaysMonthly": "Kunde/Kundin zahlt anschließend {{recurringAmount}} EUR im Monat.", "customerPaysMonthlyRecurring": "<PERSON>nde/Kundin zahlt anschließend {{recurringAmount}} EUR über {{recurringInterval}} Monate.", "adminPanel": "Admin-Panel", "ccAdmins": "<PERSON> Admins", "priceList": "<PERSON><PERSON><PERSON><PERSON>", "customers": "User", "brandUsers": "Brand Admin", "brandPages": "Brand Pages", "brandAdmin": "Brand Admin", "amountCouponRequired": "<PERSON><PERSON><PERSON> Coupons wird <PERSON>", "pleaseInsertPositiveNumberOr0": "<PERSON>te gib eine positive Zahl oder 0 ein", "amountCoupons": "<PERSON><PERSON><PERSON>upons", "pricePerCouponRequired": "Preis pro Coupon wird <PERSON><PERSON><PERSON><PERSON>", "pricePerCoupon": "Preis pro Coupon", "amountRefreshesRequired": "<PERSON><PERSON><PERSON> Coupon-Updates wird <PERSON><PERSON><PERSON><PERSON>", "amountRefreshes": "<PERSON><PERSON><PERSON> Coupon-Updates", "pricePerRefreshRequired": "Preis pro Update wird <PERSON><PERSON><PERSON><PERSON>", "pricePerRefresh": "Preis pro Update", "freeImageVideo": "Image-Video freischalten", "freeUnboxingVideo": "Unboxing-Video freischalten", "pricePerImageVideoRequired": "Preis für Image-Video wird benötigt", "pricePerImageVideo": "Preis für Image-Video", "pricePerUnboxingVideo": "Preis für Unboxing-Video", "pricePerUnboxingVideoRequired": "Preis für Unboxing-Video wird benötigt", "brandUserGreeting": "Hallo {{userName}}!", "brandAccountIntro": "Willkommen in der traffico App. Von hier aus kannst du alle deine Inhalte auf <a href='https://captaincoupon.de/' style='color:black'>CaptainCoupon.de</a> verwalten. Öffne dazu einfach die verschiedenen Seiten im Menü am linken Rand.", "helpInfo": "Bei Fragen wende dich gerne an deinen persönlichen Customer Success Manager oder an unser Projektmanagement Team", "yourTarif": "<PERSON><PERSON>", "manageBrandPage": "Brandpage verwalten", "manageBrandPageInfo": "<PERSON>er kannst du generelle Informationen zu deiner Marke anpassen", "manageCoupons": "Coupons verwalten ({{usedCoupons}}/{{coupons}})", "manageCouponsInfo": "Hier kannst du deine Coupons bearbeiten und neue erstellen", "manageAddOns": "Add-ons", "manageAddOnsInfo": "Schalte zusätzliche Funktionen frei um mehr Kunden zu erreichen", "brandBanner": "Brandpage Banner", "brandBannerInfo": "Bewirb aktuelle Aktionen mit einem Banner auf deiner Brandpage", "information": "Informationen", "informationInfo": "Hier findest du alle wichtigen Informationen zum Couponing", "manageTarif": "<PERSON><PERSON><PERSON>", "manageTarifInfo": "Hier kannst du deinen Tarif und deine Add-ons verwalten", "comingSoon": "Coming soon", "couponUpdates": "Coupon-Updates", "imageVideo": "Image-Video", "unboxingVideo": "Unboxing-Video", "deactivateBrandPageRecommendation": "Deaktiviere Brandpage Empfehlungen", "enableCouponCountdown": "Coupon-Countdown aktivieren", "enableCountDown": "Countdown aktivieren", "reportAnalysis": "Analyse-Bericht", "reachBoost": "Reach Boost", "upgrade": "Upgrade", "requestMoreCoupons": "<PERSON><PERSON> Coupons kaufen", "requestMoreCouponUpdates": "<PERSON>hr Coupon-Updates <PERSON><PERSON><PERSON>", "requestImageVideo": "Image-Video kaufen", "available": "Verfügbar", "sendRequest": "<PERSON><PERSON><PERSON> senden", "date": "Datum", "type": "<PERSON><PERSON>", "resource": "Ressource", "historyOrChangelog": "Änderungsverlauf", "requested": "Ang<PERSON><PERSON><PERSON>", "received": "<PERSON><PERSON><PERSON><PERSON>", "used": "Verwendet", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coupon": "Coupon", "couponUpdate": "Coupon-Update", "article": "Artikel", "price": "Pre<PERSON>", "loading": "Wird geladen...", "brandAccountRequestSuccess": "Wir haben deine Anfrage erhalten und unsere Admins werden sich so schnell wie möglich bei dir melden.", "brandAccountRequestError": "<PERSON>ider gab es ein Problem mit deiner Anfrage. Bitte probiere es noch einmal und lass uns wissen, falls das Problem weitehrin besteht.", "couponsInfoHtml": "Du kannst abhä<PERSON><PERSON> von deinem gewählten Paket eine begrenzte Zahl von <b>Coupons</b> für deine Brand verwalten und veröffentlichen. Du kannst jederzeit mehr Coupons anfragen.", "couponUpdatesInfoHtml": "<PERSON><PERSON> s<PERSON><PERSON> <b>Coupon-Updates</b> für deine Brand zur Verfügung, die du für Bild- und Text-Änderungen an deinen Coupons ausgeben kannst.<br /><br />Für das Anlegen oder Ändern eines Coupons wird ein Coupon-Update ausgegeben.", "imageVideoInfoHtml": "Videos sind ein großartiger Weg, um deine Brand bekannter zu machen. Schalte diese Funktion frei, um ein Image-Video auf deine Brand Page hochladen zu können.", "close": "Schließen", "isVisibleOnBrandPage": "<PERSON><PERSON> sichtbar", "approveAndGolive": "Freigeben und veröffentlichen", "saveChanges": "Änderungen speichern", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "unsupportedFile": "Dateityp nicht unterstützt", "datenschutzbestimmungenYouTube": "Datenschutzbestimmungen von YouTube", "datenschutzbestimmungenVimeo": "Datenschutzbestimmungen von V<PERSON>o", "vimeoAktivierenModalTitle": "Vimeo aktivieren", "youtubeAktivierenModalTitle": "YouTube aktivieren", "vimeoAktivierenModalContent": "Damit Du dir diese und ähnliche Videos von CaptainCoupon über Vimeo anschauen kannst, müssen Cookies von Vimeo akzeptiert werden. Mehr dazu findest du in den", "youtubeAktivierenModalContent": "Damit Du dir diese und ähnliche Videos von CaptainCoupon über YouTube anschauen kannst, müssen Cookies von YouTube akzeptiert werden. Mehr dazu findest du in den", "invalidVideoUrl": "Ungültige Video-URL", "metaTitle": "Meta-Titel", "none": "-", "unregistered": "Unregistierte Nutzer", "brandDescriptionLessThan": "Brandn-Beschreibung darf maximal {{maxLength}} <PERSON><PERSON><PERSON> lang sein", "emailSuccessfullySent": "Email wurde erfolgreich versendet", "brandPageRequired": "Brand Page wird ben<PERSON><PERSON>gt", "createBrandUser": "<PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON>", "brandAdminSuccessfullyUpdated": "Brand Admin wurde erfolgreich aktualisiert", "brandUserSuccessfullyDeleted": "Brand Admin wurde erfolgreich <PERSON>t", "brandUserManagement": "Nutzerverwaltung", "searchUsersOrBrands": "Suche nach Nutzern oder Brands", "activeSubscriptionNotFound": "Nutzer hat keine aktive Mitgliedschaft", "productPackageNotFound": "Produkt-Paket nicht gefunden", "consumerNotFound": "Kunde nicht gefunden", "noRecurringPaymentFound": "<PERSON><PERSON> wiederkehrende Zahlung gefunden", "unknownError": "Unbekannter Fehler", "mrGender": "<PERSON>", "msGender": "<PERSON><PERSON>", "diversGender": "Divers", "noCouponsLeft": "<PERSON><PERSON> verf<PERSON>", "pleaseBuyMoreOrUpgrade": "<PERSON>te mehr kaufen oder upgraden", "noCouponUpdatesBuyMore": "<PERSON><PERSON> verbleibenden Coupon-Updates. <PERSON><PERSON> mehr kaufen", "couponRegulationsLessThan": "Einlösebedingungen dürfen maximal {{maxLength}} <PERSON><PERSON><PERSON> lang sein", "areYouSure": "Bist du dir sicher?", "couponUpdatesLeft": "verbleibende Coupon-Updates", "couponCreationsLeft": "verbleibende Coupons", "formUpdateSuccess": "Änderungen erfolgreich gespeichert", "formCreateSuccess": "Speichern erfolgreich", "formEditLink": "<PERSON><PERSON><PERSON>", "brandDetails": "Brand-Details", "bookedPackage": "Gebuchtes Paket", "freeVideo": "<PERSON>stenloses Video", "priceListForThisBrand": "PREISLISTE FÜR DIESE MARKE", "productCodesSuccessfulyUploaded": "Codes für {{fileName}} erfolgreich hochgeladen.", "invalidCredentials": "UNGÜLTIGE ZUGANGSDATEN", "internalServerError": "<PERSON><PERSON>", "brandSlugAlreadyExists": "Slug existiert bereits", "brandNameAlreadyExists": "Brand-Name existiert bereits", "productPackageAlreadyDeactivated": "Produktpaket ist bereits deaktiviert", "productPackageNotActive": "Position ist nur für aktive Produkt-Pakete verfügbar", "positionAlreadyTaken": "Position bereits in Verwendung", "invalidRecurringAmount": "Wiederkehrender Zahlungsbetrag kann für Pakete mit wiederkehrender Zahlung nicht 0 sein", "cannotChangePaymentDetails": "Zahlungsdetails können für aktive oder deaktivierte Produkt-Pakete nicht verändert werden", "codeUploadDisabledForDeactivatedProductPackages": "Codes können nicht für deaktivierte Produkt-Pakete hochladen werden", "codeAlreadyExists": "Code existiert bereits", "codeCannotBeBlank": "Code darf nicht leer sein", "subscriptionCodeLess45": "Mitgliedschafts-Code darf maximal 45 Zeichen lang sein", "numberOfUsagesForCodeCannotBeEmpty": "Ungültiges Dateiformat, Anzahl der Verwendungen darf nicht leer sein", "numberOfUsagesForCodeCannotBeText": "Ungültiges Dateiformat, Anzahl der Verwendungen darf kein Text sein", "numberOfUsagesForCodeCannotBeLessThanOne": "Ungültiges Dateiformat, Anzahl der Verwendungen darf nicht 0 oder negativ sein", "codeAlreadyExistsInList": "Code-Duplikat in der Liste^", "userDisabled": "Nutzer ist deaktiviert", "loggedInUserNotFound": "Angemeldeter Nutzer nicht gefunden", "userNotFound": "<PERSON>utzer nicht gefunden", "verificationTokenNotFound": "Verifizierungs-Token nicht gefunden", "verificationTokenExpired": "Verifizierungs-Token abgelaufen", "brandPackageTemplateNotFound": "Brand-Paket-Template nicht gefunden", "categoryNotFound": "Kategorie nicht gefunden", "primaryEmailForConsumerNotFound": "<PERSON><PERSON><PERSON><PERSON>-Email-Adresse nicht gefunden", "subscriptionForConsumerNotFound": "Keine Mitgliedschaft für Kunde/Kundin gefunden", "activeSubscriptionForConsumerNotFound": "Keine aktive Mitgliedschaft für Kunde/Kundin gefunden", "recurringPaymentForConsumerNotFound": "<PERSON><PERSON> wiederkehrende Zahlung für Kunde/Kundin gefunden", "unauthorized": "Nicht autorisiert", "accessDenied": "<PERSON><PERSON><PERSON> verweigert", "invalidRequestBody": "Ungültige Anfrage", "characterLimitExceeded": "Maximale Zeichenzahl überschritten", "brandNotFound": "Brand nicht gefunden", "couponNotFound": "Coupon nicht gefunden", "discountCodeForCouponNotFound": "Rabatt-Code für Coupon nicht gefunden", "discountCodeAlreadyExists": "Rabatt-Code existiert bereits", "brandAdminNoCouponUpdates": "Brand Admin hat keine verbleibenden Coupon-Updates", "brandAdminNoCoupons": "Brand Admin kann keine weiteren Coupons erstellen", "userRoleNotFound": "Nutzerrolle nicht gefunden", "brandPackageRequiredForBrand": "Bitte fülle die Details für dieses Brandn-Paket aus", "brandRequiredForBrandAdmin": "<PERSON>te wähle eine Brand beim Erstellen des Brand Admins", "brandPageIncomplete": "Brand Page kann wegen unvollständiger Daten nicht sichtbar gemacht werden", "productPackageAlreadyActive": "Produkt-Paket ist bereits aktiv", "successSaveBrand": "Deine Brand wurde erfolgreich gespeichert", "priceListUpdateSuccess": "Preisliste erfolgreich aktualisiert", "productMarkedAsFeaturedSuccess": "Produkt erfolgreich als gefeatured markiert", "productBranddAsNotFeaturedSuccess": "Produkt erfolgreich als nicht gefeatured markiert", "releaseDateMustBeBeforeValidationDate": "Veröffentlichungsdatum muss vor Gültigkeitsdatum liegen", "brandPackageForBrandNotFound": "Brand-Paket für Brand nicht gefunden", "brandForUserNotFound": "Brand für Nutzer nicht gefunden", "upgradeRequestNotValid": "Ungültige Upgrade-Anfrage", "couponBrandIdInfo": "Dein Coupon wird auf dieser Brand Page sichtbar sein", "couponCategoryIdInfo": "<PERSON><PERSON>hle eine Kategorie für deinen Coupon, damit <PERSON>nden ihn leichter finden können", "couponReleaseDateInfo": "Dein Coupon wird erst ab diesem Datum auf CaptainCoupon.de sichtbar sein", "couponAmountConditionInfo": "Gib hier an, ab welchem Mindestbestellwert der Coupon gültig ist.", "couponValidationDateInfo": "Gib an, wie lange dein Coupon gültig ist", "couponIsVisibleOnBrandPageInfo": "Soll dein Coupon auch auf deiner Brand Page angezeigt werden?", "couponIsCountDownEnabledInfo": "Soll ein Countdown für die Gültigkeit des Coupons angezeigt werden?", "couponRegulationsInfo": "Lass deine Kunden wissen, welche Bestimmungen für die Nutzung deines Coupons gelten, zum Beispiel den Mindestbestellwert.", "couponDiscountTypeAmountInfo": "<PERSON><PERSON><PERSON>e diese Option, wenn der Rabatt ein fixer EUR-Wert ist", "couponDiscountTypePercentageInfo": "<PERSON><PERSON><PERSON><PERSON> diese <PERSON>, wenn du einen prozentualen Rabatt auf den Wert der Bestellung geben möchtest", "couponDiscountTypeFreeInfo": "<PERSON><PERSON><PERSON><PERSON> diese <PERSON>, wenn du individuelle Konditionen für diesen Coupon festlegen möchtest:<br/><ul><li><b>Beschreibung:</b> Dieser Text wird als primäre Überschrift im Coupon angezeigt</li><li><b>Kurzbeschreibung:</b> Der Wert des Coupons, der in der rechten oberen Ecke über dem Coupon-Bild angezeigt wird, z. B. <i>25%</i>. Wenn du dieses Feld leer lässt, gilt der Coupon als kostenlos und wird mit einem Geschenk-Icon angezeigt</li></ul>", "couponDiscountCodeTypeGenericInfo": "<PERSON><PERSON><PERSON><PERSON> diese Option, wenn du einen generischen Code bereitstellen möchtest. Optional kannst du die Anzahl an Codes limitieren.", "couponDiscountCodeTypeUniqueInfo": "<PERSON><PERSON><PERSON><PERSON> diese Option, wenn du eine Liste unterschiedlicher Codes angeben möchtest. Dein Captain<PERSON>n-<PERSON><PERSON> kann dir mehr zum benötigten Dateiformat sagen.", "couponDiscountCodeTypeStationaryInfo": "<PERSON><PERSON><PERSON><PERSON> diese <PERSON>, wenn du einen Barcode als Bild hochladen möchtest. Wir betten den Barcode dann in einen Coupon zum Ausdrucken ein, den User von deiner Brand Page herunterladen können.", "couponAvailableForNewCustomersInfo": "Soll dein Coupon nur für Neukunden gelten?", "couponImageInfo": "Unterstützte Dateiformate: JPEG/PNG <br/><br/> Lade ein interessantes Bild hoch, um deinen Coupon zu präsentieren", "brandNameInfo": "Der Name deiner Brand, wie er Besuchern von CaptainCoupon.de angezeigt wird.<br/>Am besten verwendest du hier die geläufige Kurzform ohne rechtliche Bestandteile wie \"GmbH\" o. ä.", "brandWebsiteLinkInfo": "Diese URL wird Besuchern als Link auf deiner Brand Page auf CaptainCoupon.de angezeigt und sollte sie auf deine offizielle Firmen-Website führen.<br/><br/>Für Tracking-Zwecke solltest du in Abstimmung mit deinen Administratoren auch einen funktionierenden Tracking-Parameter anhängen, also z. B. <i>https://deine-brand.de?t=CaptainCoupon</i> oder ähn<PERSON>.", "brandSlugInfo": "Ein <i>Slug</i> ist ein <PERSON>, das an eine CaptainCoupon.de-URL angehängt wird, damit sie leichter gefunden werden kann.<br/><br/>Beispiel: <i>\"deinemarke\"</i> wird zu <i>captaincoupon.de/brand/deinemarke</i>.<br/><br/><PERSON>chte bitte darauf, dass dein Slug kurz und prägnant ist und keine Sonderzeichen enthält.", "brandWebsiteLinkTitleInfo": "So wird der Link zu deiner Website angezeigt. Am besten verwendest du deine URL ohne \"https\" und Tracking-Parameter.<br/><br/>Beispiel: <i>www.deinemarke.de</i>", "brandMetaTitleInfo": "<PERSON>ur<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Titel, der den Rabatt für deine Brand auf CaptainCoupon.de beschreibt. Dieser Text wird als Meta-Tag vom Typ \"title\" verwen<PERSON> und kann von Suchmaschinen erfasst und in ihren Suchergebnissen angezeigt werden. <br></br> VARIABLEN: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brandMetaDescriptionInfo": "Zwei oder drei Sätze zu deinem Rabatt auf CaptainCoupon.de, z. B. zu den Ersparnissen für deine Kunden. Dieser Text wird als Meta-Tag vom Typ \"description\" verwendet und kann von Suchmaschinen erfasst und in ihren Suchergebnissen angezeigt werden. <br></br> VARIABLEN: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brandImageInfo": "Unterstützte Dateiformate: JPEG/PNG <br/><br/> Ein Bild, das deine Brand repräsentiert, z. B. in Form exemplarischer Produktfotos.<br/><br/>Dieses Bild wird auf deiner CaptainCoupon.de-Brand-Page angezeigt und sollte im Querformat vorliegen.", "brandLogoInfo": "Unterstützte Dateiformate: JPEG/PNG Das Logo <br/><br/> deiner Brand.<br/><br/>Dieses Bild wird auf deiner Brand Page auf weißem Hintergrund angezeigt und sollte daher entweder einen transparenten oder weißen Hintergrund haben.", "brandIsPopularInfo": "<i>(Feld nur für CC-Admins)</i>Wird die Brand Page unter \"Beliebte Marken\" auf der Startseite aufgeführt?", "brandHideInfo": "Soll die Brand Page für bestimmte Nutzer nicht angezeigt werden?", "brandS1TitleInfo": "Dieser Text wird als Titel neben deinem Unboxing-Video auf deiner Brand Page angezeigt und sollte aus ein bis zwei kurzen Sätzen bestehen.", "brandS1MediaInfo": "Die Video-URL für dein Unboxing-Video, das auf deiner Brand Page angezeigt wird.<br/><br/>Wir unterstützen derzeit Vimeo und YouTube. Bei weiteren Fragen helfen wir dir gerne per<PERSON>önlich weiter!", "brandS1LinkInfo,": "<PERSON><PERSON><PERSON>, die auf den \"Mehr erfahren\"-<PERSON>ton neben deinem Video klicken, werden auf die Website hinter der hier angegebene URL weitergeleitet.", "brandS1BrandDescriptionInfo": "Ein Beschreibungstext, der unter dem Titel deines Videos angezeigt wird. Du kannst dieses Feld auch leerlassen und nur den Titel benutzen.", "brandS2BrandHeadlineInfo": "Dieser Titel-Text wird auf deiner Brand Page unterhalb der Coupons (und ggf. unterhalb deines Unboxing-Videos) angezeigt. Idealerweise enthält er deinen Marken-Namen und deinen Claim. <br></br> VARIABLEN: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brandS2BrandDescriptionInfo": "Dieser Beschreibungs-Text wird unterhalb des Titel-Texts auf deiner Brand Page angezeigt. Einige Sätze informieren Nutzer so Marke und Produkten und können im Rahmen von SEO von Suchmaschinen aufgegriffen werden. <br></br> VARIABLEN: [[brand]] [[website]] [[highest-offer]] [[exp-date]] [[month]] [[year]]", "brands3MediaLinkInfo": "Die Video-URL für dein Image-Video, das am Ende deiner Brand Page eingebettet wird.<br/><br/>Wir unterstützen derzeit Vimeo und YouTube. Bei weiteren Fragen helfen wir dir gerne per<PERSON>önlich weiter!", "blockUserInfo": "<PERSON><PERSON> der Benutzer blockiert ist, kann er sich anmelden, hat aber keinen Zugriff auf irgendetwas", "brandPricingFieldMustNotBeNegative": "Wert darf nicht negativ sein", "brandPricingFieldRequired": "Benötigter Wert", "manageBrandUsers": "<PERSON>-<PERSON><PERSON> verwalten", "incompleteBrandsMustBeHidden": "Brands mit unvollständigen Daten können nicht sichtbar gemacht werden", "brandIncompleteBannerText": "Es fehlen noch benötigte Daten. Bitte fülle alle Felder auf dieser Brand Page aus, damit du sie für Kunden sichtbar machen kannst", "brandNotApprovedBannerText": "Änderungen auf dieser Seite wurden noch nicht freigegeben. Du kannst weiterhin alle Inhalte bearbeiten und ein traffico-Admin wird benachrichtigt werden, damit er/sie deine Eingaben überprüft und veröffentlicht.", "expertModeText": "Der Experten-Modus steht dir ab dem Tarif „Professional“ und nach einer persönlichen Schulung zur Verfügung.", "maxLengthErrorMessage": "Brand-Beschreibung darf maximal {{maxLength}} <PERSON><PERSON><PERSON> lang sein", "couponPreview": "Coupon-Vorschau", "here": "<PERSON>er", "logout": "Abmelden", "brands": "Brands", "about": "<PERSON><PERSON>", "impressum": "Impressum", "privacyPolicy": "Datenschutzerklärung", "termsAndConditions": "Nutzungsbedingungen", "faq": "Häufig gestellte Fragen", "myList": "<PERSON><PERSON>", "edit": "Editieren", "BASIC": "BASIC", "PROFESSIONAL": "PROFESSIONAL", "ENTERPRISE": "ENTERPRISE", "youtubeOrVimeoLink": "YouTube- oder Vimeo-Link", "buyUnboxingVideo": "Unboxing-<PERSON> kaufen", "buyImageVideo": "Image-Video kaufen", "requestForUnboxingVideo": "Anfrage für Unboxing-Video senden?", "requestForImageVideo": "Anfrage für Image-Video senden?", "noUnboxingVideo": "Bear<PERSON><PERSON> von Unboxing-Video ist nicht möglich", "noImageVideo": "Bear<PERSON>ten von Image-Video ist nicht möglich", "recurringIntervalIsRequired": "<PERSON><PERSON> zum Abo wird <PERSON>", "unboxingVideoTitleRequired": "Titel für Unboxing-<PERSON> erforderlich", "unboxingVideoTitleLess128": "Titel für Unboxing-Video darf maximal 128 <PERSON>eichen lang sein", "unboxingVideoDescriptionRequired": "Beschreibung für Unboxing-Video erforderlich", "********************************": "Beschreibung für Unboxing-Video darf maximal {{maxLength}} <PERSON><PERSON><PERSON> lang sein", "unboxingVideoInfoHtml": "Zeige deine Produkte in einem exklusiven Unboxing-Video für deine Marke, prä<PERSON><PERSON><PERSON> von unserem Markenbotschafter <PERSON>.", "subjectUnboxingMailReq": "Änderungsanfrage bzgl. Unboxing-Video für {{brandName}}", "bodyUnboxingMailReq": "Ich würde gerne folgende Änderungen am Unboxing-Video beantragen:\n\n(Bitte gib hier deine Änderungen an)\n\n\nMit freundlichen Grüßen,\n\n{{brandAdminName}}", "requestUnboxingChanges": "Änderungen anfragen", "areYouSureYouWantToLeave": "Bist du dir sicher, dass du gehen willst?", "loggingOut": "Du wirst ausgeloggt...", "editSubscription": "Mitgliedschaft bearbeiten", "percentageShouldBeBiggerThan0": "Prozentwert muss größer als 0 sein", "amountShouldBeBiggerThan0": "Betrag muss größer als 0 sein", "releaseDateMustNotBeInPast": "Veröffentlichungsdatum darf nicht in der Vergangenheit liegen", "couponQualityFeedback": "Coupon-Fe<PERSON>back", "feedbackMax300": "Feedback (max. 300 Zeichen)", "feedBackIsRequired": "Feedback-Text wird <PERSON>gt", "lessThan300": "Bitte gib maximal 300 Zeichen ein.", "showCustomSteps": "<PERSON><PERSON>ne Schritte anzeigen", "redemptionInstruction": "Einlöse-Anleitung", "step": "<PERSON><PERSON><PERSON>", "stepRequired": "<PERSON><PERSON><PERSON> {{step}} wird <PERSON><PERSON><PERSON><PERSON>", "stepLessThan": "Schritt {{step}} darf maximal {{maxLength}} <PERSON><PERSON><PERSON> enthalten", "maxDateMessage": "Datum darf das maximale Datum nicht überschrreiten", "invalidDate": "Datum ist ungültig", "validationDateMustNotBeInPast": "Gültigkeitsdatum darf nicht in der Vergangenheit liegen", "expiryDateMustBeAfterScheduledDate": "Ablaufdatum muss nach dem geplanten Datum liegen", "couponStatusDraft": "<PERSON><PERSON><PERSON><PERSON>", "couponWebsite": "Coupon-Website", "couponWebsiteLength512": "Link zur Coupon-Website darf maximal 512 Zeichen lang sein", "couponWebsiteValidUrl": "Coupon-Website muss eine gültige URL sein", "couponWebsiteLinkInfo": "Die URL zur Website, auf der der User den Coupon-Code verwenden kann. Wenn der User <i>Code kopieren</i> klickt, wird ein neuer Button angezeigt, der zur angegebenen URL führt.", "couponStatusDeactivated": "Deaktiviert", "reason": "Begründung", "percentageShouldBeLowerOrEqualTo": "Prozentwert darf höchstens {{maxValue}} betragen", "couponRegulationsMaxParagraphs": "Bitte gib maximal 9 Paragraphen an", "lowQuality": "<PERSON><PERSON><PERSON><PERSON>", "mediumQuality": "<PERSON><PERSON><PERSON> Qualit<PERSON>", "highQuality": "Hohe Qualität", "notProvidedFeedback": "Captain<PERSON><PERSON><PERSON><PERSON> hat noch kein Feedback zu diesem Coupon gegeben.", "sureCancelSubscription": "Bist du dir sicher, dass du die Mitgliedschaft für diesen Kunden beenden möchtest?", "globalReachBoost": "DEGlobal Reach Boost", "on": "DEON", "off": "DEOFF", "globalReachBoostActivated": "DEGlobal reach boost is activated", "globalReachBoostDeactivated": "DEGlobal reach boost is deactivated", "requestReachBoost": "Reach Boost anfragen", "reachBoostInfoHtml": "Eine <i>Reach-Boost</i>-Kampag<PERSON> hilft dir, die Reichweite deiner Marke zu erh<PERSON>hen, indem du auch CaptainCoupon.de-Besuchern ohne laufende Mitgliedschaft Zugang zu den Coupon-Codes auf deiner Brand Page gibst:<br /><br /> <ol><li>Du fragst bei uns eine Kampagne für einen bestimmten Zeitraum an.</li><li>Wir stellen dir einen speziellen Link zu deiner Brand Page bereit, der allen Besuchern den Zugriff innerhalb des Kampagnen-Zeitraums ermöglicht. </li><li>Du kannst den Link in deinen Online-Werbekampagnen verwenden. Besucher, die den Link verwenden, haben dann auch ohne CaptainCoupon-Mitgliedschaft Zugang zu deinen Coupons.</li></ol><ul><li>Du kannst jederzeit eine Änderung des Zeitraums oder das Pausieren der Kampagne anfragen.</li><li>Außerhalb des Kampagnen-Zeitraums funktioniert der Link weiterhin, die Coupon-Codes sind aber nur für registrierte Nutzer mit einer Mitgliedschaft sichtbar.</li></ul>", "active": "Aktiv", "endDate": "Enddatum", "requestChanges": "Änderungen anfragen", "approveChanges": "Änderungen bestätigen", "startDateMustBeBeforeEndDate": "Startdatum muss vor Enddatum liegen", "currentSettings": "Aktuelle Einstellungen", "requestedChanges": "Angefragte Änderungen", "endDateMustNotBeInPast": "Enddatum darf nicht in der Vergangenheit liegen", "startDateMustNotBeInPast": "Startdatum darf nicht in der Vergangenheit liegen", "inactive": "Inaktiv", "noActiveReachBoostCampaign": "Du hast aktuell keine laufende Reach-Boost-Kampagne.", "noPendingReachBoostRequest": "Es gibt keine ausstehenden angefragten Änderungen für deine Reach-Boost-Kampagne.", "reachBoostActiveMsg": "Deine Reach-Boost-Kampagne ist gerade aktiv. Verwende einfach den obenstehenden Link während des eingestellten Zeitraums in deiner Online-Werbekampagne. Änderungen kannst du jederzeit über deine Account-Seite anfragen.", "reachBoostWrongTimeRangeMsg": "Deine Reach-Boost-Kampagne ist aktuell nicht aktiv, weil der heutige Tag nicht im eingestellten Zeitraum liegt. Bitte überprüfe und ändere Start- und Enddatum, wenn du den obenstehenden Link in einer Online-Werbekampagne verwenden möchtest.", "reachBoostErrorMsg": "Du hast keine laufende Reach-Boost-Kampagne. Du kannst aber jederzeit eine neue Kampagne auf deiner Account-Seite anfragen.", "copyLink": "<PERSON>", "targetUrl": "Ziel-URL", "startDateRequired": "Start<PERSON><PERSON> wird <PERSON>", "endDateRequired": "<PERSON><PERSON><PERSON> wird <PERSON>", "successReachBoostRequest": "Danke für deine Anfrage. Wir melden uns so schnell wie möglich bei dir.", "changesRequested": "Änderungen angefragt", "editReachBoost": "Reach-Boost-Kampag<PERSON> bearbeiten", "successApprovedReachBoost": "Reach-Boost-Anfrage wurde erfolgreich bestätigt", "reachBoostApproveRequestNotValid": "Anfrage zur Reach-Boost-Bestätigung ist ungültig", "noPendingRequestsForApprove": "Es liegen keine Reach-Boost-Änderungsanfragen vor", "brandPageHidden": "Reach Boost kann nicht für versteckte Brand Pages angefragt werden", "paywallDisabled": "Paywall deaktiviert", "disablePaywall": "Paywall deaktivieren", "isBlocked": "Access blocked", "isVisible": "<PERSON><PERSON><PERSON>", "loginToTraffico": "Jetzt bei traffico einloggen", "notYetACustomer": "Noch kein traffico Kunde?", "testNow": "Jetzt testen", "useCustomTheme": "DEUse custom theme", "homePageTitle": "DEHomepage title", "homePageBodyText": "DEHomepage body text", "homePageBodyTextRequired": "DEHomepage body text is required", "homePageTitleRequired": "DEHomepage title is required", "customization": "DECustomization", "backgroundColorRequired": "DEMain background color is required", "useCustomColours": "DEUse custom colours", "mainBackgroundColor": "DEMain background color", "homePageTitleLessThan": "DEHomepage title should be less than {{maxLength}} characters long", "homePageBodyTextLessThan": " DEHomepage body text should be less than {{maxLength}} characters long", "white": "DEWhite", "black": "DEBlack", "textLogoButtonsColor": "DEText, logo and buttons on colored backgrounds", "preview": "Vorschau", "brandVisibility": "Sichtbar auf Markenübersicht", "bannerEnabled": "Banner aktiviert", "blockUser": "Benutzerzugriff blockieren", "blockedMessage": "<PERSON><PERSON> ist gesperrt. Bitte wende dich an unser Customer Success Team.", "stripeBlockedMessage": "Du hast keinen Zugriff auf die Online-Tarif-Verwaltung. Bitte wende dich zur Freischaltung an unser Customer Success Team", "noActiveSubscriptionMessage": "Du hast keinen aktiven traffico Tarif?", "notLiveMessage": "Deine <PERSON> ist noch nicht live. Bitte fülle alle Felder aus und lade deine Inhalte hoch. Anschließend wird ein traffico-Admin deine Brandpage überprüfen und live schalten. Solltest du Fragen haben oder Unterstützung benötigen, schreibe uns gerne hier im Chat. Wir helfen dir gerne weiter!", "stripeCustomerId": "Stripe Customer ID", "restrictSeoFields": "SEO <PERSON> sperren", "enableCouponLink": "Coupon-Link aktivieren", "editMetaFields": "Metafelder bearbeiten", "disableNewsletter": "Newsletter deaktivieren", "restricted": "Restricted", "expertMode": "Nur im Experten-Modus verfügbar", "empty": " aa", "activate": "aktivieren", "buttonLinkDescription": "Dieser Gutschein ist ausschließlich über diesen Link einlösbar. Klicke auf ‚aktivieren', um den Gutschein direkt zu verwenden."}