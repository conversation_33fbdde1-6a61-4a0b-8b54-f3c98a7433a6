import {
  GET_CONSUMER,
  GET_CONSUMER_SUCCESS,
  GET_CONSUMER_ERROR,
  CONSUMER_CLEAR,
  EDIT_CONSUMER,
  EDIT_CONSUMER_SUCCESS,
  EDIT_CONSUMER_ERROR,
  EDIT_CONSUMER_SUBSCRIPTION,
  EDIT_CONSUMER_SUBSCRIPTION_SUCCESS,
  EDIT_CONSUMER_SUBSCRIPTION_ERROR,
} from './ConsumerFormTypes';
import * as ConsumerService from 'repository/consumer';

export const getConsumer = (id: number) => {
  return async (dispatch) => {
    try {
      dispatch({
        type: GET_CONSUMER,
      });

      const response = await ConsumerService.getConsumer(id);
      dispatch({
        type: GET_CONSUMER_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: GET_CONSUMER_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const editConsumer = (id: number, data: any) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_CONSUMER,
    });

    try {
      const response = await ConsumerService.editConsumer(id, data);
      dispatch({
        type: EDIT_CONSUMER_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: EDIT_CONSUMER_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearConsumer = () => {
  return {
    type: CONSUMER_CLEAR,
  };
};

export const changeSubscription = (id: number, packageId: number) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_CONSUMER_SUBSCRIPTION,
    });

    try {
      const response = await ConsumerService.changeSubscription(id, packageId);
      dispatch({
        type: EDIT_CONSUMER_SUBSCRIPTION_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: EDIT_CONSUMER_SUBSCRIPTION_ERROR,
        payload: error.response?.data,
      });
    }
  };
};
