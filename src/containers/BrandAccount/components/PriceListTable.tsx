import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import React from 'react';
import { useTranslation } from 'react-i18next';

const CURRENCY_FORMAT = new Intl.NumberFormat(undefined, {
  style: 'currency',
  currency: 'EUR',
});

export interface Props {
  pricePerCoupon: number;
  pricePerCouponUpdate: number;
  pricePerImageVideo: number;
  pricePerUnboxingVideo: number;
}

interface PriceRowProps {
  name: string;
  price: number;
}

const PriceRow = ({ name, price }: PriceRowProps) => (
  <TableRow>
    <TableCell scope="row">{name}</TableCell>
    <TableCell scope="row">{CURRENCY_FORMAT.format(price)}</TableCell>
  </TableRow>
);

export const PriceListTable = ({
  pricePerCoupon,
  pricePerCouponUpdate,
  pricePerImageVideo,
  pricePerUnboxingVideo,
}: Props) => {
  const { t } = useTranslation();

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t('article')}</TableCell>
            <TableCell>{t('price')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <PriceRow name={t('coupon')} price={pricePerCoupon} />
          <PriceRow name={t('couponUpdate')} price={pricePerCouponUpdate} />
          <PriceRow name={t('imageVideo')} price={pricePerImageVideo} />
          <PriceRow name={t('unboxingVideo')} price={pricePerUnboxingVideo} />
        </TableBody>
      </Table>
    </TableContainer>
  );
};
