import { Paper, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import React, { ReactNode } from 'react';

const useStyles = makeStyles({
  title: {
    marginBottom: 8,
  },
  card: {
    padding: 16,
    marginBottom: 48,
  },
});

interface Props {
  title?: string;
  children: ReactNode;
  className?: string;
  style?: any;
}

const Card = ({ title, children, className, style = {} }: Props) => {
  const classes = useStyles();

  return (
    <div className={className} style={style}>
      {title && (
        <Typography variant="h6" component="h4" className={classes.title}>
          {title}
        </Typography>
      )}
      <Paper elevation={3} className={classes.card}>
        {children}
      </Paper>
    </div>
  );
};

export default Card;
