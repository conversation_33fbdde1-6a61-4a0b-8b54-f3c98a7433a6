import * as actionTypes from './CouponTypes';
import * as CouponService from 'repository/coupon';
import * as RoleManager from 'services/role';

export const changeFilter = (filter) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.CHANGE_PAGINATION,
      payload: {
        page: filter.page,
        pageSize: filter.pageSize,
        sort: { ...filter.sort },
      },
    });
  };
};

export const getCoupons = (filter = {}) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.GET_COUPONS,
    });

    try {
      const response = await CouponService.getCoupons(filter);

      dispatch({
        type: actionTypes.GET_COUPONS_SUCCESS,
        payload: response.data.data,
      });

      dispatch({
        type: actionTypes.COUPONS_PAGINATION,
        payload: {
          current: response.data.number + 1,
          first: 1,
          last: response.data.total,
        },
      });
    } catch (error) {
      dispatch({
        type: actionTypes.GET_COUPONS_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getCouponsAsBrandAdmin = (filter = {}) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.GET_COUPONS_AS_BRAND_ADMIN,
    });

    try {
      const response = await CouponService.getCouponsAsBrandAdmin(filter);

      dispatch({
        type: actionTypes.GET_COUPONS_AS_BRAND_ADMIN_SUCCESS,
        payload: response.data.data,
      });

      dispatch({
        type: actionTypes.COUPONS_PAGINATION,
        payload: {
          current: response.data.number + 1,
          first: 1,
          last: response.data.total,
        },
      });
    } catch (error) {
      dispatch({
        type: actionTypes.GET_COUPONS_AS_BRAND_ADMIN_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const deleteCoupon = (coupon) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.DELETE_COUPON,
    });
    try {
      let response = { data: {} };

      if (RoleManager.isAbleTo('coupon', 'delete')) {
        response = await CouponService.deleteCoupon(coupon);
      } else if (RoleManager.isAbleTo('brand_coupon', 'delete')) {
        response = await CouponService.deleteCouponAsBrandAdmin(coupon);
      }

      dispatch({
        type: actionTypes.DELETE_COUPON_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: actionTypes.DELETE_COUPON_ERROR,
        payload: error.response?.data,
      });
    }
  };
};
