import React, { useState, useCallback } from 'react';
import SearchIcon from '@material-ui/icons/Search';
import { Grid, Button, TextField } from '@material-ui/core';
import { PageTitle, BoxWrap } from 'components';
import { Link } from 'react-router-dom';
import * as RoleManager from 'services/role';
import { debounce } from 'utils/debouncer';
import { useTranslation } from 'react-i18next';

import './BrandPages.scss';

export const BrandPagesView = ({ onFilterChange, filter, children }) => {
  const { t }: { t: any } = useTranslation();
  const [userQuery, setUserQuery] = useState('');
  const delayedQuery = useCallback(
    debounce((q) => {
      onFilterChange({
        ...filter,
        search: q,
      });
    }, 500),
    [filter]
  );
  const onChange = (e) => {
    setUserQuery(e.target.value);
    delayedQuery(e.target.value);
  };

  return (
    <div className="brand-page">
      <PageTitle title={t('brandManagement')}>
        {RoleManager.isAbleTo('brand_page', 'create') && (
          <Button
            component={Link}
            to="/dashboard/brand-pages/create"
            color="primary"
            variant="contained"
          >
            {t('createBrand')}
          </Button>
        )}
      </PageTitle>
      <BoxWrap>
        <BoxWrap.Toolbar>
          <Grid container spacing={1} alignItems="flex-end">
            <Grid item>
              <SearchIcon />
            </Grid>
            <Grid item>
              <TextField
                id="input-with-icon-grid"
                label={t('searchBrands')}
                onChange={onChange}
                value={userQuery}
              />
            </Grid>
          </Grid>
        </BoxWrap.Toolbar>
        {children}
      </BoxWrap>
    </div>
  );
};
