import * as CategoriesTypes from './CategoriesTypes';

interface Category {
  id: number | string;
  [key: string]: any;
}

interface CategoriesState {
  loading: boolean;
  error: null | any;
  homepageCategories: Category[];
  homepageCategory: Category | null;
}

const INITIAL_STATE: CategoriesState = {
  loading: false,
  error: null,
  homepageCategories: [],
  homepageCategory: null
};

const CategoriesReducer = (state: CategoriesState = INITIAL_STATE, action: any) => {
  switch (action.type) {
    case CategoriesTypes.GET_CATEGORIES:
    case CategoriesTypes.GET_CATEGORY:
    case CategoriesTypes.CREATE_CATEGORY:
    case CategoriesTypes.UPDATE_CATEGORY:
    case CategoriesTypes.DELETE_CATEGORY:
      return { ...state, loading: true, error: null };
    case CategoriesTypes.GET_CATEGORIES_SUCCESS:
      return { ...state, loading: false, homepageCategories: action.payload };
    case CategoriesTypes.GET_CATEGORY_SUCCESS:
      return { ...state, loading: false, homepageCategory: action.payload };
    case CategoriesTypes.CREATE_CATEGORY_SUCCESS:
      return { ...state, loading: false, homepageCategories: [...state.homepageCategories, action.payload] };
    case CategoriesTypes.UPDATE_CATEGORY_SUCCESS:
      return {
        ...state,
        loading: false,
        homepageCategories: state.homepageCategories.map((cat: Category) => cat.id === action.payload.id ? action.payload : cat)
      };
    case CategoriesTypes.DELETE_CATEGORY_SUCCESS:
      return {
        ...state,
        loading: false,
        homepageCategories: state.homepageCategories.filter((cat: Category) => cat.id !== action.payload)
      };
    case CategoriesTypes.GET_CATEGORIES_ERROR:
    case CategoriesTypes.GET_CATEGORY_ERROR:
    case CategoriesTypes.CREATE_CATEGORY_ERROR:
    case CategoriesTypes.UPDATE_CATEGORY_ERROR:
    case CategoriesTypes.DELETE_CATEGORY_ERROR:
      return { ...state, loading: false, error: action.payload };
    default:
      return state;
  }
};

export default CategoriesReducer;
