import React from 'react';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableFooter from '@material-ui/core/TableFooter';
import TablePagination from '@material-ui/core/TablePagination';
import TableRow from '@material-ui/core/TableRow';
import TableHead from '@material-ui/core/TableHead';

import IconButton from '@material-ui/core/IconButton';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import { Button, DialogContent } from '@material-ui/core';
import DialogTitle from '@material-ui/core/DialogTitle';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Sorter } from 'components/Sorter/Sorter';
import * as RoleManager from 'services/role';

enum Status {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  EXPIRED = 'EXPIRED',
  DRAFT = 'DRAFT',
  DEACTIVATED = 'DEACTIVATED',
  SOLD_OUT = 'SOLD_OUT',
}

const TRANSLATION_KEYS_BY_STATUS: Record<Status, string> = {
  ACTIVE: 'couponStatusActive',
  PENDING: 'couponStatusPending',
  DRAFT: 'couponStatusDraft',
  DEACTIVATED: 'couponStatusDeactivated',
  EXPIRED: 'couponStatusExpired',
  SOLD_OUT: 'couponStatusSoldOut',
};

export const CouponTable = ({
  coupons,
  pagination,
  filter,
  onFilterChange,
  onDeleteCoupon,
  children,
}) => {
  const { t }: { t: any } = useTranslation();
  const [page, setPage] = React.useState(filter.page);
  const [selectedCoupon, setSelectedCoupon] = React.useState();
  const [rowsPerPage, setRowsPerPage] = React.useState(filter.pageSize);

  const [delConfOpen, setDelConfOpen] = React.useState(false);

  const handleDelConfClose = () => {
    setDelConfOpen(false);
  };

  const handleDelConfOpen = (id) => {
    setSelectedCoupon(id);
    setDelConfOpen(true);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);

    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };

  const getEditRoute = (couponId) => {
    if (RoleManager.isAbleTo('brand_coupon', 'view')) {
      return `/dashboard/brand-coupons/${couponId}`;
    } else {
      return `/dashboard/coupons/${couponId}`;
    }
  };

  const handleSort = (fieldName) => (direction) =>
    onFilterChange({ sort: { field: fieldName, dir: direction } });
  if (coupons.length === 0) return <h2>{t('noResultsFound')}</h2>;
  return (
    <TableContainer>
      {children}
      <Table aria-label="custom pagination table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter label={t('id')} handleSort={handleSort('id')} />
            </TableCell>
            <TableCell>
              <Sorter label={t('brand')} handleSort={handleSort('brandName')} />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('category')}
                handleSort={handleSort('categoryName')}
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('validationDate')}
                handleSort={handleSort('validationDate')}
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('amountCondition')}
                handleSort={handleSort('amountCondition')}
              />
            </TableCell>
            <TableCell>
              <Sorter label={t('countDownEnabled')} handleSort={handleSort('isCountDownEnabled')} />
            </TableCell>
            <TableCell>
              <Sorter label={t('status')} handleSort={handleSort('status')} />
            </TableCell>
            <TableCell>{t('actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {coupons.map((row) => (
            <TableRow key={row.id}>
              <TableCell scope="row">{row.id}</TableCell>
              <TableCell scope="row">{row.brandName}</TableCell>
              <TableCell scope="row">{row.categoryName}</TableCell>
              <TableCell scope="row">
                {new Date(row.validationDate.slice(0, 10)).toLocaleDateString(
                  'en-GB'
                )}
              </TableCell>
              <TableCell scope="row">{row.amountCondition}€</TableCell>
              <TableCell scope="row">{row.isCountDownEnabled ? t('yes') : t('no')}</TableCell>
              <TableCell scope="row">
                {t(TRANSLATION_KEYS_BY_STATUS[row.status])}
              </TableCell>

              <TableCell component="th" scope="row">
                <Link to={getEditRoute(row.id)}>
                  <IconButton aria-label="edit" color="default">
                    <EditIcon />
                  </IconButton>
                </Link>
                <IconButton
                  aria-label="delete"
                  color="secondary"
                  onClick={() => handleDelConfOpen(row.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={pagination?.last || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>

      <Dialog
        open={delConfOpen}
        onClose={handleDelConfClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteCoupon')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('warningNoUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleDelConfClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              onDeleteCoupon(selectedCoupon);
              handleDelConfClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>
    </TableContainer>
  );
};
