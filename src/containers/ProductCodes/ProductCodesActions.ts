import * as productCodeActionTypes from './ProductCodesTypes';
import * as ProductService from 'repository/product';

export const uploadCode = (id, code, numberOfUsages) => {
  return async (dispatch) => {
    dispatch({
      type: productCodeActionTypes.UPLOAD_CODE,
    });

    try {
      const response = await ProductService.uploadCode(
        id,
        code,
        numberOfUsages
      );
      dispatch({
        type: productCodeActionTypes.UPLOAD_CODE_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: productCodeActionTypes.UPLOAD_CODE_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const uploadCodesFromCSV = (id, formData) => {
  return async (dispatch) => {
    dispatch({
      type: productCodeActionTypes.UPLOAD_CODES_CSV,
    });

    try {
      const response = await ProductService.uploadCodesFromCSV(formData, id);
      dispatch({
        type: productCodeActionTypes.UPLOAD_CODES_CSV_SUCCESS,
        payload: response.data.fileName,
      });
    } catch (error) {
      dispatch({
        type: productCodeActionTypes.UPLOAD_CODES_CSV_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getProduct = (id: number) => {
  return async (dispatch) => {
    dispatch({
      type: productCodeActionTypes.GET_PRODUCT,
    });
    try {
      const response = await ProductService.getProduct(id);
      dispatch({
        type: productCodeActionTypes.GET_PRODUCT_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: productCodeActionTypes.GET_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const changeFilter = (filter) => {
  return async (dispatch) => {
    dispatch({
      type: productCodeActionTypes.CHANGE_FILTER,
      payload: {
        page: filter.page,
        pageSize: filter.pageSize,
        sort: { ...filter.sort },
        search: filter.search,
      },
    });
  };
};

export const getProductSubscriptionCodes = (id, filter = {}) => {
  return async (dispatch) => {
    dispatch({
      type: productCodeActionTypes.GET_PRODUCT_SUBSCRIPTION_CODES,
    });

    try {
      const response = await ProductService.getProductSubscriptionCodes(
        id,
        filter
      );

      dispatch({
        type: productCodeActionTypes.GET_PRODUCT_SUBSCRIPTION_CODES_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: productCodeActionTypes.GET_PRODUCT_SUBSCRIPTION_CODES_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearProduct = () => async (dispatch) => {
  dispatch({
    type: productCodeActionTypes.RESET_PRODUCT,
  });
};

export const clearProductSubscriptionCodes = () => async (dispatch) => {
  dispatch({
    type: productCodeActionTypes.RESET_PRODUCT_SUBSCRIPTION_CODES,
  });
};
