const montserratUri =
  "data:application/font-woff;charset=utf-8;base64,d09GRgABAAAAADcoAA8AAAAAWfwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAA3DAAAABwAAAAcdZeN+0dERUYAACvwAAAAHQAAAB4AJwDdR1BPUwAALDAAAArZAAASns1+IxFHU1VCAAAsEAAAACAAAAAgbJF0j09TLzIAAAHQAAAAVQAAAGClvIRNY21hcAAABFQAAAF/AAAB0mFENJRnYXNwAAAr6AAAAAgAAAAIAAAAEGdseWYAAAeEAAAhxwAAOiB2QEsOaGVhZAAAAVgAAAA2AAAANgKxqspoaGVhAAABkAAAACAAAAAkB2kD3WhtdHgAAAIoAAACKwAAA1zoZy08bG9jYQAABdQAAAGwAAABsKtJuhhtYXhwAAABsAAAAB4AAAAgAR4AOG5hbWUAAClMAAABMQAAAj15gqPLcG9zdAAAKoAAAAFlAAAB3fQxPnkAAQAAAAIAQq6oQoFfDzz1AAsD6AAAAADMj2Q6AAAAANNkArj/kv8qBAwDhwAAAAgAAgAAAAAAAHjaY2BkYGA+8Z+VgYFF6/+k/9dYeBiAIijgOgCKbgZNeNpjYGRgYLjOYMrAygACTEDMyAAScwDzGQAehgFXAAB42mNgZvJlnMDAysDA1MUUwcDA4A2hGeMYjBiNgKIMrEA5IMXSwMCg78Cg4MUABQWVRcUMDgy8SkLMJ/4D1TCfYPitANQIkmMSYdoNpBQYmAAmNwwpAAAAeNptks1LVFEYxp/3OQNlSMUUYqJITDajmZ8zw4RT6Fg0qQMzQ19QYdomqoW1EPraCEmUSfQPNClICwlXEdEi2rYLIpNAV+E2wtCQpudeR5ikCz+ec+8577n3/N5rQfiXZUoZLBFGn40iw0Uc4nU0uCCa+RrtWEbGBtAh9tscjjKBNtuHlH1D2qqRYj9qOYcuu486ZtDBFMJMooG9Gt9AknG08B6OaM9Oa1BNBKeYxWGOaC6LAVvHAfcQnfwO8AOGuISo26mcFBfFiu4rMGQJjcOo53tlHEPuizgu0ohytpTzmhtFRHX1DjjPd3CBalTyBXZxpLjKabTaFT3fgVploz1CFT8h7XosqXM0soAcu/W9BbTymtaOaa9JtHEcOTnqtWDxGU/445z7pbV6zgl/fc6rsSea+y1PU6obR788bHdPEWQL9vAYKliHZssiYoZzyihj8npQPvrQzqvo4k85+YiQ7cUFDsvJMGZcN+J2WXMhJFwSvf79S/kuIO73Ylq9mEBcZ8hzDGf5XPUhnFSPmqxSZ7yDhGpTbhAhVxAP9P51xLgg357zhX8J7C6ueD3w/ZdhieIPrwfKNaVzVajZ9L8VnavNH6sH5fg9eKX9HsuX5/s/uDfKng3/5cj9sudduSRWeVr/16b/rciLn14PyvF64PVK6e0VaEJMtHAKM3YbWW7DIGuQ52fkMY8Qvm5gt8TNErM4w7dyfgkJkeLd4h+u6cx6519kmZkiAHjaY2BgYGaAYBkGRgYQOAPkMYL5LAwbgLQGgwKQxQFk8TLUMaxh+M9oyBjMdIzpFtMdBREFKQU5BSUFNQUrBReFNUpC//+DzeAF6lnIsA6oMgiqUlhBQkEGrNISrpL5//f/T/8f/n/o/8T/hX///33z9/WDrQ82Pdj4YN2D1Q9mPpjwQPPeEairiAKMbBCvgNlMQIIFXQEDAysbOwcnFzcPLx+/gKCQsIiomLiEpJS0jKycvIKikrKKqpq6hqaWto6unr6BoZGxiamZuYWllbWNrZ29g6OTs4urm7uHp5e3j6+ff0BgUHBIaFh4RGRUdExsXHxCYlIyQ1t7Z/fkGfMWL1qybOnylatXrVm7ft2GjZu3btm2Y/ue3Xv3MRSlpmVdqFhYkMNQls3QMYuhmIEhoxzsutwahhW7GlPyQey82osMTa3TDx85cfLsuVOndzIcZLhy9dJloEzlmfMMLT3NvV39Eyb2TZ3GMGXO3NmHjh4vZGA4VgWUBgDCCoQJAAAAAAAAAAAAAAAAAAAAABwALgBgAKIA4AEoATQBTAFkAYYBmgGwAbwBzgHcAfwCDAIyAlgCdAKcAtAC5gMaA04DbAOOA6IDtgPKBAIETgRoBKAExATkBPwFEgU2BU4FXAV4BZQFpAW+BdYF9gYWBkgGbAaeBrAGzAbeBvwHGgcwB0oHXAdqB3wHjgeaB6gH1gf6CBgIPAhkCIQItgjWCO4JEgkqCTYJZgmGCaQJyAnqCgQKOgpaCnoKjAqoCsIK4gr6CyYLNAtgC4ILngvOC/QMKAxQDGQMsgzQDQgNNA1ODV4NnA2qDcgN5A4IDiwOOg5aDnoOjA6qDroO2A7yDxwPUg+QD8gP7BAOEDYQaBCcEMwQ8hEsEUwRbBGOEbwR0BHkEfwSIBJGEnQSnBLEEvATJhNcE3YTthPaE/4UJhRaFHgUmhTWFQoVPhV2FbgV/BZCFowWwBbwFx4XUBeOF6IXthfOF/IYKhheGIQYqBjQGQIZNhlaGZYZvhnkGg4aRBpsGpwa1BriGwobTBteG3wbmBukG7AbxhvcG/AcFBw4HFocbByUHKYcthzGHQIdEHjalXsHfFRV9v87d5KZYGiTzGSAFDJ5yaTXaem9F0hCOulASEISBhIGpDfpGpo02R+xLLiiYl1QFERlFxErSHH3r66uKMK67C4ouuTld+59b5LJBH5+/iR5w0zuO+1+T7nnvHCEyx+4w31JznHO3BiOc9erZbzMTWcwxXio5FO2+BT7BIWFJYWFkXOrhN3Q1v8cfZMUxhGuCE5DMDnOyTgF3mfUq/X4UzQldQqc/gn/cRyuCcXLq0jbk5vMccAr9ApewZvZj1nPfvQK9oOf68mBDu8lHgu95nrt8LJ4zfaY6T3fY673XO8d3pbJlps7fHbAKwe7+vBf18GuZ/Bf10HkIeMCBo6RJHKb8+L8uCCOczbqAs0eGmMEGExmlMhDo9AFqvU+oJIr1MlgwDdErsIF4LmxM6+sel7bg02pXc1p6UWy7o6lLVGxYYmd+qDY3LyxDZV5Y9/2rHquyzlG75JRG+n6hmftkS7n3GMR2cFur8C18IxAt+dQBLRc2MAd8h3qqeBcOXfUlsdPVXI/akZn4MHJoPNDnjG2z+TSe6ipXFVRuQreFrhVhBOO+kX7+kUTV/ahXHwzd2193dp6ZX8WOf58YnBwYrDwE/vkFHtD9Y8e+JH0MRvzXBgK46czGswRYKRbqFYpPDQms0aOL4pAub9HjFkXKFe7UQOYjAYdeDdveipYa+2z9BXHTFaWTtIGJRfUVgkDhctzZiXM6zV6aucZXTob5lVFJkYUV5Tmz6hqNkQkxWcZguOzp5fsi4tvz03Kg1dCYsODYqOCAnGTKS64xxguGCqKGB7w85iBJRCK++TKcW7InmcbpJc/Xhy/MLqkJHphPBy++ktJ5t1f72aWUDpaXD9JXG/CXRN3kDcdHlx/2m45rk+CpdCNfJX4JlDDmxWB5kCzJlCvMGsUGihpcMl0afBrafGrV2Qq6v1mz4bnU+d3p8brYxJS5s1Ljo/RI40obhdxIV2Up9nIGymi9Wpe/c+TJ2tOntx1ov6NN+pPUF7pA7u5S9wW7gFcx1xlHPBmXXpYYrhnYnxAcJpvaGhi6IxWd7o2l/sJdkIDtYfZqFXnQuFPbW2Mxh2k8TD1HpPobn7poclhYcn+yaH4ynTyGthMomWh9F7QI5A+mClMnSkLvduIvpWAmFvAMDeOWTQGreRHxpt8PcbLfZ+b9kJJyQunHwGfRx4RvoZLP2ztvdErVED+6dPCH08z2t548UR7OVOvV/JK3luwPEKOI9JK2e9j0HcnIH01xzaA91MEJgOFlHwcaNVaoxnKE1OjK+IaIuYeWRtb8BQkCX4XYFVpWUKVOSt7bqOpuejQvJmfMVqBAz+TUchLQzGhVSt8AMn4oZ/qY0TSRnMR7P1DY8e2v3fGVNQnTa2aEuj/BCltq31T1l08Z3FeQ2LcnFTXmjeQVgDKpUZaY5GwtD+8UWsEvQxKLGVlFuECwKp+8Fy5cs4LL7TCk0IjJ+qD9nInb3AT8I1aqzbpqcl5FMKHMNMpApVwVbgSFtv8yM6Oh8tSXDzLZpZWPpQYQ96Y8+wE69mH6itcPAISu1O0JihG+ycivThyBnWisc1PrkgGG0n0cbMpRqOIABpqKHVorquCifVx9Ss2tj6d6h8RNOWb2gRNkaVkinVKLqlfPscretlHy3dVXN4aUppTqfIV7mbMKcqaU4ByR6C+k1BfF9F2iAJeHQE3hMurQXsH/v1eW//zpLieY7EwDmXKIO8hIphUbiiPgu4Yiwk6gxtTVK6yvV45u65g1tYfts4qONpaW2WpqmspKGgpIO+1P56c2lOzeHFNT2ry4/0DCV2JiV1AkqoTE6s5Ftf1yCfKpntAjMk8Fnga2fQxGrSAgm6tD7iJTDzggfvpvqAwq3mE6uBEVe/Ip3xy0Uf+H6wWMW7zEiK95jJvCbXzGdDaXAfvzUMf/RLvfYBzG+al46Xb88KSwj2T0FsZgXR02fiIGa1qcB6kAVw4t4A4kYNihgtQO+NXOFwS+qARpiz/fP4B1yfnM16J3E/EQMZLuRC9g34lwh3Bhf6Q8fX19YxeIdLjRXqgdjYG4BfhhWBoRJLnll+Z/6TrgfmUnv/Af+HP5KyYSQJ4jOUpwKyp4EUfVIyVKeRqIuVpiJjn7puniSrUOz34wMKXLFp3lVajcKuICE+JCL+SFBTomzcxPzoysHpWU2Vi0ATVZI2LR5BwOzQpDL/F/VwtqyaPY77WswxCo4iaQhmzhbSxmDH1ShX6K6YNX/l49FyzZiy4iYnMBCee/uZHKPmuNDM6nsQuqViztCWi1K3RPFl/Svjo4tlHF6V68D35hYvz85bnLf8Kiv/5z2c7HspIXrXpwtYobYbwZPb0/L4vLgoX/5wY4BMA58oqltVNR7moca8i9hUs0ip4GgQjwRXezV4OBvqdHbVixYEDNGod6ARf4SvcgSa8pxfvkfJwMCi1KDyzGbDAo5Cjo6IWoaBk6oQC7BA+hPE5sydGGdzHuqUVxSQIr+5amZy2+7W0rEefIcf9vE2ZpUFjFS5jq9PzYtNgb2VMmDA5OK6O7qkBfeE4xkl30RM0cl9qPur8PEN/X1JVVOFc4Z1PPoOszoKY6sT2rb1zgsOnLDkHsR8vKo3Sbfysl+4BlfsS0xWxzmEQp/KBQctTKcmYs++e/f4mjBP+YV2zYj05flR4/QQ5Ljx5U9A+KGKV3l+F97uK8U36UpJCYTNMFV6EVQJG4LmvtR2fi4uk9Xr87yhELFvJK7WKS2ASzlrhUyBFb3UKX5HjXZy9jiqah9QUEvYqxphJlnXGscdQw4iZKcHTzO1bt/w5A8r+2UlVrKrhPTd+uT7UxvNpUUa9Uq/UKmni0SqbrDDdahV+jwp9ApH9WeAjfG3Tibsk1RK4tMlqpVst6jt54Fc4h78bx+KiUozlNKEYlYugbGnT4oqMzLSINFLSLugXL66oS0tOhjZJhhdFGZAm6EcBL1OokTak7hK+A+8t37cgF+EwlAsfCgshpuvdIft6iPmSya7GO1JQnFctg7+X4Y3caPw97pknUN2wmL5kPXb+/FHrrfT/INU8OIoJ1k/whG9tNL8R94AuB7xDD6TTulLosVphF1UWBIHgq1hPGwZ+kalsOZ8TXc9AGOA8xpPRH/f2ftw7XThz4QLEXiCNO8/v3HleCP8IjB99JLxvw9hqxk9JMaZCjKmRJY3UDG03tz+6b6n15rycmsZFG8nxxYL1AxR6WcG2ROF7kX+3LJfVBFg9OCsGA4WC2h2hj8Bwk6SCl8ftZ3Isb/Mz6PLcMyotqdY0JqEcmEj7FrwaFB7kW1ebS37PRBXjNpXxCLMjRZtKoeEDMcNTJ5YzMdV60rv76K5Dj1nBqW/Tqdz8hl074PJz4cL16+R4xy/JG/W/sv0IQsxmoaw++E6u8iFDADFEkEAppiGEAdLTL3Vusjy1Ir99flxGyr7iyckPt2asjUqZ3E4aQsrDDfVH58+rro0uCQ+YPDHy7bawoGovfwmDKGsS81mO7h1P83Ob9QLxIVP7XyRTOzpwTdXALwzzuGP+FJwYRTFEsFeIeKrRelC4e9DaammENcKi3U8+uRs2CMsa8T7EPHkN75NznBYDH+IJ+tYK3629dGktxQN5ANcg1pxG4xo8uQW40yWIN/qlh4T97ftfWHFrwQ6XHQturXgXfhZGwc+Ipi8FLXV/vBflIdvFe7X0JneZXoNnMZk7DDzr/OyOm19u3++8f/uXN0Eh/AKzoezGDeFZYS/TG7MyRxaJNQnGJ1E46Fkk3F118uQq4S6o4JDwNiQL1cgnEtdapLikpQaiwcYI8CfBSiKEdtjffxJ2o61qBaWsczr8i2M264F4rP1ZnkSA4g/Eb9r0Nbld1h9YJlvFamO83MA1LDYA7zUTOmaS2/2UDReM97uK9+MZBPdFrwz++utN5PGyuyvKyBWmvwe5CnNlq6l9IRBPpHoNKIuKmha3PEiuTpu2fz+r3QesZNPAEcpDQ2t3kr4pLo4DSMRI60WusbqcDzQTr5bFVlLVGE3xG4y444gX+ogPtY5RTKAJeCCjJTRFnVph5P3U1EsI15I+d1pnz4xt1vzimKYl9Qstyx9MnFOcM7X+d1NU7absooaWbeGFgcGJom+UIO155Co7ZTL/9Quk5lGaiFj+y+HKnt3vFVss6Zm1a5sb19USr50/7asqJleFT2YJjzc17p01i+oVgXS8UcbxLNsbaK0qJnL0YUia23fo5rT0rC2zWx5NT29y2X1r77SSxLodLZm5zShDJN47FWVg/unBbqZhHPXUSUKc3rXvszSLJXnh5ua1tbXrGl323NlZA/79k4qr3mls2tPSwnShdFKZDB6IXq0P0IpCNBaaKEBLKZFUocGpOWWmIbVik+V3fVf2CA0QXbak+Up8Tkp29aP/3bmm2zUjt0D0d7zsI7eoZdzpqYD5o95oU+4vW7ZYMjJm9SZEaHODSGpYjZANr9cEl80wTuXDqTxRAyHoy15ibAOleD6JMSWBVNvGaIycpCBJ2nuzyTjlQN3Euvj2DRvOpwuflGwsr9hQQlRC1MMPF5QnlNZkRsbu/OvOyqnCew21GxuaxXiBJ1WSQL6lFak7jfVKdmZQ438RHPJTFsuEptiauZbF8RW0mCHfCh9Gx3a1Cq+D/44iKmMGUvmE/Iy4cxWzjFQA+uGty0PiQ0LiiWoFfQlheBnYPhDGfYTrGV40LPSIWtlq6BILWsTgl+U/nt09kYT3f4QmKdOGkfODdES5YxnuGFelWa9w5wMVyPXy1rNXNvxPCQKs8Ovzwslrpbb13ClcLxPX4zp8I34uO4w29pH0V1P9NSY7Gyjoq85mi+XhU63UHnHlxGLqEY2iaoqObW2VrJKXLVwG/8KcInv7eg3aV+1oX88Gs519Kakh86J9qV/U4/3ieUM6JTmJkFT1b29o2N4Avjf7+m721Tbta2rad/rA1QMHrtp8Mxl908veN5kIv+Gbwo/kYt2Qb1K/SEb/kuiI/kVDmFJuc68/7d5zMd1iKb4pede+n3bOqusPAbeqd9G7kAizA3QhjTHsZOoNoinMGj9onVaGVqgzgAwOd0z/O1HNiUmFeWK/5Q7WU160ZvZnlb9YTBkCdYE6yYvQhmqVxkem8YBzXRXxy6JMdZW6sHYXp4Pdx0pD0gqDYia5pcVEepqmTQ/RVWbznkG+Mghvy682TQv19p40etT48So3fzE3o68eZ76qFMMOHlGVto7LX4JytREJvbMyMixb4HXh37pwfqpxRhm41hw6VENlLcB9MhIV3ecAFnyYj7KcijLDabuNvgW+/TfYPkO28Fe2z0DzFp7XVDT262Usb35xseeJJ3ouogN3wfb+G7gGacuW45rRNIqzRWJ+hfyVx3rWrqXfx1YuF28Qb4IOSht1Ip54H60vNTSfoqNgcj043/rQ629vWrF0zZsvE9WRI8Jd4eaLL8I4cMZ7Rg2EETPeg/FYIzqo0cBYYvDZmZeg5wsiLy06cGCRIGgqLhina02+Y4laWAQb+jcYEvF+PMTBTeLGanmaYlmGTYU+4TNIEBKBh5caZwmvNHWCVDsHDXSBF+ZH1rnwxiLLAy8eGDT9koDWWEY9DXyGBFDrQJWcnu4d7LdhwqQJ+L3BT0b2xl3JHu2afX2Sqm7tZa1nnZffx2vrgNKtHJjNPSMby/IlbkbltL2yQ3erOekM0QVByNMXf8cbGX2zTmSnQH5UAOTHU7iqKvyCvdPTteNlbja+2+tUk65nu47OvjLZk9St/djPq85Te3kt7etABJHBJYYl1qOiTQjxdEpbMceCFJXy3LjwwpgJQYoqeV5c6JQYmJ82MTsneBJec0MmsV7BHe4L8p7YA0fp3PXutrP15c0+xd4zWA/cZTm0CbvhhNQCx/uiB9YRX7IVc4Yn7TwP6zkrWd9VbDYrQTtr86aW1Hl56UkJ6Qnp0zoW5uXELe5x3fHVjrELF4zKKzHIhecUibnlrj3Es3JJ7TjIpzZLYGe0q9xEWgfRThfdWL1RzGt6ySVBcegQhAhjSysqFs9Ykz4tOC0qhTitaf5jW9sfm5c9OBcDjlnqVx0lS8WejVlnoN1R9GqzRuGBRbYiEOMVlidSzNtl1Ffl1GQ+n1OTU6031WY3ZDybUZ/985ba2i21e3KPZVdlV4eF12RX576cNz27LiSkLruyfmdd3U6UGeMHOYy+jTKb9cZAlFWmR3viVc0rpHYd33LC6fV17616U/Huio/XvCZ7481lJy6Y5FUwTTgJacJzVXJT1dKlYlydPvA99xqr82g1h6iS4c/0+vp6eB5yhNeEeHplPQwr+hDNUQZcpxJbi4GojBut/ukhhXUdpUOAEQ8BaEOz7Ryg47ctmG7IivDPnZvaZYz/oCU1MyJiQXNvV2pz/ZyMdzrTE0oWTE9r1sfll0NSZHJYWoirOhCCQjKCoqfkxZSMV2YHZDU1F5Zk+obElUyJKnb17Wv2DSg2izocJNXwObkg9WolZMVIr+/x/nr/gPX+ePUn1Xjl9QHilfX0TAO3ZA+QM+xe3DluvNhgcBvP2jC+ZvnwAk72wAXh008vQNjTR4Wfjv0RHvDLfL5n0UtxUYaZBQWdEXBLuHjxAoRcFNxB/vpx4ZfXT+V0vtSVmBCWMSef1a8J8CGoWT+DVthY19HmoV35OlS9gjpycrV+RqZZq9OE6dsM8bFe0QHwqTYgzD9vVPRELT89Q+3v5u4t2iCWS4W/QR/6GC3R6bHOrGVX8Ozu7liIJ23xpe8F6R9iKYOrIl/Bm6xHzRoUGeR3pf2vwpvCYzCGcxpmGy/Of6R13AfPj/qh8+MIE4WUt5bVV2TuqC7qCA4xTisZaSR4qGniw2vWwPqkzX5FPg8z3yRVmGPZ2QBouO0SPiBHKiuZribYA+3kfWm/WaWlM/iA6FxbV9bUrKwxJJQn4Dd5v2ZldfXK9sTEsoQEdm8Ut4G4kOVoJdbZGJpCOKPzi5MI4Tu4LASBan39Qw/Vr48tY/E1HUKgXuzXu0n9elZY0X69nvbrb/AB3nqt2VhpKtKGNbwmXFtCXKOi/fS+JWmxk+MimtLjl1E6JpgA09B/1eL8RkHnLoOtetap//fMuJTyDak+0XH+kYYsj0ayLNlsgayu3NBYXpuqUxgtSAdKSQ+JkTmLZySzBo61tFtJz7ZoyqMCa5xUMX9rpKRtlPI4nmgqaN6mKbw16G1ykSbtfCmJK98Xcwk/sJUkYkxQoozowBQa9IuVLTreughC6yw1EzzMpapAElbec/cxmfzur/ClTOcTz+7P4W5xf4WNNKK4SZVwDmsDg6/U/QUuBGvn6xRRKKOtbKYGwNrX43pyg/8k90APDT/aP0eVvUAX5xH3gMZ3onOmbhQ7f4If96b9bCV4ZxraVJgNtaIvwBlIH5zdSDHXQ6oz92WHh2eHb5xfWDi/EO5E5EVE5NUXdBUUdIl+lI5+dN3mRwGBeo2CjwR6hSXUfzqmsetNmxOxGMIP3JHFshjqxuYfeF4OGJyd8EZUiXWreDI1V9icC9fTYmLSjjxauLP/LSr37X7Z8wUFKbNnJ93+D5YdTAmRZg+jOZHVEUjT/564k0jPpaSJC4IwmjcZK0xTtGGNCMKl/fkSj2uDYIwPp2AkTpK9ZLRelDVJ8gfdW/57QpUsctDn5RHYtVcOLo0AMuGMA7/AZYwxrJvsrmd6sRaXWY9opYOVZJnZZHQTndz04f/Riocf7tWLH6UOAldbL17GNvlz2TrW8x7LcaOAD9Ti5rq7gjsNlgfBsDpHeDFzORieX7TwRdp4oN3vA4iuEhgj/Aeu0TaEROeqbL2Njl333B3dcVgH/VV0TfsuOvywbYjG57IljIZyuCx6Da1rHcRJb13cVFTUNFwiMnddbCxnR28ho+c5nJ4fRQ7PsjFmZ73cgfI7k+rGaCE/MsSzcZxPgcxB56uRYfG5kWGrOcwJIo8OxsONmzSMi2PqdWAi43mDv/86mokDHDh8OyIv2/iMwQgxgZ34FBIjJE552cZfe9IjIVLiEhGe1rdo4Q4fn+jJk2UdKQ2+/aXI5IhvfUp/H+Pz1mT8TfRkPLvfRiafs76h+7COvpGnuih5mexDW2e/X+gv3wZdC7wPkeNdb80+1sUEx0JFeMdWe4ex/r2/bUYxDjzUw4JaBIwcWrTuGp89FOi0G19wmGD0yCePyh4MfSnKP0HI4EBD7ONWIY5lNMbRLpmz3VQCfo/ABaXdaOJ2Y/T7g9MJzu5+lgftJxoBCF/7qQaE0sxiG23EM+gO3r8Ez3huNGIwuA4TgRfBCk8OkZItQajG28SQSTQ6WJz2HFm5aezI/c2uiltup9e/7IFjGtSQDPyLzjaYfWhdi/ZRYJz8BM0it1qZNdigg+kBOYPrcA113yartYNp3Z9FblONycAvuK4c9ZXRWRlbJ+qMS92G3JJpyPBL87JZ0s19pG5UGp2dP7yIUg3XRZIPPYx8yfq7GrHbih7MhlcBBi3PQjSG40lTb75BJ1gwIPzD+uuva1asB10bhNEpFuhib55sO/qgbc++wfgwilIdmojIHIPD0Ihk7ci4YJuYkLTBqED1ZbMTZkfWWaGIdHOYoMBNNP+64VMU3Anhc8dJij09yX6O0xha8zhMZIQn6JY5jGVIuBRrRXpLGD2Pe9ATd9ORZI20tY5UXx6MuSLdhYyu7z3ojrCtA4fLDhZ25BRiF3tFXiKm2DmTG44qTJgO3IPsIOYy3PLD0LbNYQvoszrbiTNpYacJ+oiPIpA98QOez6T84ZmUw4dTDz+TCscPp/4B/4fff0hlttiM8oWy3ryOTpfocVDBi49L4XkaCwkfCKBdLg+Nu05jCJTRWkKmurDfGBU3M6+8+cL+pETTzJzCKmFra2+C/oL/x7WfeE1q7YUZj300evH+iEOz933kPn9v1IHf7WxPs/Dwp9HC4iIhzXmnGJPYLIhhUEIgcZwIXUEEptmmQhR8d792HA0N0lkvIs9xqkTjw9Bk6VPEnON0ifgwzNnoLBERN4KOlOIHSa0WsTaC2pODWBPpdQyeVR1iyghtXe2C5tlBrYdtfP/CkfqL8yfEtIvYnZMmUEzxoSmUEjUfmkTBp5LOthmoGAVsI1C13pdIU1Cy+n8em2+1WiDhveLm5q4VMO+hhQvI8SphWdEjSXQOKvaZ67AeDbY9GyVnPWZaCErPGrBeg9hIEx9wggezjXGFydYxygk6jWlr84Tw1vz8tUuCPQpKijM0o+GZrOnp9bVCBJzJTTAEBI32DtImZs9Oc2nNrLc0Ry+Mgzq0rzhbusbw6/eb0yUZBdh9JkzjEWcjp0xwmhaP//98MNTdh080bsK9+Gyz4/MF4xPw23ykEHgfVo+L8BzJjRTYcp7I73PGL/K3+Y0IjffhzA8PkffQ921bkHSSZDgvxaCI35SCkw6og050HyGE/9JIanidhdJ7iHDNwEItOpXBn7PJIZvA5l3/hxxEembVfXBmKdW1I+XoTK9IS6tIYdKUsBLXXgxYXZacXJbS/7wozrdSoWvbF9kjxAtjRgjG9aFpoG+gg0QxGjocdEZZFHImjT99KMpZ9ojQDdNTagypFUJ/XvFDZdbumaJYP5ly/9IrkOV17qNQKtiUP7/SGbyupk5Jya5ubGzYnz+BSpefv6a70ddrrBol+z4tMwHo3BprZh+UCXOms0OpPHxgirU07LWrk2dtlMans3alp5e2Ku3aA8mjd/wwOEwtHaMdhbqLc1A6S1axqoce8u49DYX9tGgee++RKB0/998cMRe10b/O5qyT7jtpNVMfvt+0FX3YYeJ6TfRfkfYXjLb3fWlLieR+5NtFv3XgAJ3MaZ0kHuel+kU31LGxOYT7vbk+Qf1Az9zB/5H72GypnU8IqxyNRwYw2HNn2N7Y1ehncBfGWCzU4J/13xBnf7hvseTvtnVYjuC6E/XtXd/RdVuiD4jrBr7CdYloL8cavcRiGSUVcqr+G+QLKV7RPAdhku50ruSgOeXiYqfmUeRmr1ImY4vy0WdfecxXEyiaQUcrJfaUPG3+q0UHx+pncFDZqyuvi6snOQ3+AeUJVS+s/qp3y4ycgB+LlhXhN7yaUp5gSpQlmL14kynu5MtL9qw7vz5A+HXatAVlZZzNHiQB46wb65mMnMSaHSOr/Wj2wvBoOmJSe90WS2XSvPaafU1PHKa2sBm3y99+dMs8pdthfjtES7K14/SXJrlhE+Bv0Svsp8BwSvQJkc4XtjrekY5Uxw8jJbwqbr49OZLDQGCj97mtfh8hl6Mph1H+cLgth8l7YigniTzOD9Xtjj7maNWn7VB3fJhxh7mU0zAry6Te9spBGzs80avAg/Sndk/1fkDb3K+Ij/b2spe4emmvdqO8XZi72LzaeWQRj6gmchUW8bwSRcc6HrK9AqJmpiZl9y0KCwybmWxK6jvVkLJNqclriN6icFG0bvRfmt99YHT9Wt2CrO4D8FbToRgwOQlPNe2PFD4DEdfi3JnizUNEm/s9ps8bEXAH7fBM8Sa4jhxDD9K7jn7icc9JNgXdsGn2bASd40QbXkDg2Wh9gbQm3puWVM7bkzsq4s6RIjFI2BNpirjwu0fkHcnFPuq62xthGC72j7AGEWfgMmfMJap7TMFpUHWchK9FY4yYhstypTpffB7jKlLzEp/HoNPgSBAfuyFmk36wQfe3rb0LS5PGyhUWi2yUn3tMXGRd7YaKCuK1/597DaNdRgm3ZU8L38pcvVy9hJS2h9tat85G24gze1teGmEbcNRgk51lhF8dVBlmnrsjR/yDz9SobM/CYsqguULEkRE36xbrFWqG9croA5p6OR2sK3k/2feD/cLf7/7gg91/3zyzunqW1Dg7A6Fnfj63Df5hi4VO9Bk22vMOHp7XBx8I14gR3UmKDGLSdeKGMm3a/ICShxf19S3aUhrQndy1564UmU7ZPcJlimnF4NCmN63pPstiRY9YtqC+B0kVIYhn+WD3DnylHCnlR8IVkxwIxhpWfB7W9tdVtn0NZvWoG6tEyRVWffbZqk7gGsg3cFjWSuc7I3ogMNWh6fGNLVYCV8p9DWcgwPb3PKVg/Tojg33+DVlj9zmJ/iY9nc6q4BfuOXKKPZshzghSQL/Oy98U4JaQROa4JfF+5knl0lwLbuHaN9hane1PFvI8pbX541Ps1w4c4Z7jaK+MTvMd1jork8WlhPKHbMbf3V4CzQhZ5jvKVGEnG6VzC7KYbO720o3kfcJR3go7uQmVG7KY3O72kt+fjk2XCptOwJVzx6ES3rSf35UXthcWtoOCXgsphtMxdj3I/r6KeYUt1zi+in9zddDu6i9ln6ErszedX3uyuZs0vbbNrftsczZgs7kAcY2ZTeZeHRzGsedmBTTACvF59lHAA1nB/vCPdXHZM/kGzomcI+e4QNZJZY+yKCLo3xbp1VrMZlo1P/SgNX2yQmcwG1q1T885UJcTNrWnTvOj0A8ygKumTlNRaE7dntbH+c4a/GBjxZzV3TWRuqTYitHRHhWtwWEV3QtnVUyIchXljoIy4gIvsdiCYSOKjZdfwlz7v2aQhtoAeNqlkM1Kw0AUhc+0SUXBLIq46CpKd9L84cYsXEkX0lIQ2n0oIYaGJMxMCl35Lj6Az+E7+CBuPRMGC9KdmUW+uefec24CwMMHBMwjcIFrywOcIbY8xC3eLDsY49Oyi0t8Wx5hLO4se5gIzSnhnLNy1TsYHlC5sTzEEx4sO5ji3bKLCb4sjzAVrmUP9+IRSzSooaGQQ/JkZCybWqtcyoz8QqFAh4qS5DUvuiojPPe1kqqmsu71mmZ7Wu1wQAr/pLmpJ4j4KyKadFWZ62xdZXWzV7tD6h+jUz+JYvacMpn9Xes4NvvdcNMPKO5oDExqwEyTjE0uVdnUfhJEUfyPiFc2arT8qJBHYcvGkndjFfTRFd8NqwX1FeZYcEjrNg1DtZVlq1WgyipoZBGu5gv8ANFCZh0AAAB42m3RR2wNAACA4e+9PmqrUrv2rNpbUVTNpyitvfcspfZMUSOESPRUsS6IPSPGAbFXbAlnuyiuvDj7k//8H35B//iTq43/8TJiQDAQDESJElJMcdFKKKmU0sooq5zyYlQQq6JK4lRWRVXVVFdDTfFqqa2Ouuqpr4GGGmmsiaYSNJOouRZaaqV1pN9WO+110FEnnXWRpKtuukvWQ0+9pOgtVR999dPfAAOFDZJmsCGGSjfMcBkyjTDSKKONMdY4400w0SQHbbDRFXu8t8kO2xQ47JCt3si12w9Ftsu32XXvfLfXEb/89NsBx9xxy3GTTbHTVPdMc9tdj9z3wEMfTPfUY0+cMMM3u7zwzHMzffLFFrPNMsc8c823T5aFFsi2SI7Flljqo2VWWG6l1Va5aL+11lhnvc++uhS5cNIpr7z12mlnnHfBDWedc1Oeo6665nIgpDCUlhEOB1PSQ6k52Vl/AdztY3wAAAAAAQAB//8AD3jaY2BkYGDgAWIxIGZiYATCa0DMAuYxAAAM0AEFAAAAAAEAAAAKABwAHgABbGF0bgAIAAQAAAAA//8AAAAAAAB42o2YXUyU2RnHnwFE5WtARvxcNUbJdmsl7dJURHE1LbU13c1WahQQtyRr0nSzMW7TaLJpUtM0XPRie9P0Yi9q0ovaNIqzm2obq5ZaXcHVFdBVFAWBGT6GmWE+mRmyb3/neY8s22yTZjK+w3vO+f//z8d5nnMUj4gUyTrZIp63fvyzt2WJFPBGHEfMiOenbx4378T9i7E8fS4RT8UTnfkj+VDuewo8Bzytnl97fuO5n7ch7xd57+f9Oa8zbyBvKC+QvzL/pfy9+W/n/yo/XLCjoL3g3YLbiwoWfWPRiUWXCzcUniq8Wnh7ccHihsWfLFm95PdLC5fuXTpQVFJ0tOiPRZ8Vv1LcXHyx+JWSshJfyZ6SN0oul9aWbi/tKO0uDZVtLHur7GzZA+9G70Hvzz0HvO96f+d93+v3XvN2l5eUHy0/UX6q/L2KvIoXK45WnKj4e8UtVJc4I1IthVLnTEo93x1ORBqcoHRIqfQwVizFzrRUOqfE51yQVcxY76Sl2umVGmY2OUnpcG6J34lKrxMTn5Q6KVnOCjNzjROSFxhZx7ta/t7G7EbG9jkJeU1aZb8zA8KYHATpkBOXZmdKWni28v4N1vhZ75EyfhXB2A9jSBlbZKm0yWKYb8tZbOiUDXJevMyPMH8l85dJFbgr4FrJdy3fajhrsGebM4utN7A1gK3TstMZxd4+2aW60uhIy2FnQo6A1cHvZXjoFBYlQRzGohRoEayZBuW6bEdpPc8dzl1QukCYBSGKFY+xYlTaeHY77+DJd2SF+D4LgjSFb+6DNIlvptU31c6/0DYDahB9UXz0KUhx/DOKvb345x6+CYCaAjWJvgj6Aui7LL/Ffr8zjjfKNE5J4jQOYkp2g7AHtn7iki/LGDnJuvuMeJgTxacljJbBW4Vv1jK7Dv/W82zg3W4nzOoZOQ6amR/Tf+NSAlIAnsv8dQ1LQnA9kHoyqEHK5U3+PqnK+uH9N2zj7Iky2HyKmQIzOq81sUBrgpGQal2KrjRabqJlFM9OaXwO4VljfxtjPXh6ETOCzIgyI8WMAG/Dspq1veKFfRmIyxmpYr2J2iaetcwwuNvxQz2qdzhPWfkx7Ddhj2v8TX4dQlWz8o3g7yCcI3IMPD/j3U4PTD1SiQ0R9Ug1M2ucDMgGNQFqhqwymqZBjqldJi8O8t5kVwu/W/l9mLVt/D7i5LB5me6RGeKfI+45dMzoima+neSOn9mFzEiBlWU0pR4xo35++/DmR6jpRc093ZmN5Nc+Rt09ZrCmWJFjRQLmNKxRonRHToNyVsrkHCidslz3XBfzS3XfVatds9g1i005bEpiT2p+p7TwbWXsMJqMLcXwZpX3B8x+fd6WNPxZqzinFhtfVqA6jeopeNKonmV15ktWG98Ze403ssp1hHdmd/rRU4HvxvDMML5LgJBWde7KT21dmURpAKUjqAwQyxQ2b6RmVILwlMoVl5Vkzhky50+gXSFrJsmYcVBD2H/RZsw1YvsRPrhMXC/pDm1SPwxrHFoYb8WjbTwNw0nnr6DnyJgzZMwZGMpArSQrfOR1NblYg+V1/P151gRBn6AWJez+C2JPTr3x6gKPHFQvJPBCRtq1Ak8rkxeGMAz/hOETGAZgGAZ1GtRZUDMgToOYQndWUUwGmMzq5V0+o6Yu7wQzR/xr+LeO9/XY2MCc3epdE/vPM/QQGM0ahxAYM+SRDz0ma+o1W0wuJJmdtfFPaATdOpbSPGzjeYR3m+gzMXx/A/3XQblJbRmi19yUzUS3mmcNGmpV/wVQp/7LK3fQcxWWi7CkYLkFyyVYrsDyD1guwXJR49LhdJP1cTJgJVkfst3DVNEHZP4d7OiSPuYsQ4mpWwHYI7DHrPVZmLMwZ2HOLsjvuM3QtM3QlI1LXNZb2wLWthtSRf0ytXM9mbAZFlNDXfsSZHEWpnvKtN95Bvo90Pvt7g3a3TsJeh92JLDjZeyIYcca7FgD4wB2PMOOQeyI8KbYdEStuHc1M1YZTXhws+7wpzAnYM7A+gjGIIwDMD7UfnxY96vphQ9hS8L2VdgSsK2EbRVsg7AFYRuFLUkPcHdj0O7lhEa/iViavuh2+HHiMkZMxu1O9Fn/BzWHTCaEQelDbxd6r+Mtr2aDqePr8cRm3T8fo9vUzAk8lobtjs21Qdiuof+WjcdjW3Hj2DKl/eMI3j4GUwdZdRrPnSXnz+GfTpjOcxryw9HFul7qfB+8pudFNDZuZcnAELN5lrToKa1/nWSNn3lVts+PoXlq/gxUrRE2PT5u63OK/dYMYhjEETSPW1TjpRioUbSG0Wn8Xq67zEud6qE+0Xfpo7WM1FHltsPpdrRhdp3JFaNrAoQwuiY4zVVq16zSXryVmJqOVUtktuku7wHBVPlBEAK20t+lYqTZ9U+oCxNondPd3sK3lfE2nqb2dIPXw7cc5Dh+mtEeZXK4zukENbvgVNkPWkhPAI2gmU57kKfxV692N1fXLAhGU05X7+K7AuwJMiGilWUrb2pQtk170wfMMtXmHhyPUW54brAqBtdTraCu8jlVfhhOV3kC5WE9MyxH9TC5NgTDMPkVsrsRC/BavWyVHfh6JxY28GYPNa5R93xc1ZuTzln5vkb+vHwda4axZozT0musKAZ7FHsiKL2C0px20gatYuasEQTD9MIAa0LE83954CQ1b4XGzsvo/x8/c7IatPHL6X5w42e6dcbGL4IXIvICeZWEYQyGpPbkTZqxSdjSWv22aa5dhSlt/T0A0xBM12AKwfQIphF7Bp61ZwNzzpmer/LtWklSsJrzWkC9tB0vF+PNMt31o0SiD/57RCJqT/334b0E1zO4Mhpbt4/l9BzUrpW2Dy+OqudNJoZAMOpzmi2uN/9mlT9DeRC0WT1X7kbFHt1vcyidU7Ql9mxvcjfM7AQznzIzqh2yiVmlem7+YrTu2Pg+x8+walC7rpv1WKd7xMtpr5Jc9ZFl7l1qBJSAtXHc9uosK2JaQ93uksHGIb3h9IJUrvyVrPTBUQ1fDdabHNsJkunWu0Bxd5rxUsbm6ggIMb2pFeHrca1pdZxnXFufZ6ZZEWLmJDMnVXUS1YVW9aMFqset6okFqpMLVGe/oLpU66jxWq3WhwvwmvNFCI9FQRiwp56oKvbzNf1gsaps1Fr5Q0Yiehps1vN4UjPoZbSZ2lqj1iTQYyJmzrcJxlehchEVc4T7YJD7YIC7TojefYGbiXvjMTGeUds90o5nyu1JzVSEZyBPW0vD9qSWtbk4jsXmXBTROnMYveZka7p+1fydx61Ys3a/5ux+nbGWm9tLHDTDHra5ktIs26fnJbNfM1pv3bNSBs1eIrKbHNpDZ29Hax5vvkLXrOAE56Wa7eFdOx5Zq/coc9Py6il7Wm9abvVM2HvwA7snnqBjxFr1EB1hm+3Ttm5krY45e2sx55s5u/My85XU5PVP7G02pr2pkZ3VRNaYztZBdvqNGnb7pM70OX8hlz60/fzLV5l71/OVvWgyMetGcQTFSdQOYW0J62r0/tpla+AotpiM6sGWpJ7U3RtEHDvC2BGzZ4EwdiTA1Ps9kXdv5uZNFRFdwbj7/wdhvFxFNS8XwfvL8e4KTnVFsllepHq9JFv4eyvVf7XUEpt10shni+yVffI1eVVeZ2S/NMk35QCR+ZYc4/NtOc7nO3KST6P8ks935T0+e+UPclq+J+fEz+pu6SG/e6UfjPt8mv4Dvgj8HQAAAAAAAAEAAAAA1aQnCAAAAADMj2Q6AAAAANNkArg=";

export function htmlToXml(html) {
  let doc = document.implementation.createHTMLDocument("");
  doc.write(html);

  // You must manually set the xmlns if you intend to immediately serialize
  // the HTML document to a string as opposed to appending it to a
  // <foreignObject> in the DOM
  doc.documentElement.setAttribute("xmlns", doc.documentElement.namespaceURI);

  // Get well-formed markup
  return new XMLSerializer().serializeToString(doc.body);
}

function htmlToSvgContent(html) {
  const xml = htmlToXml(html).replace(/\#/g, "%23");
  return `<foreignObject width="100%" height="100%">${xml}</foreignObject>`;
}

export function htmlToSvgDataUri(
  html,
  width = 300,
  height = 300,
  fontFamily,
  fontUri
) {
  const svgDefs = `
    <defs><style xmlns="http://www.w3.org/1999/xhtml" type="text/css">
      @font-face {
        font-family: "${fontFamily}";
        src: url("${fontUri}");
      }
    </style></defs>`;

  return `data:image/svg+xml;base64,${window.btoa(
    unescape(
      encodeURIComponent(
        `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}">${svgDefs}${htmlToSvgContent(
          html
        )}</svg>`
      )
    )
  )}`;
}

export function htmlToPng(html, width, height, fontFamily, fontUri) {
  return new Promise((resolve) => {
    const canvas = document.createElement("canvas");
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext("2d");

    const img = document.createElement("img");
    img.setAttribute(
      "src",
      htmlToSvgDataUri(html, width, height, fontFamily, fontUri)
    );

    img.onload = function () {
      ctx.drawImage(img, 0, 0);
      resolve(canvas.toDataURL("image/png"));
    };
  });
}

export async function htmlToPngDataUri(
  html,
  width,
  height,
  fontFamily = "Montserrat",
  fontUri = montserratUri
) {
  return await htmlToPng(html, width, height, fontFamily, fontUri);
}
