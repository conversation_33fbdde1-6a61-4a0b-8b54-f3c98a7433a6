import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Grid, FormLabel } from '@material-ui/core';
import { BoxWrap, Loading } from 'components';
import { UploadBox } from 'components/UploadBox/UploadBox';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import * as headImgActions from '../HeaderImagesActions';
import { useTranslation } from 'react-i18next';

import '../HeaderImages.scss';

const pageAPIPathNameToText = {
  ABOUT: 'about',
  IMPRESSUM: 'impressum',
  PRIVACY_POLICY: 'privacyPolicy',
  TERMS_AND_CONDITIONS: 'termsAndConditions',
  FAQ: 'faq',
};

const uploadHeaderImageAPIPathName = {
  ABOUT: 'About',
  IMPRESSUM: 'Impressum',
  PRIVACY_POLICY: 'Privacy-policy',
  TERMS_AND_CONDITIONS: 'Terms-and-conditions',
  FAQ: 'FAQ',
};

export const AdminForm = () => {
  const { t }: { t: any } = useTranslation();
  const dispatch = useDispatch();
  const { uploadErrors, uploadLoading, pages } = useSelector(
    (state: any) => state.headerImages
  );

  const { addMessage } = useAPIError();

  useEffect(() => {
    dispatch(headImgActions.getHeaderImagesList());
  }, []);

  return (
    <fieldset>
      <legend>{t('uploadHeaderImages')}</legend>
      <BoxWrap>
        <Grid container direction="column" spacing={4}>
          {pages.length ? (
            pages.map((page, i) => (
              <Grid item key={`${pageAPIPathNameToText[page.pageName]}_${i}`}>
                <FormLabel
                  component="legend"
                  className="upload-image-label-grid"
                >
                  {t(pageAPIPathNameToText[page.pageName])}:
                </FormLabel>
                <>
                  <UploadBox
                    acceptedFiles=".png,.jpg,.jpeg,.gif"
                    onDrop={async ([file]) => {
                      let formData = new FormData();
                      formData.append('file', file);
                      dispatch(
                        headImgActions.uploadHeaderImage(
                          formData,
                          uploadHeaderImageAPIPathName[page.pageName],
                          function () {
                            addMessage(t('successImageUpload'), 'success');
                          },
                          function (error) {
                            addMessage(
                              t(error?.errorCode || 'errorOccured'),
                              'error'
                            );
                          }
                        )
                      );
                    }}
                    className="upload-button"
                  >
                    {t('browseImage')}
                  </UploadBox>
                  <div className="image-status">
                    {page.imageLink}
                    {uploadLoading[page.pageName] ? (
                      <div className="loading-message">
                        {t('uploadingImage')}
                      </div>
                    ) : uploadErrors[page.pageName] ? (
                      <div className="error-message">
                        {t('errorUploadingImage')}
                      </div>
                    ) : null}
                  </div>
                </>
                <br />
              </Grid>
            ))
          ) : (
            <Loading />
          )}
        </Grid>
      </BoxWrap>
    </fieldset>
  );
};
