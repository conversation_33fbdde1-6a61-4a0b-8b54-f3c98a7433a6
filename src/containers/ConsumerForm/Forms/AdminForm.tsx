import React, { useEffect, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import * as ConsumerService from 'repository/consumer';
import { useDispatch, useSelector } from 'react-redux';

import { BoxWrap, Loading, CustomDialog } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { useTranslation } from 'react-i18next';
import { Autocomplete } from '@material-ui/lab';
import {
  Button,
  Grid,
  Checkbox,
  FormControlLabel,
  Typography,
  InputLabel,
  List,
  ListItem,
  ListItemText,
  FormLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
} from '@material-ui/core';

import { withStyles } from '@material-ui/core/styles';
import './AdminForm.scss';
import { FormTextField, ReadOnlyTextItem } from 'components/FormInputs';
import { useForm } from 'react-hook-form';
import * as consumerFormActions from '../ConsumerFormActions';
import { getEligibleProducts } from 'containers/Product/ProductActions';
const getDateFormatted = (originalDate) => {
  const date = new Date(originalDate);
  return Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date);
};

const subscriptionEndDate = (subscription) => {
  try {
    const date = new Date(
      subscription?.nextPaymentDate || subscription?.endDate
    );
    return getDateFormatted(date);
  } catch (error) {
    return '';
  }
};

const SUBSCRIPTION_STATUSES = {
  ACTIVE: 'ACTIVE',
  CANCELED: 'CANCELED',
  EXPIRED: 'EXPIRED',
};

const CustomCheckbox = withStyles({
  root: {
    color: '#197BBD',
    '&$checked': {
      color: '#000',
    },
    '&$disabled': {
      color: '#000',
    },
  },
  checked: {},
  disabled: {},
})(Checkbox);

type FormValues = {
  description: string;
};

export const AdminForm = ({
  consumerData,
  getConsumer,
  changeSubscription,
}) => {
  const {
    consumerId,
    gender,
    emails,
    favouriteBrands,
    dateOfBirth,
    productPackageId,
    registrationCode,
    subscription,
    productPackage,
    description,
    isNewslettersSubscribed,
  } = consumerData || {};

  const dispatch = useDispatch();
  const history = useHistory();

  const consumerForm = useSelector((state: any) => state.consumerForm);
  const eligibleProducts = useSelector(
    (state: any) => state.products.eligibleProducts
  );

  const [editSubscriptionDialogOpen, setEditSubscriptionDialogOpen] =
    useState(false);
  const [selectedPackage, setSelectedPackage] = React.useState(
    productPackage || {
      id: productPackageId,
    }
  );

  const [isCancelSubsConfDialogOpen, setIsCancelSubsConfDialogOpen] =
    useState(false);

  useEffect(() => {
    setSelectedPackage(productPackage);
  }, [productPackage]);

  const { addMessage } = useAPIError();
  const { t }: { t: any } = useTranslation();

  const formValues = useForm<FormValues>({
    criteriaMode: 'all',
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: {
      description: description || '',
    },
  });

  const { handleSubmit } = formValues;

  useEffect(() => {
    if (consumerForm.error) {
      addMessage(t(consumerForm.error.errorCode || 'errorOccured'), 'error');
      setTimeout(() => history.push('/dashboard/customers'), 2000);
    } else if (consumerForm.editSuccess) {
      addMessage(t('successConsumerUpdate'), 'success');
      dispatch(consumerFormActions.clearConsumer());
      setTimeout(() => history.push('/dashboard/customers'), 2000);
    }
  }, [consumerForm, history, dispatch]);

  const { status, cancelledDate } = subscription || {};
  const { title, subtitle, corporate } = productPackage || {};

  const isActive = status === SUBSCRIPTION_STATUSES.ACTIVE;
  const isExpired = status === SUBSCRIPTION_STATUSES.EXPIRED;
  const isCanceled = status === SUBSCRIPTION_STATUSES.CANCELED;

  const genderTitle =
    gender === 'male'
      ? t('mrGender')
      : gender === 'female'
      ? t('msGender')
      : gender === 'divers'
      ? t('diversGender')
      : '';

  const toggleCancelSubsConfDialog = () => {
    setIsCancelSubsConfDialogOpen((oldValue) => !oldValue);
  };

  const subscriptionMessage = ({ subscription, productPackage }) => {
    const { status, initialPeriod, nextPaymentDate } = subscription || {};
    const {
      initialAmount,
      recurring,
      recurringAmount,
      recurringInterval,
      duration,
    } = productPackage || {};

    switch (status) {
      case 'ACTIVE':
        if (recurring) {
          if (initialPeriod) {
            return (
              <>
                <p>
                  {t('initialPeriodEndsOn')} {subscriptionEndDate(subscription)}
                </p>
                <p>
                  {recurringInterval === 1
                    ? t('customerPaysMonthly', {
                        recurringAmount: recurringAmount,
                      })
                    : t('customerPaysMonthlyRecurring', {
                        recurringAmount: recurringAmount,
                        recurringInterval: recurringInterval,
                      })}
                </p>
              </>
            );
          } else {
            return (
              <>
                <p>
                  {recurringInterval === 1
                    ? t('customerPaysMonthly', {
                        recurringAmount: recurringAmount,
                      })
                    : t('customerPaysMonthlyRecurring', {
                        recurringAmount: recurringAmount,
                        recurringInterval: recurringInterval,
                      })}
                </p>
                <p>
                  {t('nextPaymentDate')} {getDateFormatted(nextPaymentDate)}
                </p>
              </>
            );
          }
        } else {
          if (duration === 99999) {
            if (initialAmount === 0) {
              return <p>{t('lifetimeFreeMembership')}</p>;
            } else {
              return <p>{t('lifetimeMembership')}</p>;
            }
          } else {
            return (
              <>
                <p>
                  {t('initialPeriodEndsOn')} {subscriptionEndDate(subscription)}
                </p>
                <p>{t('thenSubscriptionEndsAutomatically')}</p>
              </>
            );
          }
        }
      case 'REFUND':
        return t('paymentRefunded');
      case 'PENDING':
        return t('pending');
      case 'SUSPENDED':
        return t('suspended');
      case 'COMPLETED':
        return t('completed');
      default:
        return '';
    }
  };

  const onCancelSubscription = async () => {
    try {
      await ConsumerService.cancelConsumerSubscription(consumerId);
      getConsumer();
      addMessage(t('successSubscriptionCanceled'), 'success');
    } catch (error) {
      //@ts-ignore
      const errorMessage = error.response?.data?.errorCode || 'unknownError';
      addMessage(t(errorMessage), 'error');
    }
  };

  const onOpenEditSubscriptionDialog = () => {
    dispatch(getEligibleProducts(consumerId));
    setEditSubscriptionDialogOpen(true);
  };

  const submit = (data) => {
    dispatch(consumerFormActions.editConsumer(consumerId, data));
  };

  const closeDialogEditSubscription = () => {
    setEditSubscriptionDialogOpen(false);
    setSelectedPackage(
      productPackage || {
        id: productPackageId,
      }
    );
  };

  const editSubscriptionPackage = () => {
    if (selectedPackage) {
      changeSubscription(selectedPackage.id);
    }
  };

  return (
    <form onSubmit={handleSubmit(submit)}>
      <BoxWrap>
        <Grid container>
          <Grid item xl={12} lg={12} sm={12} xs={12}>
            <Grid container>
              <ReadOnlyTextItem
                size={{ xs: 6 }}
                label={t('title')}
                content={genderTitle}
              />
              <Grid item xs={6} className="consumerDetailsItem">
                <InputLabel className="MuiInputLabel-shrink">
                  {t('emails')}
                </InputLabel>
                <List component="nav" aria-label="list-items">
                  {emails?.map((emailData, index) => (
                    <ListItem key={index}>
                      {emailData.primary ? (
                        <ListItemText
                          primary={emailData.email + ' (primary)'}
                          className="listItemText primary-email"
                        />
                      ) : (
                        <ListItemText
                          primary={emailData.email}
                          className="listItemText"
                        />
                      )}
                    </ListItem>
                  )) || null}
                </List>
              </Grid>
              <ReadOnlyTextItem
                size={{ xs: 6 }}
                label={t('firstName')}
                content={consumerData?.firstName}
              />
              <ReadOnlyTextItem
                size={{ xs: 6 }}
                label={t('lastName')}
                content={consumerData?.surname}
              />
            </Grid>
          </Grid>

          <Grid item xl={12} lg={12} sm={12} xs={12}>
            <fieldset>
              <legend> {t('address')} </legend>
              <Grid container>
                <ReadOnlyTextItem
                  size={{ xs: 6 }}
                  label={t('street')}
                  content={consumerData?.street}
                />
                <ReadOnlyTextItem
                  size={{ xs: 6 }}
                  label={t('houseNumber')}
                  content={consumerData?.houseNumber}
                />
                <ReadOnlyTextItem
                  size={{ xs: 6 }}
                  label={t('postalCode')}
                  content={consumerData?.postalCode}
                />
                <ReadOnlyTextItem
                  size={{ xs: 6 }}
                  label={t('place')}
                  content={consumerData?.place}
                />
              </Grid>
            </fieldset>
          </Grid>

          <Grid item xs={12}>
            <FormLabel>{t('notes')}</FormLabel>
            <FormTextField
              control={formValues.control}
              name="description"
              label=""
              multiline={true}
              rows={4}
              variant="outlined"
              rules={{
                maxLength: {
                  value: 1000,
                  message: t('descriptionLess1000'),
                },
              }}
              errors={formValues.errors}
            />
          </Grid>

          <Grid item xl={12} lg={12} sm={12} xs={12}>
            <Grid container>
              <Grid item xs={6} className="consumerDetailsItem mt-40">
                <ReadOnlyTextItem
                  size={{ xs: 12 }}
                  itemClassName=""
                  label={t('dateOfBirth')}
                  content={dateOfBirth && getDateFormatted(dateOfBirth)}
                />
                <Grid item xs={12} className="list">
                  <InputLabel className="MuiInputLabel-shrink">
                    {t('favoriteBrands')}
                  </InputLabel>
                  <List component="nav" aria-label="list-items">
                    {favouriteBrands
                      ? Object.keys(favouriteBrands).map((brand, i) => (
                          <ListItem key={i}>
                            <ListItemText
                              primary={favouriteBrands[brand]}
                              className="listItemText"
                            />
                          </ListItem>
                        ))
                      : null}
                  </List>
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <CustomCheckbox
                        checked={isNewslettersSubscribed === true}
                        disabled
                      />
                    }
                    className="customCheckbox"
                    label={
                      <Typography style={{ color: '#000000DE' }}>
                        {t('subscribedToNewsletter')}
                      </Typography>
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={6} className="consumerDetailsItem">
                <fieldset>
                  <legend> {t('subscriptionDetails')}</legend>
                  {!status ? (
                    t('noActiveSubscription')
                  ) : (
                    <>
                      <ReadOnlyTextItem
                        size={{ xs: 12 }}
                        label={t('productId')}
                        content={productPackageId}
                      />
                      <ReadOnlyTextItem
                        size={{ xs: 12 }}
                        label={t('productTitle')}
                        content={title}
                      />
                      <ReadOnlyTextItem
                        size={{ xs: 12 }}
                        label={t('productSubtitle')}
                        content={subtitle}
                      />
                      <ReadOnlyTextItem
                        size={{ xs: 12 }}
                        label={t('corporate')}
                        content={corporate ? 'yes' : 'no'}
                      />
                      {registrationCode && (
                        <ReadOnlyTextItem
                          size={{ xs: 12 }}
                          label={t('subscriptionCode')}
                          content={registrationCode}
                        />
                      )}
                    </>
                  )}

                  {isCanceled && (
                    <>
                      <ReadOnlyTextItem
                        size={{ xs: 12 }}
                        label={t('subscriptionCancelledOn')}
                        content={getDateFormatted(cancelledDate)}
                      />
                      <ReadOnlyTextItem
                        size={{ xs: 12 }}
                        label={t('subscriptionEndsOn')}
                        content={subscriptionEndDate(
                          consumerData?.subscription
                        )}
                      />
                    </>
                  )}
                  {isExpired && (
                    <ReadOnlyTextItem
                      size={{ xs: 12 }}
                      label={t('subscriptionExpiredOn')}
                      content={subscriptionEndDate(consumerData?.subscription)}
                    />
                  )}
                  <Grid item xs={12} className="consumerDetailsItem">
                    <Typography>{subscriptionMessage(consumerData)}</Typography>
                  </Grid>
                </fieldset>
              </Grid>
              {isActive && (
                <>
                  <Grid item xs={6} className="list"></Grid>
                  <Grid item xs={3} className="list">
                    <Button onClick={toggleCancelSubsConfDialog}>
                      {t('cancelSubscription')}
                    </Button>
                  </Grid>
                  <Grid item xs={3} className="list">
                    <Button onClick={onOpenEditSubscriptionDialog}>
                      {t('editSubscription')}
                    </Button>
                  </Grid>
                </>
              )}
            </Grid>
          </Grid>
        </Grid>
      </BoxWrap>
      <Grid container spacing={1}>
        <Grid item xs={1}>
          <Button
            className="submit-button"
            component={Link}
            to="/dashboard/customers/"
          >
            {t('back')}
          </Button>
        </Grid>
        <Grid item xs={1}>
          <Button
            className="submit-button "
            type="submit"
            variant="contained"
            color="primary"
          >
            {t('save')}
          </Button>
        </Grid>
      </Grid>

      <Dialog
        open={editSubscriptionDialogOpen}
        onClose={closeDialogEditSubscription}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('editSubscription')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          <FormControl fullWidth>
            {eligibleProducts ? (
              <Autocomplete
                options={eligibleProducts}
                getOptionLabel={(option) => option.title}
                style={{ width: 500 }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('products')}
                    variant="outlined"
                  />
                )}
                value={selectedPackage}
                onChange={(event, newValue) => {
                  setSelectedPackage(newValue || null);
                }}
              />
            ) : (
              <Loading />
            )}
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={editSubscriptionPackage}
            color={'inherit'}
            variant="contained"
          >
            {t('ok')}
          </Button>
          <Button
            autoFocus
            onClick={closeDialogEditSubscription}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
        </DialogActions>
      </Dialog>

      <CustomDialog
        isOpen={isCancelSubsConfDialogOpen}
        content={t('sureCancelSubscription')}
        closeLabel={t('no')}
        confirmLabel={t('yes')}
        onClose={toggleCancelSubsConfDialog}
        onConfirm={() => {
          onCancelSubscription();
          toggleCancelSubsConfDialog();
        }}
      />
    </form>
  );
};
