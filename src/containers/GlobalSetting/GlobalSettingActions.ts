import * as globalSettingActionTypes from './GlobalSettingActionTypes';
import * as GlobalSettingService from 'repository/global-setting';

export const getGlobalSetting = () => {
  return async (dispatch) => {
    dispatch({
      type: globalSettingActionTypes.GET_GLOBAL_SETTING,
    });
    try {
      const response = await GlobalSettingService.getGlobalSetting();
      dispatch({
        type: globalSettingActionTypes.GET_GLOBAL_SETTING_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: globalSettingActionTypes.GET_GLOBAL_SETTING_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const saveGlobalSetting = (data) => {
  return async (dispatch) => {
    dispatch({
      type: globalSettingActionTypes.SAVE_GLOBAL_SETTING,
    });

    try {
      await GlobalSettingService.saveGlobalSetting(data);
      dispatch({
        type: globalSettingActionTypes.SAVE_GLOBAL_SETTING_SUCCESS,
        payload: data,
      });
    } catch (error) {
      dispatch({
        type: globalSettingActionTypes.SAVE_GLOBAL_SETTING_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearState = () => ({ type: globalSettingActionTypes.CLEAR_STATE });
