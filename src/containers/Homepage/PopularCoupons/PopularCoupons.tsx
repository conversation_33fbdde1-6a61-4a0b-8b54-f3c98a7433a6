import React, { useState, useEffect, useRef } from 'react';
import { Grid, Button, TextField, Chip, Snackbar } from '@material-ui/core';
import { Autocomplete, Alert } from '@material-ui/lab';
import DeleteIcon from '@material-ui/icons/Delete';
import SearchIcon from '@material-ui/icons/Search';
import DragIndicatorIcon from '@material-ui/icons/DragIndicator';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import * as PopularCouponsActions from './PopularCouponsActions';

interface CouponTag {
  id: string;
  name: string;
  discount: string;
}

interface CouponOption {
  id: string | number;
  brandName?: string;
  categoryName?: string;
  amountCondition?: string;
}

interface PopularCouponFormData {
  selectedTag: CouponTag | null;
}

interface PopularCouponsProps {
  allCoupons: CouponOption[];
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

const PopularCoupons: React.FC<PopularCouponsProps> = ({ allCoupons }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { setValue } = useForm<PopularCouponFormData>({
    defaultValues: {
      selectedTag: null
    }
  });
  const [selectedTags, setSelectedTags] = useState<CouponTag[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Dragging functionality
  const [draggedItem, setDraggedItem] = useState<CouponTag | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [targetIndex, setTargetIndex] = useState<number | null>(null);
  const tagContainerRef = useRef<HTMLDivElement>(null);

  // Add CSS for better drag visual effects
  useEffect(() => {
    // Add CSS to head
    const styleSheet = document.createElement("style");
    styleSheet.textContent = `
      .tag-item {
        transition: transform 0.15s ease, opacity 0.15s ease;
      }
      .tag-item.dragging {
        opacity: 0.7;
        transform: scale(1.05);
      }
      .tag-item.placeholder {
        opacity: 0.2;
        background-color: #e0e0e0 !important;
        border: 2px dashed #aaa;
      }
      .tag-item.placeholder * {
        visibility: hidden;
      }
    `;
    document.head.appendChild(styleSheet);

    return () => {
      document.head.removeChild(styleSheet);
    };
  }, []);

  // Convert allCoupons to CouponTag format - memoize to prevent infinite loops
  const couponTags: CouponTag[] = React.useMemo(() => {
    return allCoupons.map(coupon => ({
      id: coupon.id.toString(),
      name: coupon.brandName || 'Unknown',
      discount: coupon.amountCondition || 'N/A'
    }));
  }, [allCoupons]);

  // Add CSS for better drag visual effects
  useEffect(() => {
    // Add CSS to head
    const styleSheet = document.createElement("style");
    styleSheet.textContent = `
      .tag-item {
        transition: transform 0.15s ease, opacity 0.15s ease;
      }
      .tag-item.dragging {
        opacity: 0.7;
        transform: scale(1.05);
      }
    `;
    document.head.appendChild(styleSheet);

    return () => {
      document.head.removeChild(styleSheet);
    };
  }, []);

  // Fetch popular coupons on component mount
  useEffect(() => {
    let isMounted = true;
    let hasError = false;

    const fetchPopularCoupons = async () => {
      if (hasError) return; // Prevent additional API calls if there was an error

      try {
        console.log('Fetching popular coupons...');
        const response = await dispatch(PopularCouponsActions.getPopularCoupons({
          page: 0,
          pageSize: 100 // Fetch a large number to ensure we get all popular coupons
        }));

        console.log('Popular coupons response:', response);

        if (isMounted && response && response.couponIds && couponTags.length > 0) {
          console.log('Coupon IDs from API:', response.couponIds);
          console.log('Available coupon tags:', couponTags);

          // Find the coupon tags that match the IDs from the API and preserve their order
          const matchedTags: CouponTag[] = [];

          // Preserve the order of couponIds from the API response
          response.couponIds.forEach((couponId: number) => {
            const matchingTag = couponTags.find(tag => Number(tag.id) === couponId);
            if (matchingTag) {
              matchedTags.push(matchingTag);
            }
          });

          console.log('Matched tags in order:', matchedTags);

          if (matchedTags.length > 0) {
            setSelectedTags(matchedTags);
          } else {
            console.log('No matching tags found between API response and available coupons');
          }
        } else {
          console.log('No coupon IDs in response or component unmounted or no coupon tags available');
        }
      } catch (error) {
        console.error('Error fetching popular coupons:', error);
        hasError = true;

        if (isMounted) {
          setSnackbar({
            open: true,
            message: 'Failed to load popular coupons',
            severity: 'error'
          });
        }
      }
    };

    // Only fetch if we have coupons available
    if (allCoupons.length > 0) {
      fetchPopularCoupons();
    } else {
      console.log('No coupons available yet, skipping popular coupons fetch');
    }

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [dispatch, allCoupons.length, couponTags]);

  const handleAddTag = (tag: CouponTag | null) => {
    if (tag && !selectedTags.some(t => t.id === tag.id)) {
      setSelectedTags([...selectedTags, tag]);
    }
    setValue('selectedTag', null);
    setInputValue('');
  };

  const handleRemoveTag = (id: string) => {
    setSelectedTags(selectedTags.filter(tag => tag.id !== id));
  };

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, tag: CouponTag, index: number) => {
    // Set the dragged item and its original index
    setDraggedItem(tag);
    setDraggedIndex(index);

    // Make the element visually draggable with a ghost image
    if (e.dataTransfer) {
      e.dataTransfer.effectAllowed = 'move';
      // Create a custom drag image - optional
      const dragImage = e.currentTarget.cloneNode(true) as HTMLDivElement;
      dragImage.style.opacity = '0.7';
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px';
      document.body.appendChild(dragImage);
      e.dataTransfer.setDragImage(dragImage, 20, 20);

      // Clean up after drag image is captured
      setTimeout(() => {
        document.body.removeChild(dragImage);
      }, 0);
    }

    // Add dragging class to the dragged element
    e.currentTarget.classList.add('dragging');
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedItem === null) return;

    // Don't do anything if hovering over the same item being dragged
    if (index === draggedIndex) return;

    // Set the target index for the placeholder
    setTargetIndex(index);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedItem === null) return;

    // Don't do anything if hovering over the same item being dragged
    if (index === draggedIndex) return;

    // Set the target index for the placeholder if not already set
    if (targetIndex !== index) {
      setTargetIndex(index);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    // Only clear the target if we're leaving a child element
    if (e.currentTarget.contains(e.relatedTarget as Node)) {
      return;
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedItem === null) return;

    // Don't do anything if dropping on the same item being dragged
    if (index === draggedIndex) return;

    // Create new array with updated order
    const newTags = [...selectedTags];
    const draggedItemContent = newTags[draggedIndex];

    // Remove the item from its original position
    newTags.splice(draggedIndex, 1);
    // Insert at the new position
    newTags.splice(index, 0, draggedItemContent);

    // Update the state with new order
    setSelectedTags(newTags);

    // Clear the target index
    setTargetIndex(null);
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    // If we have a target index and haven't dropped yet, update the order
    if (targetIndex !== null && draggedIndex !== null) {
      const newTags = [...selectedTags];
      const draggedItemContent = newTags[draggedIndex];

      // Remove the item from its original position
      newTags.splice(draggedIndex, 1);
      // Insert at the new position
      newTags.splice(targetIndex, 0, draggedItemContent);

      // Update the state with new order
      setSelectedTags(newTags);
    }

    // Reset drag state
    setDraggedItem(null);
    setDraggedIndex(null);
    setTargetIndex(null);

    // Remove dragging class from all elements
    document.querySelectorAll('.tag-item').forEach(el => {
      el.classList.remove('dragging');
      el.classList.remove('placeholder');
    });

    // Log the new order for debugging
    console.log('New order of coupon IDs:', selectedTags.map(tag => tag.id));
  };

  const handleSave = async () => {
    if (selectedTags.length === 0) {
      setSnackbar({
        open: true,
        message: 'Please select at least one coupon',
        severity: 'error'
      });
      return;
    }

    setLoading(true);
    try {
      // Extract coupon IDs from selected tags in their current order
      const couponIds = selectedTags.map(tag => Number(tag.id));
      console.log('Saving popular coupons with IDs in order:', couponIds);

      // Call API to update popular coupons
      const result = await dispatch(PopularCouponsActions.updatePopularCoupons({ couponIds }));
      console.log('Save result:', result);

      setSnackbar({
        open: true,
        message: 'Popular coupons saved successfully!',
        severity: 'success'
      });

      // Refresh the data after saving
      console.log('Refreshing data after save...');
      const response = await dispatch(PopularCouponsActions.getPopularCoupons({
        page: 0,
        pageSize: 100
      }));

      console.log('Refresh response:', response);

      if (response && response.couponIds && couponTags.length > 0) {
        // Find the coupon tags that match the IDs from the API and preserve their order
        const matchedTags: CouponTag[] = [];

        // Preserve the order of couponIds from the API response
        response.couponIds.forEach((couponId: number) => {
          const matchingTag = couponTags.find(tag => Number(tag.id) === couponId);
          if (matchingTag) {
            matchedTags.push(matchingTag);
          }
        });

        console.log('Updated matched tags after save in order:', matchedTags);

        // Only update state if there's a change to prevent unnecessary re-renders
        if (JSON.stringify(matchedTags.map(tag => tag.id)) !== JSON.stringify(selectedTags.map(tag => tag.id))) {
          setSelectedTags(matchedTags);
        }
      }
    } catch (error) {
      console.error('Error saving popular coupons:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save popular coupons',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const availableTags = couponTags.filter(
    (tag: CouponTag) => !selectedTags.some(selectedTag => selectedTag.id.toString() === tag.id.toString())
  );

  return (
    <div className="popular-coupons-section">
      <div className="section-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 className="section-title" style={{ margin: 0 }}>{t('popularCoupons')}</h2>
        <Button
          variant="contained"
          className="save-button"
          onClick={handleSave}
          disabled={loading}
        >
          {loading ? t('saving') : t('save').toUpperCase()}
        </Button>
      </div>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <div style={{ marginBottom: '8px' }}>
            <Autocomplete
              options={availableTags}
              getOptionLabel={(option: CouponTag) => option ? `${option.name} | ${option.discount}` : ''}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  variant="outlined"
                  size="small"
                  placeholder={t('search')}
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <div style={{ paddingLeft: '8px', paddingRight: '16px' }}>
                        <SearchIcon style={{ color: '#757575' }} />
                      </div>
                    ),
                    style: {
                      borderRadius: '12px',
                      fontSize: 11,
                      height: 36
                    }
                  }}
                  className="search-input"
                />
              )}
              onChange={(_event, newValue) => handleAddTag(newValue)}
              inputValue={inputValue}
              onInputChange={(_event, newInputValue) => {
                setInputValue(newInputValue);
              }}
              value={null}
              ListboxProps={{
                style: {
                  maxHeight: 200,
                  fontSize: 11,
                  overflowY: 'auto',
                  borderRadius: 8,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                }
              }}
            />
          </div>

          <div
            className="tag-container"
            ref={tagContainerRef}
            style={{
              padding: '16px',
              border: '1px solid #707070',
              borderRadius: '12px',
              minHeight: '100px',
              position: 'relative',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))',
              gridGap: '12px',
              alignContent: 'flex-start'
            }}
          >
            {selectedTags.length === 0 && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                color: '#aaa',
                fontSize: '14px'
              }}>
                {t('dragAndDropCouponsHere') || 'Drag and drop coupons here'}
              </div>
            )}

            {selectedTags.map((tag, index) => (
              <div
                key={tag.id}
                className={`tag-item ${draggedIndex === index ? 'dragging' : ''} ${targetIndex === index && draggedIndex !== index ? 'placeholder' : ''}`}
                draggable={true}
                onDragStart={(e) => handleDragStart(e, tag, index)}
                onDragEnter={(e) => handleDragEnter(e, index)}
                onDragOver={(e) => handleDragOver(e, index)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, index)}
                onDragEnd={handleDragEnd}
                style={{
                  userSelect: 'none',
                  background: draggedIndex === index ? '#f0f0f0' : '#f5f5f5',
                  borderRadius: '15px',
                  boxShadow: draggedIndex === index ? '0 2px 5px rgba(0,0,0,0.2)' : 'none',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '4px',
                  zIndex: draggedIndex === index ? 9999 : 1,
                  minHeight: '38px',
                  opacity: draggedIndex === index ? 0.7 : 1,
                  cursor: 'grab',
                  border: targetIndex === index && draggedIndex !== index ? '2px dashed #aaa' : 'none'
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0 8px 0 4px',
                    height: '100%'
                  }}
                >
                  <DragIndicatorIcon style={{ fontSize: 16, color: '#666' }} />
                </div>

                <Chip
                  label={`${tag.name} | ${tag.discount}`}
                  onDelete={() => handleRemoveTag(tag.id)}
                  className="tag"
                  size="small"
                  style={{
                    borderRadius: '15px',
                    fontSize: 11,
                    height: 30,
                    backgroundColor: 'transparent',
                    display: 'flex',
                    alignItems: 'center',
                    paddingBottom: '2px',
                    paddingTop: '2px',
                    paddingLeft: 0,
                    flexGrow: 1,
                    width: 'calc(100% - 24px)' // Account for the drag handle
                  }}
                  deleteIcon={
                    <DeleteIcon
                      style={{
                        fontSize: 16,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%'
                      }}
                    />
                  }
                />
              </div>
            ))}
          </div>
        </Grid>
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default PopularCoupons;