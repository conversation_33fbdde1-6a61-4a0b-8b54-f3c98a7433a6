import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Redirect } from 'react-router-dom';

import { loginUser, clearErrors } from './LoginActions';
import { LoginView } from './LoginView';
import { isAuth } from 'services/auth';
import * as RoleManager from 'services/role';

import './Login.scss';

export default () => {
  const dispatch = useDispatch();
  const login = useSelector((state: any) => state.login);

  const signInUser = (data) => {
    const redirect = true;
    dispatch(loginUser(data, redirect));
  };

  const removeErrors = () => {
    dispatch(clearErrors());
  };

  if (isAuth()) {
    if (RoleManager.isAbleTo('user', 'view')) {
      return <Redirect to="/dashboard/admin-users" />;
    } else if (RoleManager.isAbleTo('brand_account', 'view')) {
      return <Redirect to="/dashboard/brand-account" />;
    } else {
      return <Redirect to="/dashboard/coupons" />;
    }
  }

  return (
    <LoginView
      clearErrors={removeErrors}
      loginUser={signInUser}
      login={login}
    />
  );
};
