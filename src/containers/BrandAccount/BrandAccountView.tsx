import { Box, Button, Grid, Typography } from '@material-ui/core';
import { useHistory } from 'react-router-dom';
import { makeStyles } from '@material-ui/styles';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

import { BrandAccount } from 'repository/brand-account';
import { BrandAccountBrandUsersTable } from './components/BrandAccountBrandUsersTable';
import Card from './components/Card';
import PageCard from './components/PageCard';
import { HistoryTable } from './components/HistoryTable';

import HelpIcon from '@material-ui/icons/Help';

import './BrandAccount.scss';
import { useDispatch } from 'react-redux';
import { getUserProfile } from 'containers/User/UserActions';
import { BlockedInfoDialog } from 'components';

const useStyles = makeStyles({
  introCard: {
    maxWidth: 800,
    '&>div': {
      borderRadius: '10px',
      marginBottom: '0px',
      boxShadow: 'none',
      border: '1px solid #E5E5E5',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-around',
      padding: '0px 16px 0px 16px',
      minHeight: '140px',
    },
  },
  greetingHeadline: {
    fontSize: '1.5rem',
    fontWeight: 700,
    marginBottom: 8,
  },
  tilesRow: {
    display: 'flex',
    flexDirection: 'row',
    padding: 0,
    margin: 0,
    maxWidth: '100%',
  },
  submitButtonRow: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 0,
    margin: 0,
    maxWidth: '100%',
  },
});

export interface Props {
  data: BrandAccount;
  reachBoostStatus: string;
  requestedCouponCount: number;
  onCouponCountChange(count: number): void;
  requestedCouponUpdatesCount: number;
  onCouponUpdatesCountChange(count: number): void;
  isImageVideoRequested: boolean;
  onImageVideoRequest(isRequested: boolean): void;
  isUnboxingVideoRequested: boolean;
  onUnboxingVideoRequest(isRequested: boolean): void;
  openReachBoostDialog(): void;
  onRequest(): void;
  onInfoClick(translationKey: string): void;
}

export const BrandAccountView = ({
  data
}: Props) => {
  const {
    templateName,
    coupons,
    usedCoupons,
    history,
    brandUsers,
  } = data;

  const classes = useStyles();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const historyRouter = useHistory();
  const account = useSelector((state: any) => state.account);
  const userProfile = useSelector((state: any) => state.user?.userProfile);
  const { firstName, lastName } = account.user;

  const subscriptionPackageName = useMemo(
    () =>
      templateName.substring(0, 1).toUpperCase() +
      templateName.substring(1).toLowerCase(),

    [templateName]
  )
  const [showInfoDialogue, setShowInfoDialogue] = React.useState(false);

  const closeDialog = () => {
    setShowInfoDialogue(false);
  }

  const fetchAndOpenStripeUrl = () => {
    if(userProfile?.stripeSessionUrl) {
      dispatch(getUserProfile());
      window.open(userProfile.stripeSessionUrl, '_blank');
    } else {
      setShowInfoDialogue(true);
    }
  }

  return (
    <div>
      <Grid container spacing={3}>
        <Grid item xs={12} md={12} lg={12}>
          <Card className={classes.introCard} style={{ maxWidth: '100%' }}>
            <div>
              <Typography
                variant="h3"
                component="h2"
                className={classes.greetingHeadline}
              >
                {t('brandUserGreeting', {
                  userName: `${firstName} ${lastName}`,
                })}
              </Typography>
              <Typography
                component="p"
                dangerouslySetInnerHTML={{ __html: t('brandAccountIntro') }}
              ></Typography>
            </div>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={2} className='mt-3'>
        <Grid item md={4} sm={6} xs={6}>
          <PageCard 
            title={t('manageBrandPage')}
            description={t('manageBrandPageInfo')}
            redirectTo='/dashboard/brand-page'
            icon='/icons/brand-page.svg'
          />
        </Grid>
        <Grid item md={4} sm={6} xs={6}>
          <PageCard 
            title={t('manageCoupons', { usedCoupons, coupons })}
            description={t('manageCouponsInfo')}
            redirectTo='/dashboard/brand-coupons'
            icon='/icons/coupons.svg'
          />
        </Grid>
        <Grid item md={4} sm={6} xs={6}>
          <PageCard 
            title={t('manageAddOns')}
            description={t('manageAddOnsInfo')}
            redirectTo='/dashboard/add-ons'
            icon='/icons/add-ons.svg'
          />
        </Grid>
        <Grid item md={4} sm={6} xs={6}>
          <PageCard 
            title={t('brandBanner')}
            description={t('brandBannerInfo')}
            redirectTo='/dashboard/brand-page-banners'
            icon='/icons/brand-banner.svg'
            isDisabled
            customButton={
              <Button 
                size="small" 
                color="primary"
                variant="contained"
                className="primary-button"
                onClick={() => historyRouter.push('/dashboard/brand-page-banners')}
              >
                {t('upgrade')}
              </Button>
            }
          />
        </Grid>
        <Grid item md={4} sm={6} xs={6}>
          <PageCard
            title={t('information')}
            description={t('informationInfo')}
            redirectTo='/dashboard/information'
            icon='/icons/information.svg'
          />
        </Grid>
        <Grid item md={4} sm={6} xs={6}>
          <PageCard
            title={t('manageTarif')}
            description={t('manageTarifInfo')}
            customAction={fetchAndOpenStripeUrl}
            icon='/icons/tarif.svg'
          />
        </Grid>
      </Grid>

      <Grid container spacing={2} style={{ marginTop: '30px' }}>
        <Grid item xs={6}>
          <Card title={t('historyOrChangelog')} className='rounded-card'>
            <HistoryTable data={history} />
          </Card>
        </Grid>
        <Grid item xs={6}>
          <Card title={t('brandUsers')} className='rounded-card'>
          <BrandAccountBrandUsersTable data={brandUsers} />
          </Card>
        </Grid>
      </Grid>

      {showInfoDialogue && <BlockedInfoDialog messageType='stripeUserBlocked' allowClose={true} onClose={closeDialog} />}
    </div>
  );
};
