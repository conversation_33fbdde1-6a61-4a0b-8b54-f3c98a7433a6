import React, { useState, useEffect } from 'react';
import {
  Button,
  TextField,
  FormControlLabel,
  Radio,
  RadioGroup,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import Dialog from '@material-ui/core/Dialog';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import { useTranslation } from 'react-i18next';
import BannerRow from './BannerRow';
import {
  getBannerSliders,
  deleteBannerSlider
} from 'repository/bannerSlider';
import { Banner } from 'repository/bannerSlider';

import { useDispatch } from 'react-redux';
import { createBannerSlider, updateBannerSlider } from './BannerSliderActions';

interface BannerItem {
  id?: string | undefined;
  brand?: string | undefined;
  desktopBanner?: string;
  mobileBanner?: string;
  startDate?: Date | null;
  endDate?: Date | null;
  link?: string;
}

interface Brand {
  id: string;
  name: string;
}

interface ApiError {
  response?: {
    data: any;
  };
}

interface BannerSliderProps {
  brands: Brand[];
}

interface ImageChange {
  file: File;
  oldPath?: string;
}

interface BannerImageChanges {
  desktopBanner?: ImageChange;
  mobileBanner?: ImageChange;
}

const BannerSlider: React.FC<BannerSliderProps> = ({ brands }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [autoSlide, setAutoSlide] = useState(true);
  const [sekValue, setSekValue] = useState('5');
  const [banners, setBanners] = useState<BannerItem[]>([
    {
      brand: '',
      desktopBanner: '',
      mobileBanner: '',
      startDate: null,
      endDate: null,
      link: ''
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [imageChanges, setImageChanges] = useState<Record<string, BannerImageChanges>>({});
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; index: number | null }>({ open: false, index: null });

  const fetchBannerSliders = async () => {
    try {
      setLoading(true);
      const response = await getBannerSliders({
        page: 1,
        size: 10,
        filters: {
          status: 'ACTIVE'
        }
      });

      if (response.data && response.data.length > 0 && response.data[0].banners) {
        const bannersArray = response.data[0].banners;
        const formattedBanners = bannersArray.map((banner: Banner) => ({
          id: banner.id ? banner.id.toString() : undefined,
          brand: brands.find(b => b.id === banner.brandId.toString())?.name || '',
          desktopBanner: banner.desktopBanner,
          mobileBanner: banner.mobileBanner,
          startDate: banner.startDate ? new Date(banner.startDate.split('T')[0]) : null,
          endDate: banner.endDate ? new Date(banner.endDate.split('T')[0]) : null,
          link: banner.link
        }));
        setBanners(formattedBanners);

        // Reset image changes after fetching
        setImageChanges({});
      }
    } catch (error) {
      const apiError = error as ApiError;
      if (apiError?.response) {
        setSnackbar({
          open: true,
          message: 'Failed to fetch banner sliders',
          severity: 'error'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (brands.length > 0) {
      fetchBannerSliders();
    }
  }, [brands]);

  const handleAddBanner = () => {
    if (banners.length === 0) {
      setBanners([
        {
          brand: '',
          desktopBanner: '',
          mobileBanner: '',
          startDate: null,
          endDate: null,
          link: ''
        }
      ]);
      return;
    }
    const lastBanner = banners[banners.length - 1];
    const allEmpty = !lastBanner.brand && !lastBanner.desktopBanner && !lastBanner.mobileBanner && !lastBanner.startDate && !lastBanner.endDate && !lastBanner.link;
    if (allEmpty) {
      return;
    }
    setBanners([
      ...banners,
      {
        brand: '',
        desktopBanner: '',
        mobileBanner: '',
        startDate: null,
        endDate: null,
        link: ''
      }
    ]);
  };

  const handleRemoveBanner = (idx: number, banner: BannerItem) => {
    if (!banner?.id) {
      // New/unsaved banner: remove immediately
      setBanners(banners => banners.filter((_, i) => i !== idx));
      setImageChanges(prev => {
        const updated = { ...prev };
        delete updated[idx];
        return updated;
      });
      setSnackbar({ open: true, message: 'Banner deleted', severity: 'success' });
    } else {
      // Existing banner: show dialog
      setDeleteDialog({ open: true, index: idx });
    }
  };

  const confirmDeleteBanner = async () => {
    if (deleteDialog.index == null) return;
    const banner = banners[deleteDialog.index];
    try {
      setLoading(true);
      await deleteBannerSlider(parseInt(banner.id!));
      setBanners(banners => banners.filter((_, i) => i !== deleteDialog.index));
      setImageChanges(prev => {
        const updated = { ...prev };
        delete updated[deleteDialog.index!];
        return updated;
      });
      setSnackbar({ open: true, message: 'Banner deleted', severity: 'success' });
    } catch {
      setSnackbar({ open: true, message: 'Failed to delete banner', severity: 'error' });
    } finally {
      setLoading(false);
      setDeleteDialog({ open: false, index: null });
    }
  };

  const handleBannerChange = (index: number, field: keyof BannerItem, value: any) => {
    setBanners(banners => banners.map((banner, i) => i === index ? { ...banner, [field]: value } : banner));
  };

  const handleBrandChange = (index: number, _event: any, newValue: Brand | null) => {
    handleBannerChange(index, 'brand', newValue ? newValue.name : '');
  };

  // Handle image change for both desktop and mobile banners
  const handleImageChange = (index: number, type: 'desktopBanner' | 'mobileBanner', file: File) => {
    setImageChanges(prev => ({
      ...prev,
      [index]: {
        ...prev[index],
        [type]: { file }
      }
    }));
  };

  const handleSave = async () => {
    // Validation for required fields
    const missingFields = banners.some(banner => {
      return !banner.brand?.trim() || !banner.link?.trim() || !banner.startDate;
    });
    if (missingFields) {
      setSnackbar({
        open: true,
        message: 'Please fill all required fields: Brand, Start Date, and Link.',
        severity: 'error'
      });
      return;
    }

    try {
      setLoading(true);

      // Format data for API
      const bannerSliderData = {
        enableAutoSlide: autoSlide,
        autoSlideTime: parseInt(sekValue) * 1000,
        banners: banners.map(banner => {
            const formattedBanner = {
            desktopBanner: banner.desktopBanner,
            mobileBanner: banner.mobileBanner,
            brandId: parseInt(brands.find(b => b.name === banner.brand)?.id || '0'),
            startDate: banner.startDate ? banner.startDate.toISOString() : '',
            endDate: banner.endDate ? banner.endDate.toISOString() : '',
            link: banner.link
            };

          // Only include ID if it's a valid number (existing banner from backend)
          if (banner.id) {
            return { ...formattedBanner, id: parseInt(banner.id) };
          }

          return formattedBanner;
        })
      };

      // Determine if we're creating new or updating existing based on whether any banner has a numeric ID
      const hasExistingBanners = banners.some(b => b.id);

      if (!hasExistingBanners) {
        await dispatch(createBannerSlider(bannerSliderData, imageChanges));
      } else {
        await dispatch(updateBannerSlider({
          ...bannerSliderData,
          id: parseInt(banners.find(b => b.id)?.id ?? banners[0].id ?? '')
        }, imageChanges));
      }

      setSnackbar({
        open: true,
        message: 'Banners saved successfully',
        severity: 'success'
      });

      // Reset image changes after successful save
      setImageChanges({});

      // Refresh data from server
      fetchBannerSliders();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to save banners',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="banner-slider-container banner-slider-section">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 className="section-title" style={{ margin: 0 }}>Banner Slider</h2>
        <div style={{ display: 'flex', alignItems: 'center', gap: '32px' }}>
          <RadioGroup
            row
            value={autoSlide ? "auto" : "no-auto"}
            onChange={(e) => setAutoSlide(e.target.value === "auto")}
          >
            <FormControlLabel
              value="no-auto"
              control={<Radio size="small" />}
              label={<span style={{ fontSize: 10 }}>No auto-slide</span>}
            />
            <FormControlLabel
              value="auto"
              control={<Radio size="small" />}
              label={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <TextField
                    size="small"
                    value={sekValue}
                    onChange={(e) => setSekValue(e.target.value)}
                    style={{ width: 40 }}
                    InputProps={{
                      style: { height: 24, fontSize: 11 }
                    }}
                  />
                  <span style={{ fontSize: 10 }}>Sek</span>
                </div>
              }
              style={{ marginLeft: 16 }}
            />
          </RadioGroup>
          <Button
            variant="contained"
            className="save-button"
            onClick={handleSave}
            disabled={loading}
          >
            {loading ? 'Saving...' : 'SAVE'}
          </Button>
        </div>
      </div>
      {/* Render all banners as editable rows */}
      {banners.map((banner, idx) => (
        <BannerRow
          key={idx}
          banner={banner}
          onRemove={(index, bannerObj) => handleRemoveBanner(index, bannerObj)}
          setValue={(field: string, value: any) => handleBannerChange(idx, field as keyof BannerItem, value)}
          brands={brands}
          handleBrandChange={(_event: any, newValue: Brand | null) => handleBrandChange(idx, _event, newValue)}
          onImageChange={(type: 'desktopBanner' | 'mobileBanner', file: File) => handleImageChange(idx, type, file)}
          index={idx}
        />
      ))}
      <Button
        type="button"
        variant="contained"
        className="add-button"
        style={{ textTransform: 'capitalize', height: 28 }}
        onClick={handleAddBanner}
        disabled={loading}
      >
        + Add Banner
      </Button>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          style={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, index: null })}>
        <DialogTitle>Delete Banner?</DialogTitle>
        <DialogContent>Are you sure you want to delete this banner?</DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, index: null })}>Cancel</Button>
          <Button color="secondary" onClick={confirmDeleteBanner}>Delete</Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default BannerSlider;