import { Typography } from '@material-ui/core';
import StarIcon from '@material-ui/icons/Star';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import { makeStyles } from '@material-ui/styles';
import React, { useMemo } from 'react';

const MAX_STARS = 3;

const useStyles = makeStyles({
  root: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: 16,
  },
  starsContainer: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 8,
  },
});

interface Props {
  name: string;
  numberOfStars: number;
}

const SubscriptionPackage = ({ name, numberOfStars }: Props) => {
  const classes = useStyles();

  const starFillStates = useMemo(() => {
    const result: boolean[] = [];
    for (let i = 0; i < MAX_STARS; i++) {
      result.push(i + 1 <= numberOfStars);
    }
    return result;
  }, [numberOfStars]);

  const subscriptionPackageName =
    name.substring(0, 1).toUpperCase() + name.substring(1).toLowerCase();

  return (
    <>
      <Typography variant="h5" component="h4" className={classes.root}>
        <b>{subscriptionPackageName}</b>
        <div className={classes.starsContainer}>
          {starFillStates.map((isFilled, index) =>
            isFilled ? (
              <StarIcon key={index} style={{ fill: '#ffea00' }} />
            ) : (
              <StarBorderIcon key={index} style={{ fill: '#ffea00' }} />
            )
          )}
        </div>
      </Typography>
    </>
  );
};

export default SubscriptionPackage;
