import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { Grid, Button } from '@material-ui/core';
import { useForm } from 'react-hook-form';
import { BoxWrap, Loading, UploadBox } from 'components';
import { FormTextField } from 'components/FormInputs';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import * as productActions from './ProductCodesActions';
import { ProductCodesTable } from './components/ProductCodesTable';
import { ProductCodesView } from './ProductCodesView';
import { useTranslation } from 'react-i18next';

import './ProductCodes.scss';

type FormValues = {
  code: string;
  numberOfUsages: number;
};

export default (props) => {
  let { id } = useParams();
  const dispatch = useDispatch();
  const productCodes = useSelector((state: any) => state.productsCodes);
  const { addMessage } = useAPIError();
  const { t }: { t: any } = useTranslation();

  useEffect(() => {
    dispatch(productActions.getProduct(id));

    return () => {
      dispatch(productActions.clearProduct());
    };
  }, [id, dispatch]);

  useEffect(() => {
    dispatch(
      productActions.getProductSubscriptionCodes(id, productCodes.filter)
    );
  }, [dispatch, productCodes.filter, id]);

  useEffect(() => {
    if (productCodes.fileName) {
      addMessage(
        t('productCodesSuccessfulyUploaded', {
          fileName: productCodes.fileName,
        }),
        'success'
      );
    }
  }, [productCodes.fileName, addMessage]);

  useEffect(() => {
    if (productCodes.uploadedCode) {
      addMessage(t('successCodesUploaded'), 'success');
    }
  }, [productCodes.uploadedCode, addMessage]);

  useEffect(() => {
    if (productCodes.error) {
      addMessage(t(productCodes.error?.errorCode || 'errorOccured'), 'error');
    }
  }, [productCodes.error, addMessage]);

  const formValues = useForm<FormValues>({
    criteriaMode: 'all',
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: {},
  });

  const { control, errors, handleSubmit } = formValues;

  const submit = (data) =>
    dispatch(productActions.uploadCode(id, data.code, data.numberOfUsages));

  const onFilterChange = (data) => {
    dispatch(productActions.changeFilter({ ...productCodes.filter, ...data }));
  };
  return (
    <>
      <fieldset>
        <legend>
          {t('uploadCodesForProduct')} {productCodes?.product?.title}
        </legend>
        <BoxWrap>
          <Grid container direction="column">
            <Grid container item spacing={4}>
              <Grid item xs={8} className="line-label">
                {t('uploadPromotionalCodes')}
              </Grid>
              <Grid item xs={4} className="align-right">
                <UploadBox
                  acceptedFiles=".csv"
                  onDrop={async ([file]) => {
                    let formData = new FormData();
                    formData.append('file', file);
                    dispatch(productActions.uploadCodesFromCSV(id, formData));
                  }}
                >
                  {t('uploadCodes')}
                </UploadBox>
              </Grid>
            </Grid>

            <Grid item container direction="row" xs={12}>
              <Grid item lg={5} sm={12} className="line-label">
                {t('uploadSingleMembershipCode')}
              </Grid>
              <Grid item lg={7} sm={12}>
                <form onSubmit={handleSubmit(submit)} className="full-width">
                  <Grid container direction="row" spacing={3}>
                    <Grid item xs={5}>
                      <FormTextField
                        control={control}
                        name="code"
                        label={t('subscriptionDiscountCode')}
                        rules={{
                          required: t('discountCodeRequired'),
                          maxLength: {
                            value: 45,
                            message: t('discountCodeLessThan', {
                              maxLength: 45,
                            }),
                          },
                        }}
                        errors={errors}
                      />
                    </Grid>
                    <Grid item xs={4}>
                      <FormTextField
                        control={control}
                        name="numberOfUsages"
                        label={t('numberOfUsages')}
                        type="number"
                        min="1"
                        rules={{
                          required: t('numberOfUsagesRequired'),
                        }}
                        errors={errors}
                      />
                    </Grid>
                    <Grid item xs={3} className="align-right">
                      <Button
                        className="button"
                        type="submit"
                        variant="contained"
                        color="primary"
                      >
                        {t('upload')}
                      </Button>
                    </Grid>
                  </Grid>
                </form>
              </Grid>
            </Grid>
          </Grid>
        </BoxWrap>
      </fieldset>
      <ProductCodesView
        onFilterChange={onFilterChange}
        filter={productCodes.filter}
      >
        {productCodes.data && (
          <ProductCodesTable
            productSubscriptionCodes={productCodes.data}
            pagination={productCodes.total}
            filter={productCodes.filter}
            onFilterChange={onFilterChange}
          >
            {productCodes.loading ? (
              <Loading />
            ) : !productCodes.data.length ? (
              <h2>{t('noResultsFound')}</h2>
            ) : null}
          </ProductCodesTable>
        )}
      </ProductCodesView>
    </>
  );
};
