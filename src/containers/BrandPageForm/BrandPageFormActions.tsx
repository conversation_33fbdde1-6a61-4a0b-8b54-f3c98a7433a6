import * as brandPageActionTypes from './BrandPageFormTypes';

import * as BrandPageService from '../../repository/brand-page';

export const deleteMedia = (mediaFile) => {
  return async (dispatch) => {
    dispatch({
      type: brandPageActionTypes.DELETE_MEDIA,
    });
    try {
      const response = await BrandPageService.deleteFile(mediaFile);

      dispatch({
        type: brandPageActionTypes.DELETE_MEDIA_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: brandPageActionTypes.DELETE_MEDIA_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const saveBrandPage = (
  id: number | null,
  payload: any,
  canEditPricing: boolean
) => {
  return async (dispatch) => {
    dispatch({
      type: brandPageActionTypes.SAVE_BRAND_PAGE,
    });

    const {
      // see names here
      brandPackageId,
      brandPackageTemplateId,
      coupons,
      couponUpdates,
      freeImageVideo,
      freeUnboxingVideo,
      pricePerCoupon,
      pricePerCouponUpdate,
      pricePerImageVideo,
      pricePerUnboxingVideo,
      isPaywallDisabled,
      ...brandPage
    } = payload;

    let brandPagePayload = {
      ...brandPage,
      websiteLink: brandPage?.websiteLink?.trim() || '',
      s3MediaLink: brandPage?.s3MediaLink?.trim() || '',
      s1Media: brandPage?.s1Media?.trim() || '',
      s1Link: brandPage?.s1Link?.trim() || '',
      isPaywallDisabled,
    };

    if (
      canEditPricing &&
      brandPackageTemplateId &&
      brandPackageTemplateId !== -1
    ) {
      const brandPricingPayload = {
        brandPackageTemplateId,
        coupons,
        couponUpdates,
        freeImageVideo,
        freeUnboxingVideo,
        pricePerCoupon,
        pricePerCouponUpdate,
        pricePerImageVideo,
        pricePerUnboxingVideo,
        ...(brandPackageId ? { id: brandPackageId } : null),
      };
      try {
        const pricingResponse = brandPackageId
          ? await BrandPageService.putBrandPackage(brandPricingPayload)
          : await BrandPageService.postBrandPackage(brandPricingPayload);
        dispatch({
          type: brandPageActionTypes.SAVE_BRAND_PACKAGE_SUCCESS,
          payload: pricingResponse.data,
        });
        brandPagePayload = {
          ...brandPagePayload,
          brandPackageId: pricingResponse.data.id,
        };
      } catch (error) {
        dispatch({
          type: brandPageActionTypes.SAVE_BRAND_PACKAGE_ERROR,
          payload: error.response?.data,
        });
        return;
      }
    }

    try {
      if (canEditPricing) {
        if (id) {
          await BrandPageService.editBrandPage(id, brandPagePayload);
        } else {
          await BrandPageService.createBrandPage(brandPagePayload);
        }
      } else {
        await BrandPageService.patchBrandPage(brandPagePayload);
      }
      dispatch({
        type: brandPageActionTypes.SAVE_BRAND_PAGE_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: brandPageActionTypes.SAVE_BRAND_PAGE_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const editReachBoostRequest = (
  reachBoostRequestData: BrandPageService.ReachBoostUpgradeApproval
) => {
  return async (dispatch) => {
    dispatch({
      type: brandPageActionTypes.EDIT_REACH_BOOST_REQUEST,
    });

    try {
      await BrandPageService.editReachBoostRequest(reachBoostRequestData);

      dispatch({
        type: brandPageActionTypes.EDIT_REACH_BOOST_REQUEST_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: brandPageActionTypes.EDIT_REACH_BOOST_REQUEST_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getBrandPage = (id: number, canEditPricing: boolean) => {
  return async (dispatch) => {
    dispatch({
      type: brandPageActionTypes.GET_BRAND_PAGE,
    });
    try {
      const { data: brandPage } = await BrandPageService.getBrandPage(id);
      let brandPackage = {};
      if (brandPage.public?.brandPackageId) {
        ({ data: brandPackage } = await BrandPageService.getBrandPackage(
          brandPage.public.brandPackageId
        ));
      } else if (!canEditPricing) {
        ({ data: brandPackage } = await BrandPageService.getBrandPackage(null));
      }
      dispatch({
        type: brandPageActionTypes.GET_BRAND_PAGE_SUCCESS,
        payload: { ...brandPage, ...brandPackage },
      });
    } catch (error) {
      dispatch({
        type: brandPageActionTypes.GET_BRAND_PAGE_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getBrandPackageTemplates = () => {
  return async (dispatch) => {
    try {
      const response = await BrandPageService.getBrandPackageTemplates();
      dispatch({
        type: brandPageActionTypes.GET_BRAND_PACKAGE_TEMPLATES_SUCCESS,
        payload: {
          ...response.data,
          data: [
            {
              name: '\uFEFF',
              id: null,
              brandPackageId: 0,
              coupons: 0,
              couponUpdates: 0,
              freeImageVideo: false,
              pricePerCoupon: 0,
              pricePerCouponUpdate: 0,
              pricePerImageVideo: 0,
              pricePerUnboxingVideo: 0,
            },
            ...response.data.data,
          ],
        },
      });
    } catch (error) {
      dispatch({
        type: brandPageActionTypes.GET_BRAND_PACKAGE_TEMPLATES_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearBrandPageErrors = () => ({
  type: brandPageActionTypes.CLEAR_BRAND_PAGE_ERROR_MESSAGES,
});

export const clearBrandPage = () => ({
  type: brandPageActionTypes.CLEAR_BRAND_PAGE,
});

export const clearSuccess = () => ({
  type: brandPageActionTypes.CLEAR_BRAND_PAGE_SUCCESS,
  payload: null,
});
