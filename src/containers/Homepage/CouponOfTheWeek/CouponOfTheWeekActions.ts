import * as CouponOfTheWeekTypes from './CouponOfTheWeekTypes';
import * as CouponOfTheWeekAPI from 'repository/couponOfTheWeek';

export const getCouponOfTheWeeks = (request: { page: number; size: number; filters: { status: string } }) => async (dispatch) => {
    dispatch({ type: CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEKS });
    try {
        const response = await CouponOfTheWeekAPI.getCouponOfTheWeeks(request);
        dispatch({ type: CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEKS_SUCCESS, payload: response.data });
    } catch (error) {
        dispatch({ type: CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEKS_ERROR, payload: error });
    }
};

export const getCouponOfTheWeekById = (brandId: number, couponOfTheWeekId: number) => async (dispatch) => {
    dispatch({ type: CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEK });
    try {
        const response = await CouponOfTheWeekAPI.getCouponOfTheWeekById(brandId, couponOfTheWeekId);
        dispatch({ type: CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEK_SUCCESS, payload: response });
    } catch (error) {
        dispatch({ type: CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEK_ERROR, payload: error });
    }
};

export const createCouponOfTheWeek = (data: CouponOfTheWeekAPI.CouponOfTheWeek) => async (dispatch) => {
    dispatch({ type: CouponOfTheWeekTypes.CREATE_COUPON_OF_THE_WEEK });
    try {
        const response = await CouponOfTheWeekAPI.createCouponOfTheWeek(data);
        dispatch({ type: CouponOfTheWeekTypes.CREATE_COUPON_OF_THE_WEEK_SUCCESS, payload: response });
        return response;
    } catch (error) {
        dispatch({ type: CouponOfTheWeekTypes.CREATE_COUPON_OF_THE_WEEK_ERROR, payload: error });
        throw error;
    }
};

export const updateCouponOfTheWeek = (data: CouponOfTheWeekAPI.CouponOfTheWeek) => async (dispatch) => {
    dispatch({ type: CouponOfTheWeekTypes.UPDATE_COUPON_OF_THE_WEEK });
    try {
        const response = await CouponOfTheWeekAPI.updateCouponOfTheWeek(data);
        dispatch({ type: CouponOfTheWeekTypes.UPDATE_COUPON_OF_THE_WEEK_SUCCESS, payload: response });
        return response;
    } catch (error) {
        dispatch({ type: CouponOfTheWeekTypes.UPDATE_COUPON_OF_THE_WEEK_ERROR, payload: error });
        throw error;
    }
};

export const deleteCouponOfTheWeek = (couponOfTheWeekId: number) => async (dispatch) => {
    dispatch({ type: CouponOfTheWeekTypes.DELETE_COUPON_OF_THE_WEEK });
    try {
        await CouponOfTheWeekAPI.deleteCouponOfTheWeek(couponOfTheWeekId);
        dispatch({ type: CouponOfTheWeekTypes.DELETE_COUPON_OF_THE_WEEK_SUCCESS, payload: couponOfTheWeekId });
    } catch (error) {
        dispatch({ type: CouponOfTheWeekTypes.DELETE_COUPON_OF_THE_WEEK_ERROR, payload: error });
    }
};
