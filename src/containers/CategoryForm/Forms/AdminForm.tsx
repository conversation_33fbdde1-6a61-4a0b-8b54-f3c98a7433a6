import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  Button,
  TextField,
  Grid,
  FormLabel,
  IconButton,
  DialogActions,
  DialogTitle,
  Dialog,
  DialogContent,
} from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/Delete';
import { useForm, Controller } from 'react-hook-form';
import { InputError, BoxWrap } from 'components';
import { NavLink } from 'react-router-dom';
import {
  clearCategory,
  createCategory,
  editCategory,
} from '../CategoryFormActions';
import { useParams } from 'react-router';

import { UploadBox } from 'components/UploadBox/UploadBox';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { ErrorMessage } from '@hookform/error-message';
import * as categoryService from 'repository/category';
import * as categoryActions from '../CategoryFormActions';
import { useTranslation } from 'react-i18next';

import { useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';

const isIdValid = (id) => id !== 'create';

export const AdminForm = (props) => {
  const { t }: { t: any } = useTranslation();
  const { category } = props;
  let history = useHistory();
  const dispatch = useDispatch();
  const categoriesForm = useSelector((state: any) => state.categoryForm);

  const { register, handleSubmit, errors, control, reset } = useForm({
    criteriaMode: 'all',
    mode: 'onBlur',
    reValidateMode: 'onBlur',
  });

  const [inputVals, setInputVals]: any = useState(category);
  let { id } = useParams();
  const { addMessage } = useAPIError();

  const NONE_SELECTED = t('noFileSelected');

  const parseFileName = (path) => {
    return path.split('/').slice(-1)[0].split('-').slice(2).join('');
  };

  useEffect(() => {
    if (!isIdValid(id)) {
      dispatch(clearCategory());
      setImageFileName(NONE_SELECTED);
      setIconFileName(NONE_SELECTED);
    }
  }, []);

  useEffect(() => {
    if (isIdValid(id)) {
      category?.image && setImageFileName(parseFileName(category?.image));
      category?.icon && setIconFileName(parseFileName(category?.icon));
    }
    setInputVals({ ...category });
  }, [category, reset, id]);

  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const [isIconUpdated, setIsIconUpdated] = useState(false);
  const [typeForDelete, setTypeForDelete] = useState('');
  const [delConfOpen, setDelConfOpen] = useState(false);

  const [imageFile, setImageFile] = useState<any>();
  const [imageFileName, setImageFileName] = useState(
    category?.image ? parseFileName(category.image) : NONE_SELECTED
  );

  const [iconFile, setIconFile] = useState<any>();
  const [iconFileName, setIconFileName] = useState(
    category?.icon ? parseFileName(category.icon) : NONE_SELECTED
  );

  const inputChange = (field, event) => {
    setInputVals({ ...inputVals, [field]: event.target.value });
  };

  const handleDelConfClose = () => {
    setDelConfOpen(false);
  };

  const handleDelConfOpen = () => {
    setDelConfOpen(true);
  };

  useEffect(() => {
    if (categoriesForm.error) {
      addMessage(
        t(categoriesForm.error.errorCode || 'unexpectedError'),
        'error'
      );
    } else if (
      categoriesForm.success ||
      categoriesForm.editSuccess ||
      categoriesForm.createSuccess
    ) {
      history.push('/dashboard/categories');
    }
  }, [categoriesForm, addMessage]);

  const onMediaDelete = () => {
    switch (typeForDelete) {
      case 'image':
        setIsImageUpdated(true);
        setImageFile(null);
        setImageFileName(NONE_SELECTED);
        break;
      case 'icon':
        setIsIconUpdated(true);
        setIconFile(null);
        setIconFileName(NONE_SELECTED);
        break;
    }
  };

  const submit = (data, imageLocation, iconLocation) => {
    const payload = {
      ...inputVals,
      image: imageLocation,
      icon: iconLocation,
    };

    if (!!id && isIdValid(id) && isIconUpdated) {
      dispatch(categoryActions.deleteMedia(category?.icon));
    }

    if (!!id && isIdValid(id) && isImageUpdated) {
      dispatch(categoryActions.deleteMedia(category?.image));
    }

    if (isIdValid(id)) {
      dispatch(editCategory(category['id'], payload));
    } else {
      dispatch(createCategory(payload));
    }
  };

  const uploadFile = (file, type) => {
    if (!file.length) {
      return;
    }
    switch (type) {
      case 'image':
        setIsImageUpdated(true);
        setImageFile(file[0]);
        setImageFileName(file[0].name);
        break;
      case 'icon':
        setIsIconUpdated(true);
        setIconFile(file[0]);
        setIconFileName(file[0].name);
        break;
    }
  };

  const handleUpload = (submit) => async (data) => {
    let imageLocation = category?.image;
    let iconLocation = category?.icon;

    if (!isIdValid(id) || imageFile) {
      try {
        const formData = new FormData();
        formData.append('file', imageFile);
        const response = await categoryService.uploadFile(formData);
        imageLocation = response.data.fileName;
      } catch {
        addMessage(t('errorOccured'), 'error');
        return;
      }
    }

    if (!isIdValid(id) || iconFile) {
      try {
        const formData = new FormData();
        formData.append('file', iconFile);
        const response = await categoryService.uploadFile(formData);
        iconLocation = response.data.fileName;
      } catch {
        addMessage(t('errorOccured'), 'error');
        return;
      }
    }

    submit(data, imageLocation, iconLocation);
  };

  return (
    <form onSubmit={handleSubmit(handleUpload(submit))}>
      <Grid container spacing={4}>
        <Grid item xl={12} lg={12} sm={12} xs={12}>
          <BoxWrap>
            <Grid container spacing={4}>
              <Grid item xl={12} lg={12} sm={12} xs={12}>
                <TextField
                  label={t('categoryName')}
                  fullWidth={true}
                  inputProps={{
                    name: 'name',
                  }}
                  value={inputVals.name || ''}
                  inputRef={register({
                    required: t('categoryNameRequired'),
                    maxLength: {
                      value: 128,
                      message: t('categoryNameLess128'),
                    },
                  })}
                  onChange={(event) => {
                    inputChange('name', event);
                  }}
                  helperText={
                    errors.name && <InputError message={errors.name.message} />
                  }
                />
              </Grid>

              <Grid item xl={12} lg={12} sm={12} xs={12}>
                <TextField
                  label={t('categorySlug')}
                  fullWidth={true}
                  inputProps={{
                    name: 'slug',
                  }}
                  value={inputVals.slug || ''}
                  inputRef={register({
                    required: t('categorySlugRequired'),
                    maxLength: {
                      value: 128,
                      message: t('categorySlugLess128'),
                    },
                  })}
                  onChange={(event) => {
                    inputChange('slug', event);
                  }}
                  helperText={
                    errors.slug && <InputError message={errors.slug.message} />
                  }
                />
              </Grid>

              <Grid item xl={6} lg={6} sm={6} xs={6}>
                <FormLabel
                  component="legend"
                  className="upload-image-label-grid"
                >
                  {t('categoryImage')}
                </FormLabel>
                <Controller
                  name="image"
                  control={control}
                  rules={{
                    validate: () => {
                      if (imageFileName === NONE_SELECTED) {
                        return t('categoryImageRequired');
                      } else if (imageFileName.length > 221) {
                        return t('imageNameLess221');
                      }
                      return true;
                    },
                  }}
                  as={
                    <>
                      <UploadBox
                        acceptedFiles=".png,.jpg,.jpeg,.gif"
                        onDrop={(file) => uploadFile(file, 'image')}
                      >
                        {t('browseImage')}
                      </UploadBox>
                      <div
                        style={{ display: 'inline-block', marginLeft: '10px' }}
                      >
                        {imageFileName}
                      </div>
                      {imageFileName !== NONE_SELECTED && (
                        <IconButton
                          aria-label="delete"
                          color="secondary"
                          onClick={() => {
                            handleDelConfOpen();
                            setTypeForDelete('image');
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </>
                  }
                />
                <br />
                <ErrorMessage
                  errors={errors}
                  name="image"
                  render={({ message }) => <InputError message={message} />}
                />
              </Grid>
              <Grid item xl={6} lg={6} sm={6} xs={6}>
                <FormLabel
                  component="legend"
                  className="upload-image-label-grid"
                >
                  {t('categoryIcon')}
                </FormLabel>

                <Controller
                  name="icon"
                  control={control}
                  rules={{
                    validate: () => {
                      if (iconFileName === NONE_SELECTED) {
                        return t('categoryIconRequired');
                      } else if (iconFileName.length > 221) {
                        return t('categoryIconNameLess211');
                      }
                      return true;
                    },
                  }}
                  as={
                    <>
                      <UploadBox
                        acceptedFiles=".png,.jpg,.jpeg,.gif"
                        onDrop={(file) => uploadFile(file, 'icon')}
                      >
                        {t('browseImage')}
                      </UploadBox>
                      <div
                        style={{ display: 'inline-block', marginLeft: '10px' }}
                      >
                        {iconFileName}
                      </div>
                      {iconFileName !== NONE_SELECTED && (
                        <IconButton
                          aria-label="delete"
                          color="secondary"
                          onClick={() => {
                            handleDelConfOpen();
                            setTypeForDelete('icon');
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </>
                  }
                />
                <br />
                <ErrorMessage
                  errors={errors}
                  name="icon"
                  render={({ message }) => <InputError message={message} />}
                />
              </Grid>

              <Grid item sm={4}>
                <FormLabel component="legend" style={{ marginBottom: '10px' }}>
                  {t('isPopular')}
                </FormLabel>
                <Grid container>
                  <Grid item sm={12}>
                    <label style={{ marginRight: '20px' }}>
                      <input
                        type="radio"
                        value="1"
                        name="isPopular"
                        ref={register({ required: true })}
                        onChange={(event) => inputChange('isPopular', event)}
                        checked={
                          inputVals?.isPopular === true ||
                          inputVals?.isPopular === '1'
                        }
                      />
                      {t('yes')}
                    </label>

                    <label>
                      <input
                        type="radio"
                        value="0"
                        name="isPopular"
                        ref={register({ required: true })}
                        onChange={(event) => inputChange('isPopular', event)}
                        checked={
                          inputVals?.isPopular === false ||
                          inputVals?.isPopular === '0'
                        }
                      />
                      {t('no')}
                    </label>
                  </Grid>
                </Grid>
                {errors.isPopular && (
                  <InputError message={t('isPopularIsRequired')} />
                )}
              </Grid>
            </Grid>
          </BoxWrap>
        </Grid>
      </Grid>

      <Dialog
        open={delConfOpen}
        onClose={handleDelConfClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteImage')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('warningNoUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleDelConfClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              onMediaDelete();
              handleDelConfClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>

      <Button
        className="submit-button"
        type="submit"
        variant="contained"
        color="primary"
      >
        {t('save')}
      </Button>
      <Button
        component={NavLink}
        to="/dashboard/categories/"
        className={'btn btn-cancel'}
        color="primary"
      >
        {t('cancel')}
      </Button>
    </form>
  );
};
