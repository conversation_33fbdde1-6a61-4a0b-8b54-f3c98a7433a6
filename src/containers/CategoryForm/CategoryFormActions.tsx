import {
  <PERSON><PERSON>AR_CATEGORY,
  <PERSON>LEAR_CATEGORY_SUCCESS,
  CREATE_CATEGORY,
  CREATE_CATEGORY_ERROR,
  CREATE_CATEGORY_SUCCESS,
  EDIT_CATEGORY,
  EDIT_CATEGORY_SUCCESS,
  GET_CATEGORY,
  GET_CATEGORY_ERROR,
  GET_CATEGORY_SUCCESS,
  UPLOAD_SUCCESS,
  START_UPLOAD,
  UPLOAD_ERROR,
  DELETE_MEDIA,
  DELETE_MEDIA_SUCCESS,
  DELETE_MEDIA_ERROR,
  EDIT_CATEGORY_ERROR,
} from './CategoryFormTypes';
import * as CategoryService from '../../repository/category';

export const uploadFile = (file) => {
  return async (dispatch) => {
    dispatch({
      type: START_UPLOAD,
    });

    try {
      const response = await CategoryService.uploadFile(file);
      dispatch({
        type: UPLOAD_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: UPLOAD_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const deleteMedia = (mediaFile) => {
  return async (dispatch) => {
    dispatch({
      type: DELETE_MEDIA,
    });
    try {
      const response = await CategoryService.deleteFile(mediaFile);

      dispatch({
        type: DELETE_MEDIA_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: DELETE_MEDIA_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const createCategory = (category: any) => {
  return async (dispatch) => {
    dispatch({
      type: CREATE_CATEGORY,
    });

    try {
      await CategoryService.createCategory(category);
      dispatch({
        type: CREATE_CATEGORY_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: CREATE_CATEGORY_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const editCategory = (id: number, category: any) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_CATEGORY,
    });

    try {
      await CategoryService.editCategory(id, category);
      dispatch({
        type: EDIT_CATEGORY_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: EDIT_CATEGORY_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getCategory = (id: number) => {
  return async (dispatch) => {
    dispatch({
      type: GET_CATEGORY,
    });
    try {
      const response = await CategoryService.getCategory(id);

      dispatch({
        type: GET_CATEGORY_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: GET_CATEGORY_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearCategory = () => ({
  type: CLEAR_CATEGORY,
});

export const removeError = () => ({
  type: GET_CATEGORY_ERROR,
  payload: null,
});

export const clearSuccess = () => ({
  type: CLEAR_CATEGORY_SUCCESS,
  payload: null,
});
