import React, { useState } from 'react';
import ReactPlayer from 'react-player';

import { PlayVideoModal } from 'components';

import './VideoPlayer.scss';
import { useTranslation } from 'react-i18next';

const getVideoType = (url) =>
  url.includes('youtube.com') || url.includes('youtu.be') ? 'youtube' : 'vimeo';

const youtubeUrlToEmbedFormat = (url) => {
  /* Tested with the folowing formats
  let formats = [
    'http://www.youtube.com/watch?v=-wtIMTCHWuI',
    'http://www.youtube.com/v/-wtIMTCHWuI?version=3&autohide=1',
    'http://youtu.be/-wtIMTCHWuI',
    'http://www.youtube.com/oembed?url=http%3A//www.youtube.com/watch?v%3D-wtIMTCHWuI&format=json',
    'http://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=-wtIMTCHWuI&format=json',
    'https://www.youtube.com/embed/-wtIMTCHWuI',
    'http://www.youtube.com/attribution_link?a=JdfC0C9V6ZI&u=%2Fwatch%3Fv%3DEhxJLojIE_o%26feature%3Dshare',
    'http://www.youtube.com/attribution_link?a=JdfC0C9V6ZI&u=/watch?v=EhxJLojIE_o&feature=share',
  ];
  */
  function youtubeIdParser(
    url,
    regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/
  ) {
    var match = decodeURIComponent(url).match(regExp);
    return match && match[7].length == 11 ? match[7] : false;
  }

  return `https://www.youtube.com/embed/${youtubeIdParser(url) || url}`;
};

const isVideoTypeAccepted = (videoUrl) => {
  // next line fixes the localStorage is undefined error
  if (typeof window !== 'undefined') {
    switch (getVideoType(videoUrl)) {
      case 'vimeo':
        return !!localStorage.getItem('isVimeoPlayAccepted');
      case 'youtube':
        return !!localStorage.getItem('isYoutubePlayAccepted');
      default:
        return false;
    }
  } else return false;
};

export const VideoPlayer = ({ videoUrl }) => {
  const [modal, setModal] = useState(false);
  const [play, setPlay] = useState(false);
  const videoType = getVideoType(videoUrl);
  const { t }: { t: any } = useTranslation();

  if (!videoUrl) {
    return <>{t('invalidVideoUrl')}</>;
  }

  const playVideoOnAccept = (isAccepted) => {
    setModal(false);
    if (isAccepted) {
      setPlay(true);
    }
  };

  const toggleVideoModal = (e) => {
    if (!isVideoTypeAccepted(videoUrl)) {
      setModal(true);
    }
  };

  return (
    <>
      <ReactPlayer
        url={
          videoType === 'vimeo'
            ? `${videoUrl}?autoplay=1&title=0&playsinline=1`
            : `${youtubeUrlToEmbedFormat(
                videoUrl
              )}?enablejsapi=1&version=3&playerapiid=ytplayer&autoplay=1`
        }
        playing={isVideoTypeAccepted(videoUrl)}
        width="100%"
        height="90%"
        playIcon={
          <button className="play-btn" onClick={toggleVideoModal}>
            <img src="/images/play-button.png" alt="play" />
          </button>
        }
        light={!modal && !play}
        config={{
          vimeo: {
            playerOptions: {
              frameborder: '0',
              controls: '1',
            },
          },
          youtube: {
            playerVars: {
              frameborder: '0',
            },
          },
        }}
        className={'iframe-wrapper'}
      />
      {modal && (
        <PlayVideoModal
          videoType={videoType}
          playVideoOnAccept={playVideoOnAccept}
        />
      )}
    </>
  );
};
