import React from 'react';
import { AdminForm } from './Forms/AdminForm';
import { PageTitle, Loading } from 'components';
import { useTranslation } from 'react-i18next';

import './ConsumerForm.scss';

export const ConsumerFormView = ({
  data,
  loading,
  getConsumer,
  changeSubscription,
}) => {
  const { t }: { t: any } = useTranslation();
  return (
    <div className="consumer-create-page">
      <PageTitle title={t('consumerDetails')} />
      {loading && <Loading />}
      <AdminForm
        consumerData={data}
        getConsumer={getConsumer}
        changeSubscription={changeSubscription}
      />
    </div>
  );
};
