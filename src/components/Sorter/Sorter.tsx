import React from 'react';
import { Arrow } from 'components';

import './Sorter.scss';

type SorterProps = {
  label?: string;
  handleSort: Function;
};

export const Sorter: React.FC<SorterProps> = ({ label, handleSort }) => (
  <span>
    {label}
    <span className="cell-sorter">
      <span onClick={() => handleSort('ASC')}>
        <Arrow color="#000" direction="up" />
      </span>
      <span onClick={() => handleSort('DESC')}>
        <Arrow color="#000" direction="down" />
      </span>
    </span>
  </span>
);
