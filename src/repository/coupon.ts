import API from 'services/api';

export const uploadFile = async (file, couponId = null) => {
  const url = `/media/upload?entityType=Coupon${couponId ? `&couponId=${couponId}` : ''}`;
  return await API.post(url, file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const deleteFile = async (file) => {
  return await API.delete(`/media/delete?s3FileName=${file}`, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const getCoupons = (props) => {
  let {
    pageSize = 10,
    page = 0,
    sort = { field: 'id', dir: 'ASC' },
    search = '',
  } = props;

  if (sort.field === 'validationDate') {
    sort = [
      {
        field: 'validationDate',
        dir: 'ASC',
      },
      {
        field: 'id',
        dir: 'ASC',
      },
    ];
  } else {
    sort = [sort];
  }

  return API.post('/coupons/list', {
    page,
    pageSize,
    sort,
    search,
  });
};

export const getCouponsAsBrandAdmin = (props) => {
  const {
    pageSize = 10,
    page = 0,
    sort = { field: 'id', dir: 'ASC' },
    search = '',
  } = props;
  return API.post('/brand-admin/coupons/list', {
    page,
    pageSize,
    sort: sort.field ? [{ field: sort.field, dir: sort.dir }] : [],
    search,
  });
};

// expected error messages from BE
// - internalServerError
// - unauthorized
// - accessDenied
// - invalidRequestBody --> probably has the particular error message in error.values

export const deleteCoupon = async (id) => {
  // expected error messages from BE
  // - couponNotFound (with coupon id)
  return await API.delete(`/coupons/${id}`);
};

export const getCoupon = (id) => {
  // expected error messages from BE
  // - couponNotFound (with coupon id)
  return API.get(`/coupons/${id}`);
};

export const createCoupon = (props) => {
  // expected error messages from BE
  // - characterLimitExceeded (for discount codes)
  // - categoryNotFound
  // - brandNotFound
  return API.post('/coupons', {
    ...props,
  });
};

export const editCoupon = (id, data) => {
  // expected error messages from BE
  // - couponNotFound (with coupon id)
  // - couponNotFound characterLimitExceeded (for discount codes)
  // - couponNotFound categoryNotFound
  // - couponNotFound brandNotFound
  // - couponNotFound discountCodeForCouponNotFound
  // - couponNotFound discountCodeAlreadyExists
  return API.put(`/coupons`, {
    id,
    ...data,
  });
};

export const getCouponAsBrandAdmin = (id) => {
  // expected error messages from BE
  // - couponNotFound
  return API.get(`/brand-admin/coupons/${id}`);
};

export const editCouponAsBrandAdmin = (id, data) => {
  // expected error messages from BE
  // - brandAdminNoCouponUpdates
  // - brandAdminNoCoupons
  // - characterLimitExceeded
  // - categoryNotFound
  // - discountCodeForCouponNotFound
  // - discountCodeAlreadyExists
  return API.patch(`/brand-admin/coupons`, {
    id,
    ...data,
  });
};

export const createCouponAsBrandAdmin = (props) => {
  // expected error messages from BE
  // - brandAdminNoCouponUpdates
  // - brandAdminNoCoupons
  // - characterLimitExceeded
  // - categoryNotFound
  // - brandNotFound
  return API.post('/brand-admin/coupons', {
    ...props,
  });
};

export const getCouponCreationCreditsLeft = () => {
  // expected error messages from BE
  // - brandAdminNoCouponUpdates
  // - brandAdminNoCoupons
  return API.get('/brand-admin/coupons/validation?action=create');
};

export const getCouponUpdateCreditsLeft = () => {
  // expected error messages from BE
  // - brandAdminNoCouponUpdates
  // - brandAdminNoCoupons
  return API.get('/brand-admin/coupons/validation?action=update');
};

export const deleteCouponAsBrandAdmin = async (id) => {
  return await API.delete(`/brand-admin/coupons/${id}`);
};
