import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { BrandPageFormView } from './BrandPageFormView';
import {
  getBrandPage,
  clearBrandPage,
  getBrandPackageTemplates,
} from './BrandPageFormActions';
import * as brandAccountActions from '../BrandAccount/BrandAccountActions';
import * as RoleManager from 'services/role';

export default (props) => {
  const dispatch = useDispatch();
  const brandPageForm = useSelector((state: any) => state.brandPageForm);
  const brandPackagesError = useSelector(
    (state: any) => state.brandPageForm.brandPackageError
  );
  const { t }: { t: any } = useTranslation();
  const { addMessage } = useAPIError();

  useEffect(() => {
    if (RoleManager.isAbleTo('brand_pages', 'view')) {
      dispatch(getBrandPackageTemplates());
    }
    if (props.match.params.id !== 'create') {
      dispatch(
        getBrandPage(
          props.match.params.id,
          RoleManager.isAbleTo('brand_page', 'editPricing')
        )
      );
    }
    return () => {
      dispatch(clearBrandPage());
      dispatch(brandAccountActions.clearState());
    };
  }, [dispatch, props]);

  useEffect(() => {
    if (brandPackagesError) {
      addMessage(t(brandPackagesError.errorCode), 'error');
    }
  }, [brandPackagesError]);

  if (
    brandPageForm.error &&
    brandPageForm.error.errorCode === 'brandNotFound'
  ) {
    return <Redirect to="/dashboard/404" />;
  } else {
    return (
      <>
        <BrandPageFormView loading={brandPageForm?.loading} />
      </>
    );
  }
};
