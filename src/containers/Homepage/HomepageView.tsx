import React, { useState, useEffect, useCallback } from 'react';
import { Grid } from '@material-ui/core';
import { BoxWrap } from 'components';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import BannerSlider from './BannerSlider/BannerSlider';
import CouponOfTheWeek from './CouponOfTheWeek/CouponOfTheWeek';
import PopularCoupons from './PopularCoupons/PopularCoupons';
import PopularBrands from './PopularBrands/PopularBrands';
import Categories from './Categories/Categories';
import TextBanner from './TextBanner/TextBanner';
import TestedForYou from './TestedForYou/TestedForYou';
import { getAllBrandPagesMap } from 'repository/brand-page';
import { getCoupons } from 'containers/Coupon/CouponActions';
import './Homepage.scss';
import './HomepageComponents.scss';

interface Brand {
  id: string;
  name: string;
}

export const HomepageView = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // State for brands and coupons
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(false);

  // Get coupons from Redux state
  const couponsState = useSelector((state) => state.coupons);

  // Function to fetch all brands
  const fetchBrands = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getAllBrandPagesMap();
      if (response && response.data) {
        const brandList = Object.entries(response.data).map(([id, name]) => ({
          id,
          name: String(name)
        }));
        setBrands(brandList);
      }
    } catch (error) {
      console.error('Failed to fetch brands:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Function to fetch all coupons
  const fetchAllCoupons = useCallback(() => {
    dispatch(getCoupons({
      pageSize: 1000,
      page: 0
    }));
  }, [dispatch]);

  // Fetch data on component mount
  useEffect(() => {
    fetchBrands();
    fetchAllCoupons();
  }, [fetchBrands, fetchAllCoupons]);

  return (
    <div className="homepage-container">
      <h1 style={{ fontSize: '20px', marginBottom: '15px' }}>{t('homepage')}</h1>
      <Grid container spacing={1}>
        <Grid item xs={12}>
          <BoxWrap>
            <BannerSlider brands={brands} />
          </BoxWrap>
        </Grid>
        <Grid item xs={12}>
          <BoxWrap>
            <CouponOfTheWeek
              brands={brands}
              allCoupons={couponsState?.data || []}
            />
          </BoxWrap>
        </Grid>
        <Grid item xs={12}>
          <BoxWrap>
            <PopularCoupons
              allCoupons={couponsState?.data || []}
            />
          </BoxWrap>
        </Grid>
        <Grid item xs={12}>
          <BoxWrap>
            <PopularBrands
              brands={brands}
            />
          </BoxWrap>
        </Grid>
        <Grid item xs={12}>
          <BoxWrap>
            <Categories allCoupons={couponsState?.data || []} />
          </BoxWrap>
        </Grid>
        <Grid item xs={12}>
          <BoxWrap>
            <TextBanner />
          </BoxWrap>
        </Grid>
        <Grid item xs={12}>
          <BoxWrap>
            <TestedForYou brands={brands} />
          </BoxWrap>
        </Grid>
      </Grid>
    </div>
  );
};
