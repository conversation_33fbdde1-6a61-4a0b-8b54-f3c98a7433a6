import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import CategoryRow from './CategoryRow';
import * as CategoriesActions from './CategoriesActions';
import * as CategoryService from 'repository/category';

interface Category {
  id: string;
  name: string;
}

interface Coupon {
  id: string | number;
  brandName?: string;
  categoryName?: string;
  amountCondition?: string;
}

interface CategoryRowData {
  id?: string;
  categoryId: string | number;
  selectedCoupons: Coupon[];
}

interface CategoriesProps {
  allCoupons: Coupon[];
}

const Categories: React.FC<CategoriesProps> = ({ allCoupons }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // Use Redux state with useSelector
  const categoriesState = useSelector((state: any) => state.homepageCategories);

  // Local state
  const [mappedCategories, setMappedCategories] = useState<CategoryRowData[]>([
    { id: `temp_${Date.now()}`, categoryId: '', selectedCoupons: [] }
  ]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; id: string | null }>({ open: false, id: null });
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Function to fetch categories data
  const fetchCategories = useCallback(() => {
    setLoadingCategories(true);
    try {
      console.log('Fetching categories data...');
      // Dispatch the action to fetch categories data
      dispatch(CategoriesActions.getCategories());
    } catch (e) {
      console.error('Error fetching categories:', e);
      setSnackbar({
        open: true,
        message: 'Failed to load categories',
        severity: 'error'
      });
    }
  }, [dispatch]);

  // Effect to fetch data on component mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Effect to fetch categories from repository for dropdown
  useEffect(() => {
    const loadCategoriesForDropdown = async () => {
      setLoadingCategories(true);
      try {
        // Use the proper API call to get categories
        console.log('Fetching categories for dropdown...');
        const response = await CategoryService.getCategories({ pageSize: 100 });
        console.log('Categories API response:', response);

        if (response && response.data) {
          const categoryList = response.data.data.map((cat: any) => ({
            id: cat.id.toString(),
            name: cat.name
          }));
          console.log('Mapped categories for dropdown:', categoryList);

          // Log the first few categories to verify the structure
          if (categoryList.length > 0) {
            console.log('Sample category:', categoryList[0]);
          }

          setCategories(categoryList);
        }
      } catch (e) {
        console.error('Error loading categories:', e);
        setSnackbar({
          open: true,
          message: 'Failed to load categories',
          severity: 'error'
        });
      } finally {
        setLoadingCategories(false);
      }
    };
    loadCategoriesForDropdown();
  }, []);

  // Effect to map categories data from Redux to component state
  useEffect(() => {
    console.log('Categories state changed:', categoriesState);

    const categoriesData = categoriesState?.homepageCategories;
    console.log('Homepage categories data from Redux:', categoriesData);

    if (categoriesData && categoriesData.length > 0) {
      // Map the API response to the component's state format
      const mapped = categoriesData.map((item: any) => {
        console.log('Processing API item:', item);

        // Map the coupons
        const selectedCoupons = item.couponIds.map((couponId: string | number) => {
          const foundCoupon = allCoupons.find(c => c.id.toString() === couponId.toString());
          return foundCoupon || { id: couponId };
        });

        return {
          id: item.id.toString(),
          categoryId: item.categoryId.toString(),
          selectedCoupons
        };
      });

      console.log('Mapped categories:', mapped);

      // Set the mapped categories
      setMappedCategories(mapped);
    } else if (!categoriesState?.loading && categoriesData && categoriesData.length === 0) {
      // If no data and not loading, set a default empty category
      setMappedCategories([{ id: `temp_${Date.now()}`, categoryId: '', selectedCoupons: [] }]);
    }
  }, [categoriesState, categories, allCoupons]);

  // Effect to handle Redux state changes for success/error messages
  useEffect(() => {
    const error = categoriesState?.error;
    // Only show error if we're not in the middle of a save operation and there's actually an error
    if (error && !loading && categoriesState?.lastAction && categoriesState.lastAction.includes('ERROR')) {
      setSnackbar({
        open: true,
        message: 'An error occurred with categories',
        severity: 'error'
      });
    }
  }, [categoriesState, loading]);

  const handleDeleteCategory = (id: string | number | undefined) => {
    if (id === undefined) return;
    setDeleteDialog({ open: true, id: id.toString() });
  };

  const confirmDelete = async () => {
    if (!deleteDialog.id) return;

    const categoryToDelete = mappedCategories.find(c => c.id === deleteDialog.id);
    if (!categoryToDelete) return;

    try {
      setLoading(true);

      // Call the API to delete the category
      await dispatch(CategoriesActions.deleteCategory(Number(categoryToDelete.id)));

      // Remove from local state
      setMappedCategories(prev => prev.filter(row => row.id !== deleteDialog.id));

      // Show success message
      setSnackbar({
        open: true,
        message: 'Category row deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      setSnackbar({
        open: true,
        message: 'Failed to delete category row',
        severity: 'error'
      });
    } finally {
      setLoading(false);
      // Close the dialog
      setDeleteDialog({ open: false, id: null });
    }
  };

  const handleAddNew = () => {
    if (mappedCategories.length === 0) {
      setMappedCategories([
        { id: `temp_${Date.now()}`, categoryId: '', selectedCoupons: [] }
      ]);
      return;
    }

    const lastCategory = mappedCategories[mappedCategories.length - 1];
    const allEmpty = !lastCategory.categoryId && lastCategory.selectedCoupons.length === 0;

    if (allEmpty) {
      return;
    }

    setMappedCategories([
      ...mappedCategories,
      { id: `temp_${Date.now()}`, categoryId: '', selectedCoupons: [] }
    ]);
  };

  const validateCategories = () => {
    const missingFields = mappedCategories.some(c => {
      return !c.categoryId || c.selectedCoupons.length === 0;
    });

    if (missingFields) {
      setSnackbar({
        open: true,
        message: 'Please select a category and at least one coupon for all rows.',
        severity: 'error'
      });
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateCategories()) {
      return;
    }

    try {
      setLoading(true);

      // Process all categories
      await Promise.all(mappedCategories.map(async (c) => {

        // Make sure we have a valid categoryId
        if (!c.categoryId) {
          console.error('Missing category data for row:', c);
          throw new Error('Missing category data');
        }

        // Check if this is a temporary ID (starts with 'temp_')
        const isTemporaryId = c.id && typeof c.id === 'string' && c.id.startsWith('temp_');
        console.log(`Row ID: ${c.id}, Is temporary: ${isTemporaryId}`);

        // For create operation - don't include id for new categories or temporary IDs
        if (!c.id || isTemporaryId) {
          const createData = {
            categoryId: Number(c.categoryId),
            couponIds: c.selectedCoupons.map(coupon => Number(coupon.id))
          };
          console.log('Creating new category with data:', createData);
          await dispatch(CategoriesActions.createCategory(createData));
        }
        // For update operation - include id for existing categories
        else {
          const updateData = {
            id: Number(c.id),
            categoryId: Number(c.categoryId),
            couponIds: c.selectedCoupons.map(coupon => Number(coupon.id))
          };
          console.log('Updating existing category with data:', updateData);
          await dispatch(CategoriesActions.updateCategory(updateData));
        }
      }));

      setSnackbar({
        open: true,
        message: 'Categories saved successfully',
        severity: 'success'
      });

      // Refresh data from server
      fetchCategories();

    } catch (error) {
      console.error('Error saving categories:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save categories',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper functions for CategoryRow
  const handleAddCategory = (rowId: string | undefined, category: Category | null) => {
    if (rowId === undefined) return;

    setMappedCategories(prev => prev.map(row =>
      row.id === rowId ? { ...row, categoryId: category ? category.id : '' } : row
    ));
  };

  const handleAddCoupon = (rowId: string | undefined, coupon: Coupon | null) => {
    console.log('handleAddCoupon called with rowId:', rowId, 'coupon:', coupon);

    if (!rowId || !coupon) {
      console.log('Returning early because rowId or coupon is missing');
      return;
    }

    // Log all rows for debugging
    console.log('All rows before update:', mappedCategories);

    // Find the row to make sure it exists
    const rowToUpdate = mappedCategories.find(row => {
      const match = row.id === rowId;
      console.log(`Comparing row.id: "${row.id}" with rowId: "${rowId}" - Match: ${match}`);
      return match;
    });

    if (!rowToUpdate) {
      console.log('Row not found with ID:', rowId);
      console.log('Available rows:', mappedCategories.map(r => r.id));

      // As a fallback, try to find the row by index if it's a new row
      if (rowId.startsWith('temp_')) {
        const index = mappedCategories.findIndex(r => r.id && r.id.startsWith('temp_'));
        if (index >= 0) {
          console.log('Found row by temp prefix at index:', index);
          const tempRowId = mappedCategories[index].id;

          // Add coupon to this row instead
          if (tempRowId) {
            addCouponToRow(tempRowId, coupon);
          }
          return;
        }
      }
      return;
    }

    // Check if coupon is already selected
    const isCouponAlreadySelected = rowToUpdate.selectedCoupons.some(
      selected => selected.id.toString() === coupon.id.toString()
    );

    if (isCouponAlreadySelected) {
      console.log('Coupon already selected:', coupon);
      return;
    }

    console.log('Updating row:', rowToUpdate);

    // Add the coupon to the row
    addCouponToRow(rowId, coupon);
  };

  // Helper function to add a coupon to a specific row
  const addCouponToRow = (rowId: string, coupon: Coupon) => {
    setMappedCategories(prev => {
      const updated = prev.map(row =>
        row.id === rowId ? {
          ...row,
          selectedCoupons: [...row.selectedCoupons, coupon]
        } : row
      );
      console.log('Updated categories:', updated);
      return updated;
    });
  };

  const handleRemoveCoupon = (rowId: string | undefined, couponId: string | number) => {
    if (rowId === undefined) return;

    setMappedCategories(prev => prev.map(row =>
      row.id === rowId ? {
        ...row,
        selectedCoupons: row.selectedCoupons.filter(c => c.id !== couponId)
      } : row
    ));
  };

  const handleDeleteRow = (rowId: string | undefined) => {
    if (rowId === undefined) return;

    // Find the category row
    const categoryRow = mappedCategories.find(c => c.id === rowId);

    if (!categoryRow) return;

    // Check if it's a temporary ID (starts with 'temp_')
    const isTemporaryId = categoryRow.id && typeof categoryRow.id === 'string' && categoryRow.id.startsWith('temp_');

    // Check if it's a new row (empty ID or temporary ID) or an existing one from the backend
    if (!categoryRow.id || isTemporaryId) {
      // For new rows, delete immediately without confirmation
      setMappedCategories(prev => prev.filter(row => row.id !== rowId));
      setSnackbar({
        open: true,
        message: 'Category row deleted',
        severity: 'success'
      });
    } else {
      // For existing rows, show confirmation dialog
      handleDeleteCategory(rowId);
    }
  };

  // Log the current state of mappedCategories for debugging
  console.log('Current mappedCategories:', mappedCategories);

  return (
    <div className="categories-section">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 className="section-title" style={{ margin: 0 }}>{t('categories')}</h2>
        <Button
          variant="contained"
          className="save-button"
          onClick={handleSave}
          disabled={categoriesState?.loading || loading}
        >
          {(categoriesState?.loading || loading) ? 'Saving...' : 'SAVE'}
        </Button>
      </div>

      {(categoriesState?.loading || loading) && (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          Loading...
        </div>
      )}

      <div>
        {mappedCategories.map((row, idx) => {
          console.log(`Rendering row ${idx} with ID:`, row.id);
          return (
            <CategoryRow
              key={row.id || idx}
              row={row}
              categories={categories}
              allCoupons={allCoupons}
              loadingCategories={loadingCategories}
              onCategoryChange={cat => handleAddCategory(row.id, cat)}
              onAddCoupon={coupon => handleAddCoupon(row.id, coupon)}
              onRemoveCoupon={couponId => handleRemoveCoupon(row.id, couponId)}
              onDeleteRow={() => handleDeleteRow(row.id)}
            />
          );
        })}
        <Button
          variant="contained"
          className="add-button"
          onClick={handleAddNew}
          disabled={categoriesState?.loading || loading}
        >
          + {t('addNewCategory')}
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, id: null })}>
        <DialogTitle>{t('Delete Category?')}</DialogTitle>
        <DialogContent>{t('Are you sure you want to delete this category?')}</DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, id: null })}>{t('Cancel')}</Button>
          <Button color="secondary" onClick={confirmDelete}>{t('Delete')}</Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          style={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default Categories;
