import {
  CONSUMER_START,
  CONSUMER_SUCCESS,
  CONSUMER_ERROR,
  GET_CONSUMERS,
  GET_CONSUMERS_SUCCESS,
  CONSUMER_PAGINATION,
  GET_CONSUMERS_ERROR,
  DELETE_CONSUMER,
  <PERSON>LETE_CONSUMER_ERROR,
  DELETE_CONSUMER_SUCCESS,
  EXPORT_CONSUMERS,
  EXPORT_CONSUMERS_ERROR,
  EXPORT_CONSUMERS_SUCCESS,
  CHANGE_PAGINATION,
} from './ConsumerTypes';

import * as ConsumerService from 'repository/consumer';

export const changeFilter = (filter) => {
  return async (dispatch) => {
    dispatch({
      type: CHANGE_PAGINATION,
      payload: {
        page: filter.page,
        pageSize: filter.pageSize,
        sort: { ...filter.sort },
      },
    });
  };
};

export const getConsumers = (filter = {}) => {
  return async (dispatch) => {
    dispatch({
      type: GET_CONSUMERS,
    });

    try {
      const response = await ConsumerService.getConsumers(filter);
      dispatch({
        type: GET_CONSUMERS_SUCCESS,
        payload: response.data.data,
      });

      dispatch({
        type: CONSUMER_PAGINATION,
        payload: {
          current: response.data.number + 1,
          first: 1,
          last: response.data.total,
        },
      });
    } catch (error) {
      dispatch({
        type: GET_CONSUMERS_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const deleteConsumer = (consumer) => {
  return async (dispatch) => {
    dispatch({
      type: DELETE_CONSUMER,
    });
    try {
      const response = await ConsumerService.deleteConsumer(consumer);
      dispatch({
        type: DELETE_CONSUMER_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: DELETE_CONSUMER_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const exportConsumers = (type, id?) => {
  return async (dispatch) => {
    dispatch({
      type: EXPORT_CONSUMERS,
    });
    try {
      let response;
      switch (type) {
        case 'allConsumers':
          response = await ConsumerService.exportAllConsumers();
          break;
        case 'allCategories':
          response = await ConsumerService.exportConsumersPerCategory(-1);
          break;
        case 'perCategory':
          response = await ConsumerService.exportConsumersPerCategory(id);
          break;
        case 'allBrands':
          response = await ConsumerService.exportConsumersPerBrand(-1);
          break;
        case 'perBrand':
          response = await ConsumerService.exportConsumersPerBrand(id);
          break;
        case 'allUsers':
          response = await ConsumerService.exportActivityOfAllUsers();
          break;
        case 'subscribed':
          response =
            await ConsumerService.exportNonRegisteredAndSubcribedToNewsletter();
          break;
      }
      const fileName = response.headers['content-disposition']
        .split('filename="')[1]
        .split('"')[0];
      dispatch({
        type: EXPORT_CONSUMERS_SUCCESS,
        payload: response.data,
      });
      fileDownload(response.data, fileName);
    } catch (error) {
      dispatch({
        type: EXPORT_CONSUMERS_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

const fileDownload = (data, filename) => {
  const blob = new Blob([data], {
    type: 'text/csv',
  });

  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    window.navigator.msSaveBlob(blob, filename);
  } else {
    const blobURL = window.URL.createObjectURL(blob);

    let tempLink = document.createElement('a');
    tempLink.style.display = 'none';
    tempLink.href = blobURL;
    tempLink.setAttribute('download', filename);

    if (typeof tempLink.download === 'undefined') {
      tempLink.setAttribute('target', '_blank');
    }

    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    window.URL.revokeObjectURL(blobURL);
  }
};
