import React from 'react';
import { Link } from 'react-router-dom';
import { useParams } from 'react-router';
import { useTranslation } from 'react-i18next';

import TrafficoLogo from '../../assets/images/traffico_logo.jpeg';

import './InvalidToken.scss';

export default () => {
  const { t }: { t: any } = useTranslation();
  const { reason } = useParams();

  return (
    <div className="page-not-found">
      <div className="box">
        <img src={TrafficoLogo} alt='traffico_logo' />

        <h1>{t('error')}</h1>
        <h3>
          {reason === 'expired'
            ? t('tokenExpired')
            : reason === 'invalid'
            ? t('tokenInvalid')
            : t('unknownTokenError')}
        </h3>
        <Link to="/">&larr; {t('backToHome')}</Link>
      </div>
    </div>
  );
};
