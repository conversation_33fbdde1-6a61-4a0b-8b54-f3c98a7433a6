import * as React from 'react';

function OutlineHeartIcon(props) {
  return (
    <svg
      data-name="Ebene 1"
      viewBox="0 0 250 250"
      width="1em"
      height="1em"
      {...props}
    >
      <circle
        cx={124.37}
        cy={125.79}
        r={121.21}
        fill="#fff"
        data-name="Ellipse 4"
      />
      <g data-name="Gruppe 245">
        <g data-name="Gruppe 245-2">
          <path
            d="M124.37 191.92a10.43 10.43 0 01-6.73-2.56 1236.7 1236.7 0 00-15-12.86C76.09 153.74 57 137.78 57 113.34A39.66 39.66 0 0194.74 72h1a39.59 39.59 0 0128.62 13.47A39 39 0 01152.59 72a39.8 39.8 0 0139.12 40.4v1c0 24.11-19.12 40.41-45.52 62.76l-15.08 12.93a10.13 10.13 0 01-6.74 2.83zm-2-12.66zM95.75 85.39h-.33a26.26 26.26 0 00-18.18 8.69A25.81 25.81 0 0070.5 113c0 18.52 16.23 32.32 40.81 53.2l13.06 11.24c4.51-4 8.89-7.74 13.07-11.24 24.57-20.88 40.8-34.68 40.8-52.86v-1a26.33 26.33 0 00-25.65-26.94 25.38 25.38 0 00-22.09 15.15h-12.32a25.46 25.46 0 00-22.43-15.16z"
            data-name="Pfad 274"
          />
        </g>
      </g>
    </svg>
  );
}

export default OutlineHeartIcon;
