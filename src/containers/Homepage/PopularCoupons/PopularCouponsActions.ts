import * as PopularCouponsTypes from './PopularCouponsTypes';
import * as PopularEntitiesAPI from 'repository/popularEntities';

export const getPopularCoupons = (request?: PopularEntitiesAPI.PopularEntitiesListRequest) => async (dispatch) => {
    dispatch({ type: PopularCouponsTypes.GET_POPULAR_COUPONS });
    try {
        const response = await PopularEntitiesAPI.getPopularCoupons(request);
        dispatch({ type: PopularCouponsTypes.GET_POPULAR_COUPONS_SUCCESS, payload: response });
        return response;
    } catch (error) {
        console.error('Error fetching popular coupons:', error);
        dispatch({ type: PopularCouponsTypes.GET_POPULAR_COUPONS_ERROR, payload: error });
        // Return an empty response instead of throwing to prevent component errors
        return { couponIds: [] };
    }
};

export const updatePopularCoupons = (data: PopularEntitiesAPI.PopularCoupons) => async (dispatch) => {
    dispatch({ type: PopularCouponsTypes.UPDATE_POPULAR_COUPONS });
    try {
        await PopularEntitiesAPI.updatePopularCoupons(data);
        dispatch({ type: PopularCouponsTypes.UPDATE_POPULAR_COUPONS_SUCCESS, payload: data });
        return data;
    } catch (error) {
        console.error('Error updating popular coupons:', error);
        dispatch({ type: PopularCouponsTypes.UPDATE_POPULAR_COUPONS_ERROR, payload: error });
        throw error;
    }
};
