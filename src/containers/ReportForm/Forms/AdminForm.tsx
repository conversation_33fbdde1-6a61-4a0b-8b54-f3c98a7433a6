import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  Button,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
} from '@material-ui/core';

import { getBrandPagesMap } from '../../BrandPages/BrandPagesActions';
import { getCategoriesMap } from '../../Categories/CategoriesActions';
import { exportConsumers } from '../../Consumer/ConsumerActions';
import useAPIError from 'components/APIErrorNotification/useAPIError';

import { BoxWrap } from 'components';

export const AdminForm = () => {
  const dispatch = useDispatch();
  const { t }: { t: any } = useTranslation();
  const { addMessage } = useAPIError();

  const brandPages = useSelector((state: any) => state.brandPages);
  const categories = useSelector((state: any) => state.categories);
  const mapBrandPagesError = useSelector(
    (state: any) => state.brandPages.errorMap
  );

  const [brandPageId, setBrandPageId] = useState(-1);
  const [categoryId, setCategoryId] = useState(-1);

  useEffect(() => {
    dispatch(getBrandPagesMap());
    dispatch(getCategoriesMap());
  }, []);

  useEffect(() => {
    if (!brandPages?.map?.length && !brandPages?.loadingMap) {
      dispatch(getBrandPagesMap());
    }
  }, [dispatch, brandPages]);

  useEffect(() => {
    if (!categories?.map?.length && !categories?.loadingMap) {
      dispatch(getCategoriesMap());
    }
  }, [dispatch, categories]);

  useEffect(() => {
    if (mapBrandPagesError) {
      addMessage(t(mapBrandPagesError.errorCode || 'unexpectedError'), 'error');
    }
  }, [mapBrandPagesError, addMessage]);

  const exportToCSV = (type) => {
    switch (type) {
      case 'category':
        if (categoryId === -1) {
          dispatch(exportConsumers('allCategories'));
        } else {
          dispatch(exportConsumers('perCategory', categoryId));
        }
        break;
      case 'brand':
        if (brandPageId === -1) {
          dispatch(exportConsumers('allBrands'));
        } else {
          dispatch(exportConsumers('perBrand', brandPageId));
        }
        break;
      case 'allUsers':
        dispatch(exportConsumers('allUsers'));
        break;
      case 'subscribed':
        dispatch(exportConsumers('subscribed'));
        break;
    }
  };

  return (
    <form>
      <fieldset>
        <legend>{t('perCategory')}</legend>
        <BoxWrap>
          <Grid container spacing={4}>
            <Grid item xs={8}>
              <FormControl fullWidth margin="normal">
                <InputLabel htmlFor="categoryId">{t('category')}</InputLabel>
                <Select
                  inputProps={{ name: 'categoryId' }}
                  fullWidth={true}
                  defaultValue={-1}
                  onChange={(event: any) => {
                    const value = event.target.value;
                    setCategoryId(value);
                  }}
                >
                  <MenuItem key={-1} value={-1}>
                    {t('allCategories')}
                  </MenuItem>
                  {categories?.map?.length
                    ? categories.map.map((category) => (
                        <MenuItem key={category.id} value={category.id}>
                          {category.name}
                        </MenuItem>
                      ))
                    : null}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={4}>
              <Button onClick={() => exportToCSV('category')}>
                {t('exportToCSV')}
              </Button>
            </Grid>
          </Grid>
        </BoxWrap>
      </fieldset>
      <fieldset>
        <legend> {t('perBrandPage')}</legend>
        <BoxWrap>
          <Grid container spacing={4}>
            <Grid item xs={8}>
              <FormControl fullWidth margin="normal">
                <InputLabel htmlFor="brandId">{t('brandPage')}</InputLabel>
                <Select
                  inputProps={{ name: 'brandId' }}
                  fullWidth={true}
                  defaultValue={-1}
                  onChange={(event: any) => {
                    const value = event.target.value;
                    setBrandPageId(value);
                  }}
                >
                  <MenuItem key={-1} value={-1}>
                    {t('allBrandPages')}
                  </MenuItem>
                  {brandPages?.map?.length
                    ? brandPages.map.map((brand) => (
                        <MenuItem key={brand.id} value={brand.id}>
                          {brand.name}
                        </MenuItem>
                      ))
                    : null}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={4}>
              <Button onClick={() => exportToCSV('brand')}>
                {t('exportToCSV')}
              </Button>
            </Grid>
          </Grid>
        </BoxWrap>
      </fieldset>

      <fieldset>
        <legend>{t('userActivity')}</legend>
        <BoxWrap>
          <Grid container spacing={4}>
            <Grid item xs={8}>
              {t('exportSummarizedReport')}
            </Grid>
            <Grid item xs={4}>
              <Button onClick={() => exportToCSV('allUsers')}>
                {t('exportToCSV')}
              </Button>
            </Grid>
          </Grid>
        </BoxWrap>
      </fieldset>

      <fieldset>
        <legend>{t('newsletterSubscriptions')}</legend>
        <BoxWrap>
          <Grid container spacing={4}>
            <Grid item xs={8}>
              {t('exportListAllUsersSubscribedToNewsletter')}
            </Grid>
            <Grid item xs={4}>
              <Button onClick={() => exportToCSV('subscribed')}>
                {t('exportToCSV')}
              </Button>
            </Grid>
          </Grid>
        </BoxWrap>
      </fieldset>
    </form>
  );
};
