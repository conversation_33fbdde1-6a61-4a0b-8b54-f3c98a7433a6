import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { CategoryFormView } from './CategoryFormView';
import { getCategory } from './CategoryFormActions';
import { CLEAR_CATEGORY_ERROR_MESSAGES } from './CategoryFormTypes';

export default (props) => {
  const dispatch = useDispatch();
  const categoryForm = useSelector((state: any) => state.categoryForm);

  useEffect(() => {
    dispatch({ type: CLEAR_CATEGORY_ERROR_MESSAGES });
    if (props.match.params.id && props.match.params.id !== 'create') {
      dispatch(getCategory(props.match.params.id));
    }
    return () => {};
  }, []);

  if (
    categoryForm.error &&
    categoryForm.error.errorCode === 'categoryNotFound'
  ) {
    return <Redirect to="/dashboard/categories/404" />;
  } else {
    return (
      <>
        <CategoryFormView data={categoryForm} loading={categoryForm?.loading} />
      </>
    );
  }
};
