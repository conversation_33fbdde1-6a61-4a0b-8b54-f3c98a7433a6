import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, IconButton, Chip } from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/Delete';
import { Autocomplete } from '@material-ui/lab';
import { useForm, Controller } from 'react-hook-form';

interface Category {
  id: string;
  name: string;
}

interface CouponOption {
  id: string | number;
  brandName?: string;
  categoryName?: string;
  amountCondition?: string;
}

interface CategoryRowData {
  id?: string;
  categoryId: string | number;
  selectedCoupons: CouponOption[];
}

interface CategoryRowProps {
  row: CategoryRowData;
  categories: Category[];
  allCoupons: CouponOption[];
  loadingCategories: boolean;
  onCategoryChange: (category: Category | null) => void;
  onAddCoupon: (coupon: CouponOption | null) => void;
  onRemoveCoupon: (couponId: string | number) => void;
  onDeleteRow: () => void;
  brandFilter?: string[]; // Array of brand names to filter coupons by
}

const labelStyle = { fontSize: 12, marginBottom: 2 };
const inputStyle = {
  borderRadius: '12px',
  fontSize: 11,
  height: 28
};

const getCouponDisplay = (coupon: CouponOption) => {
  if (!coupon) return '';
  return `${coupon.categoryName || ''}-${coupon.amountCondition || ''}`;
};

const CategoryRow: React.FC<CategoryRowProps> = ({
  row,
  categories,
  allCoupons,
  loadingCategories,
  onCategoryChange,
  onAddCoupon,
  onRemoveCoupon,
  onDeleteRow,
  brandFilter = []
}) => {
  // Local state for coupon input value
  const [couponInputValue, setCouponInputValue] = useState('');

  // Find the selected category from the categoryId
  const selectedCategory = categories.find(cat => cat.id.toString() === row.categoryId?.toString()) || null;

  const { control, setValue } = useForm({
    defaultValues: {
      category: selectedCategory,
      coupon: null
    },
  });

  // Filter available coupons by brand and exclude already selected ones
  const availableCoupons = allCoupons.filter(coupon => {
    // Exclude already selected coupons
    const isNotSelected = !row.selectedCoupons.some(selected => selected.id === coupon.id);

    // If brandFilter is provided and not empty, filter by brand
    if (brandFilter.length > 0) {
      const matchesBrand = coupon.brandName && brandFilter.some(brand =>
        coupon.brandName?.toLowerCase() === brand.toLowerCase()
      );
      return isNotSelected && matchesBrand;
    }

    // If no brand filter, just exclude selected coupons
    return isNotSelected;
  });

  // Log available categories for debugging
  console.log('Available categories:', categories);

  // Update form values when row changes
  useEffect(() => {
    console.log('Row categoryId changed:', row.categoryId);
    const category = categories.find(cat => cat.id.toString() === row.categoryId?.toString()) || null;
    if (category && setValue) {
      console.log('Setting category value to:', category);
      setValue('category', category);
    }
  }, [row.categoryId, categories, setValue]);

  return (
    <div className="category-row" style={{
      backgroundColor: '#f8f9fa',
      border: '1px solid #707070',
      borderRadius: '12px',
      padding: '20px',
      marginBottom: '10px',
      display: 'flex',
      justifyContent: 'center' // Center-align content like CouponRow
    }}>
      <div className="category-content" style={{ display: 'flex', width: '100%' }}>
        <div style={{ width: '20%', marginRight: '2%' }}>
          <FormLabel style={labelStyle}>Category</FormLabel>
          <Controller
            name="category"
            control={control}
            render={({ onChange, value }) => (
              <Autocomplete
                options={categories}
                getOptionLabel={(option) => option.name}
                loading={loadingCategories}
                value={value}
                onChange={(_event, newValue) => {
                  console.log('Category selected:', newValue);
                  onChange(newValue);
                  onCategoryChange(newValue);
                  console.log('Category selection complete');
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    variant="outlined"
                    size="small"
                    placeholder={"Search"}
                    InputProps={{
                      ...params.InputProps,
                      style: inputStyle
                    }}
                    inputProps={{
                      ...params.inputProps,
                      style: { fontSize: 11, minHeight: 28 }
                    }}
                  />
                )}
                ListboxProps={{
                  style: {
                    fontSize: 11,
                    borderRadius: 8,
                    boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                  }
                }}
              />
            )}
          />
        </div>

        <div style={{ width: '20%', marginRight: '2%' }}>
          <FormLabel style={labelStyle}>Coupon</FormLabel>
          <Controller
            name="coupon"
            control={control}
            render={({ onChange, value }) => (
              <Autocomplete
                options={availableCoupons}
                getOptionLabel={(option) => getCouponDisplay(option)}
                value={value}
                onChange={(_event, newValue) => {
                  console.log('Coupon selected in dropdown:', newValue);
                  if (newValue) {
                    // First update the form value
                    onChange(newValue);

                    // Then call the parent component's handler to add the coupon
                    console.log('Calling onAddCoupon with:', newValue);
                    onAddCoupon(newValue);

                    // Reset the dropdown value to null
                    console.log('Setting coupon value to null');
                    setValue('coupon', null);

                    // Also clear the input value
                    setCouponInputValue('');
                  }
                }}
                inputValue={couponInputValue}
                onInputChange={(_event, newInputValue) => setCouponInputValue(newInputValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    variant="outlined"
                    size="small"
                    placeholder={"Search"}
                    InputProps={{
                      ...params.InputProps,
                      style: inputStyle
                    }}
                    inputProps={{
                      ...params.inputProps,
                      style: { fontSize: 11, minHeight: 28 }
                    }}
                  />
                )}
                ListboxProps={{
                  style: {
                    maxHeight: 200,
                    fontSize: 11,
                    overflowY: 'auto',
                    borderRadius: 8,
                    boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                  }
                }}
              />
            )}
          />
        </div>

        <div style={{ width: '50%', display: 'flex', alignItems: 'flex-end' }}>
          <div className="tag-container" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '8px',
            padding: '8px 12px',
            border: '1px solid #707070',
            borderRadius: '12px',
            backgroundColor: '#fff',
            minHeight: 28,
            width: '100%'
          }}>
            {row.selectedCoupons.map((coupon) => (
              <Chip
                key={coupon.id}
                label={getCouponDisplay(coupon)}
                onDelete={() => onRemoveCoupon(coupon.id)}
                className="tag"
                size="small"
                style={{
                  borderRadius: '12px',
                  fontSize: 11,
                  height: 28,
                  backgroundColor: '#f5f5f5'
                }}
                deleteIcon={<DeleteIcon style={{ color: '#757575' }} />}
              />
            ))}
          </div>
        </div>

        <div className="category-actions" style={{ marginLeft: 'auto', paddingLeft: '10px' }}>
          <IconButton
            onClick={onDeleteRow}
            style={{ color: '#757575' }}
          >
            <DeleteIcon />
          </IconButton>
        </div>
      </div>
    </div>
  );
};

export default CategoryRow;
