import * as BannerSliderActionTypes from './BannerSliderTypes';
import * as BannerSliderService from 'repository/bannerSlider';

interface Filter {
  page?: number;
  pageSize?: number;
  sort?: any;
}

interface ImageUploadResponse {
  fileName: string;
}

// New interface to track image changes
interface BannerImageChanges {
  desktopBanner?: {
    file: File;
    oldPath?: string;
  };
  mobileBanner?: {
    file: File;
    oldPath?: string;
  };
}

export const uploadBannerImage = (file: File, bannerId?: number, imageType: 'desktop' | 'mobile' = 'desktop') => {
  return async (dispatch: any) => {
    dispatch({
      type: BannerSliderActionTypes.UPLOAD_BANNER_IMAGE,
    });

    try {
      // Map the imageType to the expected API parameter value
      const apiImageType = imageType === 'desktop' ? 'bannerDesktop' : 'bannerMobile';
      console.log(`Uploading ${imageType} banner image, bannerId:`, bannerId, `imageType: ${apiImageType}`);
      const response = await BannerSliderService.uploadFile(file, bannerId, apiImageType);
      console.log('Upload response:', response);

      dispatch({
        type: BannerSliderActionTypes.UPLOAD_BANNER_IMAGE_SUCCESS,
        payload: response.data,
      });

      return response.data.fileName;
    } catch (error) {
      console.error('Error uploading banner image:', error);
      dispatch({
        type: BannerSliderActionTypes.UPLOAD_BANNER_IMAGE_ERROR,
        payload: error?.response?.data,
      });
      throw error;
    }
  };
};



export const changeFilter = (filter: Filter) => {
  return async (dispatch: any) => {
    dispatch({
      type: BannerSliderActionTypes.CHANGE_PAGINATION,
      payload: {
        page: filter.page,
        pageSize: filter.pageSize,
        sort: filter.sort ? { ...filter.sort } : undefined,
      },
    });
  };
};

export const getBannerSliders = (filter: Filter = {}) => {
  return async (dispatch: any) => {
    dispatch({
      type: BannerSliderActionTypes.GET_BANNER_SLIDERS,
    });

    try {
      const response = await BannerSliderService.getBannerSliders({
        page: filter.page || 1,
        size: filter.pageSize || 10,
        filters: {
          status: 'ACTIVE',
        },
      });

      dispatch({
        type: BannerSliderActionTypes.GET_BANNER_SLIDERS_SUCCESS,
        payload: response,
      });
    } catch (error) {
      const errorData = error instanceof Error && 'response' in error ? (error as any).response?.data : error;
      dispatch({
        type: BannerSliderActionTypes.GET_BANNER_SLIDERS_ERROR,
        payload: errorData,
      });
    }
  };
};

export const createBannerSlider = (bannerSlider: any, imageChanges: Record<string, BannerImageChanges>) => {
  return async (dispatch: any) => {
    dispatch({
      type: BannerSliderActionTypes.CREATE_BANNER_SLIDER,
    });

    // No need to track uploaded images for cleanup as backend will handle it

    try {
      console.log('createBannerSlider received data:', bannerSlider);
      console.log('imageChanges:', imageChanges);

      // Upload all new images first
      const updatedBanners = await Promise.all(
        bannerSlider.banners.map(async (banner: any, index: number) => {
          const changes = imageChanges[index];
          const updatedBanner = { ...banner };

          console.log(`Processing banner ${index}:`, banner);
          console.log(`Image changes for banner ${index}:`, changes);

          // For new banners, make sure we're handling desktop banner correctly
          if (changes?.desktopBanner?.file) {
            console.log(`Uploading new desktop banner for banner ${index}`);
            const fileName = await dispatch(uploadBannerImage(changes.desktopBanner.file, undefined, 'desktop'));
            updatedBanner.desktopBanner = fileName;
            console.log(`New desktop banner filename: ${fileName}`);
          } else if (banner.desktopBanner) {
            // Ensure we keep the existing URL if it exists
            console.log(`Keeping existing desktop banner URL for banner ${index}: ${banner.desktopBanner}`);
            updatedBanner.desktopBanner = banner.desktopBanner;
          } else {
            console.log(`No desktop banner for banner ${index}`);
          }

          // For new banners, make sure we're handling mobile banner correctly
          if (changes?.mobileBanner?.file) {
            console.log(`Uploading new mobile banner for banner ${index}`);
            const fileName = await dispatch(uploadBannerImage(changes.mobileBanner.file, undefined, 'mobile'));
            updatedBanner.mobileBanner = fileName;
            console.log(`New mobile banner filename: ${fileName}`);
          } else if (banner.mobileBanner) {
            // Ensure we keep the existing URL if it exists
            console.log(`Keeping existing mobile banner URL for banner ${index}: ${banner.mobileBanner}`);
            updatedBanner.mobileBanner = banner.mobileBanner;
          } else {
            console.log(`No mobile banner for banner ${index}`);
          }

          console.log(`Final banner ${index} after processing:`, updatedBanner);
          return updatedBanner;
        })
      );

      const finalPayload = {
        ...bannerSlider,
        banners: updatedBanners,
      };

      console.log('Final payload to be sent to API:', finalPayload);

      const response = await BannerSliderService.createBannerSlider(finalPayload);

      console.log('API response:', response);

      dispatch({
        type: BannerSliderActionTypes.CREATE_BANNER_SLIDER_SUCCESS,
        payload: response,
      });

      return response;
    } catch (error) {
      console.error('Error creating banner slider:', error);
      // No need to cleanup uploaded images as backend will handle it
      dispatch({
        type: BannerSliderActionTypes.CREATE_BANNER_SLIDER_ERROR,
        payload: error?.response?.data,
      });
      throw error;
    }
  };
};

export const updateBannerSlider = (bannerSlider: any, imageChanges: Record<string, BannerImageChanges>) => {
  return async (dispatch: any) => {
    dispatch({
      type: BannerSliderActionTypes.UPDATE_BANNER_SLIDER,
    });

    try {
      console.log('updateBannerSlider received data:', bannerSlider);
      console.log('imageChanges:', imageChanges);

      // Upload all new images first
      const updatedBanners = await Promise.all(
        bannerSlider.banners.map(async (banner: any, index: number) => {
          const changes = imageChanges[index];
          const updatedBanner = { ...banner };

          console.log(`Processing banner ${index}:`, banner);
          console.log(`Image changes for banner ${index}:`, changes);

          // Make sure we're sending the existing desktop banner URL if no new image is uploaded
          if (changes?.desktopBanner?.file) {
            console.log(`Uploading new desktop banner for banner ${index}`);
            const fileName = await dispatch(uploadBannerImage(changes.desktopBanner.file, banner.id, 'desktop'));
            updatedBanner.desktopBanner = fileName;
            console.log(`New desktop banner filename: ${fileName}`);
          } else if (banner.desktopBanner) {
            // Ensure we keep the existing URL
            console.log(`Keeping existing desktop banner URL for banner ${index}: ${banner.desktopBanner}`);
            updatedBanner.desktopBanner = banner.desktopBanner;
          } else {
            console.log(`No desktop banner for banner ${index}`);
          }

          // Make sure we're sending the existing mobile banner URL if no new image is uploaded
          if (changes?.mobileBanner?.file) {
            console.log(`Uploading new mobile banner for banner ${index}`);
            const fileName = await dispatch(uploadBannerImage(changes.mobileBanner.file, banner.id, 'mobile'));
            updatedBanner.mobileBanner = fileName;
            console.log(`New mobile banner filename: ${fileName}`);
          } else if (banner.mobileBanner) {
            // Ensure we keep the existing URL
            console.log(`Keeping existing mobile banner URL for banner ${index}: ${banner.mobileBanner}`);
            updatedBanner.mobileBanner = banner.mobileBanner;
          } else {
            console.log(`No mobile banner for banner ${index}`);
          }

          console.log(`Final banner ${index} after processing:`, updatedBanner);
          return updatedBanner;
        })
      );

      const finalPayload = {
        ...bannerSlider,
        banners: updatedBanners,
      };

      console.log('Final payload to be sent to API:', finalPayload);

      const response = await BannerSliderService.updateBannerSlider(finalPayload);

      console.log('API response:', response);

      // No need to delete old images as backend will handle it

      dispatch({
        type: BannerSliderActionTypes.UPDATE_BANNER_SLIDER_SUCCESS,
        payload: response,
      });

      return response;
    } catch (error) {
      console.error('Error updating banner slider:', error);
      dispatch({
        type: BannerSliderActionTypes.UPDATE_BANNER_SLIDER_ERROR,
        payload: error?.response?.data,
      });
      throw error;
    }
  };
};
