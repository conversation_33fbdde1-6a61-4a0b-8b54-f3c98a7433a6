import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import {
  <PERSON>ton,
  TextField,
  Grid
} from '@material-ui/core';
import { useForm } from 'react-hook-form';
import { InputError, BoxWrap, PageTitle } from 'components';
import { NavLink } from 'react-router-dom';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { useTranslation } from 'react-i18next';
import * as globalSettingActions from '../GlobalSettingActions';

import { useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';

export const AdminForm = (props) => {
  const { t }: { t: any } = useTranslation();

  let history = useHistory();
  const dispatch = useDispatch();
  const globalSettingForm = useSelector((state: any) => state.globalSetting);
  const { addMessage } = useAPIError();

  const { register, handleSubmit, errors } = useForm({
    criteriaMode: 'all',
    mode: 'onBlur',
    reValidateMode: 'onBlur',
  });

  const defaultValues = {
    brandBannerBackgroundColor: props.brandBannerBackgroundColor || '#000000',
    brandBannerTextColor: props.brandBannerTextColor || '#ffffff',
  }

  const [inputVals, setInputVals]: any = useState(defaultValues);

  const inputChange = (field, event) => {
    setInputVals({ ...inputVals, [field]: event.target.value });
  };


  useEffect(() => {
  if (globalSettingForm.error) {
      addMessage(
        t(globalSettingForm.error.errorCode || 'unexpectedError'),
        'error'
      );
    } else if (
      globalSettingForm.success ||
      globalSettingForm.createSuccess
    ) {
      addMessage(t('successGlobalSettingUpdated'), 'success');
      history.push('/dashboard/global-setting');
    }
  }, [globalSettingForm, addMessage]);


  const onSubmit = useCallback(
    (submitData) => {
      dispatch(globalSettingActions.saveGlobalSetting(submitData));
    },
    [dispatch]
  );


  return (
    <>
      <PageTitle title={t('brandBanner')} />
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={4}>
          <Grid item xl={12} lg={12} sm={12} xs={12}>
            <BoxWrap>
              <Grid container spacing={4}>
                <Grid item xl={9} lg={9} sm={12} xs={12}>
                  <TextField
                    label={t('brandBannerBackgroundColor')}
                    fullWidth={true}
                    inputProps={{
                      name: 'brandBannerBackgroundColor',
                    }}
                    value={inputVals.brandBannerBackgroundColor}
                    inputRef={register({
                      required: t('brandBannerBackgroundColorRequired'),
                      maxLength: {
                        value: 7,
                        message: t('hexColorEqualTo7'),
                      },
                      minLength: {
                        value: 7,
                        message: t('hexColorEqualTo7'),
                      }
                    })}
                    onChange={(event) => {
                      inputChange('brandBannerBackgroundColor', event);
                    }}
                    helperText={
                      errors.brandBannerBackgroundColor && <InputError message={errors.brandBannerBackgroundColor.message} />
                    }
                  />
                </Grid>

                <Grid item xl={9} lg={9} sm={12} xs={12}>
                  <TextField
                    label={t('brandBannerTextColor')}
                    fullWidth={true}
                    inputProps={{
                      name: 'brandBannerTextColor',
                    }}
                    value={inputVals.brandBannerTextColor}
                    inputRef={register({
                      required: t('brandBannerTextColorRequired'),
                      maxLength: {
                        value: 7,
                        message: t('hexColorEqualTo7'),
                      },
                      minLength: {
                        value: 7,
                        message: t('hexColorEqualTo7'),
                      }
                    })}
                    onChange={(event) => {
                      inputChange('brandBannerTextColor', event);
                    }}
                    helperText={
                      errors.brandBannerTextColor && <InputError message={errors.brandBannerTextColor.message} />
                    }
                  />
                </Grid>
              </Grid>
            </BoxWrap>
          </Grid>
        </Grid>

        <Button
          className="submit-button"
          type="submit"
          variant="contained"
          color="primary"
        >
          {t('save')}
        </Button>
        <Button
          component={NavLink}
          to="/dashboard/global-setting/"
          className={'btn btn-cancel'}
          color="primary"
        >
          {t('cancel')}
        </Button>
      </form>
    </>
  );
};
