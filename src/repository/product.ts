import API from 'services/api';

export const getProducts = (props) => {
  const {
    pageSize = 10,
    page = 0,
    sort = { field: 'position', dir: 'ASC' },
    search = '',
  } = props;
  return API.post('/product-packages/list', {
    page,
    pageSize,
    sort: sort.field ? [{ field: sort.field, dir: sort.dir }] : [],
    search,
  });
};

export const uploadFile = async (file) => {
  return await API.post('/media/upload?entityType=Product', file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const deleteFile = async (file) => {
  // expected error messages from BE
  // - productPackageNotFound
  return await API.delete(`/media/delete?s3FileName=${file}`, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const deleteProduct = async (id) => {
  // expected error messages from BE
  // - productPackageNotFound
  // - productPackageAlreadyDeactivated
  return await API.delete(`/product-packages/${id}`);
};

export const getProduct = async (id) => {
  // expected error messages from BE
  // - productPackageNotFound
  return await API.get(`/product-packages/${id}`);
};

export const setProductFeatured = async (data) => {
  // expected error messages from BE
  // - productPackageNotFound
  // - productPackageNotActive
  // - positionAlreadyTaken
  return await API.patch(`/product-packages/position`, {
    ...data,
  });
};

export const getFreePositions = async () => {
  return await API.get(`/product-packages/positions`);
};

export const createProduct = (props) => {
  // TODO handle invalidRequestBody having message in "values"
  // - positionAlreadyTaken
  // - invalidRecurringAmount
  return API.post('/product-packages', {
    ...props,
  });
};

export const editProduct = (id, data) => {
  // TODO handle invalidRequestBody having message in "values"
  // - translate productPackageNotFound
  // - translate cannotChangePaymentDetails
  return API.put(`/product-packages`, {
    id,
    ...data,
  });
};

export const activateProduct = async (id) => {
  // expected error messages from BE
  // - productPackageNotFound
  // - productPackageAlreadyActive
  return await API.post(`/product-packages/activate/${id}`);
};

export const getProductSubscriptions = async (id) => {
  // expected error messages from BE
  // - productPackageNotFound
  return await API.get(`/product-packages/subscribers/${id}`);
};

export const uploadCodesFromCSV = async (file, productPackageId) =>
  // expected error messages from BE
  // - productPackageNotFound
  // - codeCannotBeBlank
  // - subscriptionCodeLess45
  // - numberOfUsagesForCodeCannotBeEmpty
  // - numberOfUsagesForCodeCannotBeText
  // - numberOfUsagesForCodeCannotBeLessThanOne
  // - codeAlreadyExistsInList
  // - codeAlreadyExists

  await API.post(
    `/subscription-discount-codes/upload?productPackageId=${productPackageId}`,
    file
  );

export const uploadCode = (productPackageId, code, numberOfUsages) =>
  API.post('/product-packages/upload-code', {
    code,
    numberOfUsages,
    productPackageId,
  });
// expected error messages from BE
// - productPackageNotFound
// - codeUploadDisabledForDeactivatedProductPackages
// - codeAlreadyExists (with code)

export const getProductSubscriptionCodes = (id, props) => {
  const {
    pageSize = 10,
    page = 0,
    sort = { field: 'id', dir: 'DESC' },
    search = '',
  } = props;
  return API.post(`product-packages/codes/${id}`, {
    page,
    pageSize,
    sort: sort.field ? [{ field: sort.field, dir: sort.dir }] : [],
    search,
  });
};

export const getEligibleProducts = (id) => {
  return API.get(`/product-packages/eligible/${id}`);
};
