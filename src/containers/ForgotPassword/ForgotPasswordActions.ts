import { RESET_START, RESET_SUCCESS, RESET_ERROR } from './ForgotPasswordTypes';
import * as AuthRepository from 'repository/auth';

export const forgotPassword = (email) => {
  return async (dispatch) => {
    dispatch({
      type: RESET_START,
    });

    try {
      await AuthRepository.forgotPassword(email);
      dispatch({
        type: RESET_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: RESET_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearErrors = () => ({
  type: RESET_ERROR,
  payload: null,
});
