import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BrandUser } from 'repository/brand-account';

export interface Props {
  data: BrandUser[];
}

const roleTranslationsMap = {
  BRAND_ADMIN: 'brandAdmin',
  ADMIN: 'admin',
  CONTENT_MANAGER: 'contentManager',
};

const BrandUserRow = ({ user }: { user: BrandUser }) => {
  const { t }: { t: any } = useTranslation();
  const { firstName, surname, username, role } = user;
  return (
    <TableRow>
      <TableCell scope="row">{`${firstName} ${surname}`}</TableCell>
      <TableCell scope="row">{`${username}`}</TableCell>
      <TableCell scope="row">{t(roleTranslationsMap[role])}</TableCell>
    </TableRow>
  );
};

export const BrandAccountBrandUsersTable = ({ data }: Props) => {
  const { t } = useTranslation();

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t('name')}</TableCell>
            <TableCell>{t('email')}</TableCell>
            <TableCell>{t('role')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((user, index) => (
            <BrandUserRow user={user} key={index} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
