import useAPIError from 'components/APIErrorNotification/useAPIError';
import { InfoDialog, ReachBoostRequestDialog } from 'components';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { BrandAccount } from 'repository/brand-account';
import {
  getBrandAccountData,
  sendReachBoostUpgradeRequest,
  sendUpgradeRequest,
} from './BrandAccountActions';
import { BrandAccountView } from './BrandAccountView';
import { reachBoostStatus } from 'utils/constants';

export const getRequestedReachBoostData = (reachBoostList) =>
  reachBoostList?.find(
    ({ status }) =>
      status === reachBoostStatus.PENDING ||
      status === reachBoostStatus.WAITING_FOR_APPROVAL
  );

export const getCurrentReachBoostData = (reachBoostList) =>
  reachBoostList?.find(
    ({ status }) =>
      status === reachBoostStatus.ACTIVE || status === reachBoostStatus.DISABLED
  );

export const getReachBoostStatus = ({ active, requested }, translateFn) => {
  if (active) {
    return translateFn('active').toLowerCase();
  } else if (requested) {
    if (requested.status == 'PENDING') {
      return translateFn('pending').toLowerCase();
    }
    return `${translateFn('inactive')} (${translateFn(
      'changesRequested'
    )})`.toLowerCase();
  } else return translateFn('inactive').toLowerCase();
};

export default (props) => {
  const { t } = useTranslation();
  const { addMessage } = useAPIError();
  const dispatch = useDispatch();
  const brandAccountData: BrandAccount = useSelector(
    (state) => state.brandAccount?.data
  );
  const shouldShowRequestSuccess = useSelector(
    (state) => state.brandAccount?.didRequestSucceed
  );
  const brandAccountError = useSelector((state) => state.brandAccount?.error);
  const reachBoostRequestSuccess = useSelector(
    (state) => state.brandAccount?.didReachBoostRequestSucceed
  );

  let reachBoost = {
    active: false,
    requested: undefined,
    current: undefined,
  };

  if (brandAccountData) {
    const { reachBoosts, reachBoost: active } = brandAccountData;
    const requested = getRequestedReachBoostData(reachBoosts);
    const current = getCurrentReachBoostData(reachBoosts);

    reachBoost = {
      active,
      requested,
      current,
    };
  }
  useEffect(() => {
    dispatch(getBrandAccountData());
  }, [dispatch]);

  const [requestedCouponCount, setRequestCouponCount] = useState(0);
  const [requestedCouponUpdatesCount, setRequestCouponUpdatesCount] =
    useState(0);
  const [isImageVideoRequested, setImageVideoRequested] = useState(false);
  const [isUnboxingVideoRequested, setIsUnboxingVideoRequested] =
    useState(false);
  const [infoHtmlText, setInfoHtmlText] = useState<string | undefined>();
  const [isReachBoostDialogOpen, setIsReachBoostDialogOpen] = useState(false);

  const onCouponCountChange = useCallback(
    (count: number) => setRequestCouponCount(count),
    [setRequestCouponCount]
  );

  const onCouponUpdatesCountChange = useCallback(
    (count: number) => setRequestCouponUpdatesCount(count),
    [setRequestCouponUpdatesCount]
  );

  const onImageVideoRequest = useCallback(
    (isRequested: boolean) => setImageVideoRequested(isRequested),
    [setImageVideoRequested]
  );

  const onUnboxingVideoRequest = useCallback(
    (isRequested: boolean) => setIsUnboxingVideoRequested(isRequested),
    [setIsUnboxingVideoRequested]
  );

  useEffect(() => {
    if (shouldShowRequestSuccess) {
      setRequestCouponCount(0);
      setRequestCouponUpdatesCount(0);
      setImageVideoRequested(false);
      setIsUnboxingVideoRequested(false);
      addMessage(t('brandAccountRequestSuccess'), 'success');
    } else if (reachBoostRequestSuccess) {
      addMessage(t('successReachBoostRequest'), 'success');
    } else if (brandAccountError) {
      addMessage(
        t(brandAccountError.errorCode || 'brandAccountRequestError'),
        'error'
      );
    }
  }, [
    dispatch,
    shouldShowRequestSuccess,
    reachBoostRequestSuccess,
    brandAccountError,
    setRequestCouponUpdatesCount,
    setRequestCouponCount,
    setImageVideoRequested,
  ]);

  const onRequest = useCallback(() => {
    dispatch(
      sendUpgradeRequest({
        coupons: requestedCouponCount,
        couponUpdates: requestedCouponUpdatesCount,
        freeImageVideo: isImageVideoRequested,
        freeUnboxingVideo: isUnboxingVideoRequested,
      })
    );
  }, [
    requestedCouponCount,
    requestedCouponUpdatesCount,
    isImageVideoRequested,
    isUnboxingVideoRequested,
    dispatch,
  ]);

  const sendReachBoostRequest = (reachBoostRequestData) => {
    dispatch(sendReachBoostUpgradeRequest(reachBoostRequestData));
    setIsReachBoostDialogOpen(false);
  };

  const onInfoClick = useCallback(
    (translationKey) => setInfoHtmlText(t(translationKey)),
    [t, setInfoHtmlText]
  );

  const onInfoClose = useCallback(
    () => setInfoHtmlText(undefined),
    [setInfoHtmlText]
  );

  if (!brandAccountData) {
    return <div>{t('loading')}</div>;
  }

  return (
    <>
      <BrandAccountView
        data={brandAccountData}
        reachBoostStatus={getReachBoostStatus(reachBoost, t)}
        requestedCouponCount={requestedCouponCount}
        onCouponCountChange={onCouponCountChange}
        requestedCouponUpdatesCount={requestedCouponUpdatesCount}
        onCouponUpdatesCountChange={onCouponUpdatesCountChange}
        isImageVideoRequested={isImageVideoRequested}
        onImageVideoRequest={onImageVideoRequest}
        isUnboxingVideoRequested={isUnboxingVideoRequested}
        onUnboxingVideoRequest={onUnboxingVideoRequest}
        openReachBoostDialog={() => setIsReachBoostDialogOpen(true)}
        onRequest={onRequest}
        onInfoClick={onInfoClick}
      />

      <InfoDialog
        open={!!infoHtmlText}
        textHtml={infoHtmlText}
        onCloseClick={onInfoClose}
      />

      <ReachBoostRequestDialog
        data={reachBoost}
        onCloseClick={() => setIsReachBoostDialogOpen(false)}
        onInfoClick={() => onInfoClick('reachBoostInfoHtml')}
        onOKClick={sendReachBoostRequest}
        isOpen={isReachBoostDialogOpen}
      />
    </>
  );
};
