import React, { useCallback } from 'react';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import { ErrorMessage } from '@hookform/error-message';
import {
  FormLabel,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Button,
} from '@material-ui/core';

import { BoxWrap, InputError } from 'components';
import { FormTextField, FormCheckBox } from 'components/FormInputs';

import './BrandPricingDetails.scss';

interface BrandPricingDetailsProps {
  brandPagePackageTemplates: Array<any> | null;
  reachBoostStatus: string;
  readOnly: boolean;
  control: any;
  setValue: any;
  clearErrors: any;
  register: any;
  errors: any;
  watch: any;
  openReachBoostDialog(): void;
}

const isIdValid = (id) => id !== 'create';

const BrandPricingDetails = ({
  brandPagePackageTemplates = [],
  reachBoostStatus = '',
  readOnly = true,
  control,
  setValue,
  clearErrors,
  errors,
  watch,
  openReachBoostDialog,
}: BrandPricingDetailsProps) => {
  const { t }: { t: any } = useTranslation();
  const { id } = useParams();

  const brandPackageTemplateIdWatch = watch('brandPackageTemplateId');

  const setValuesFromTemplate = useCallback(
    (packageTemplateId) => {
      const template = brandPagePackageTemplates?.find(
        (packageTemplate) => packageTemplate.id === packageTemplateId
      );
      const inputFields = [
        'coupons',
        'couponUpdates',
        'freeImageVideo',
        'freeUnboxingVideo',
        'pricePerCoupon',
        'pricePerCouponUpdate',
        'pricePerImageVideo',
        'pricePerUnboxingVideo',
      ];
      clearErrors(inputFields);
      inputFields.forEach((field) => setValue(field, template[field]));
    },
    [brandPagePackageTemplates, setValue, clearErrors]
  );

  return (
    <>
      <div className="brand-pricing-title">
        <FormLabel>{t('brandDetails')}</FormLabel>
      </div>
      <BoxWrap>
        <Grid container spacing={5}>
          <Grid item sm={6} xs={12}>
            <Grid item sm={12} xs={12}>
              <FormControl fullWidth>
                <InputLabel htmlFor="brandPackageTemplateId">
                  {t('bookedPackage')}
                </InputLabel>
                <Controller
                  name="brandPackageTemplateId"
                  control={control}
                  rules={{ required: false }}
                  render={({ value, onChange }) => (
                    <Select
                      value={value}
                      onChange={(event) => {
                        onChange(event);
                        setValuesFromTemplate(event.target.value);
                      }}
                      fullWidth={true}
                      disabled={readOnly}
                    >
                      {brandPagePackageTemplates !== null &&
                        brandPagePackageTemplates.map((packageTemplate) => (
                          <MenuItem
                            key={packageTemplate.id}
                            value={packageTemplate.id}
                          >
                            {packageTemplate.name}
                          </MenuItem>
                        ))}
                    </Select>
                  )}
                />
                <ErrorMessage
                  errors={errors}
                  name="roleId"
                  render={({ message }) => (
                    <InputError message={t('roleRequired')} />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormTextField
                control={control}
                name="coupons"
                label={t('amountCoupons')}
                type="number"
                rules={{
                  required:
                    brandPackageTemplateIdWatch !== -1 &&
                    t('brandPricingFieldRequired'),
                  min: {
                    value: 0,
                    message: t('brandPricingFieldMustNotBeNegative'),
                  },
                }}
                disabled={readOnly}
                errors={errors}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormTextField
                control={control}
                name="couponUpdates"
                label={t('amountRefreshes')}
                type="number"
                rules={{
                  required:
                    brandPackageTemplateIdWatch !== -1 &&
                    t('brandPricingFieldRequired'),
                  min: {
                    value: 0,
                    message: t('brandPricingFieldMustNotBeNegative'),
                  },
                }}
                disabled={readOnly}
                errors={errors}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormCheckBox
                control={control}
                name="freeImageVideo"
                label={t('imageVideo')}
                disabled={readOnly}
                style={{ marginLeft: '-12px' }}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormCheckBox
                control={control}
                name="freeUnboxingVideo"
                label={t('unboxingVideo')}
                disabled={readOnly}
                style={{ marginLeft: '-12px' }}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormCheckBox
                control={control}
                name="deactivateBrandPageRecommendation"
                label={t('deactivateBrandPageRecommendation')}
                disabled={readOnly}
                style={{ marginLeft: '-12px' }}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormCheckBox
                control={control}
                name="isCountDownEnabled"
                label={t('enableCouponCountdown')}
                disabled={readOnly}
                style={{ marginLeft: '-12px' }}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormCheckBox
                control={control}
                name="enableSoftRenewal"
                label={t('enableVirtualExpiration')}
                disabled={readOnly}
                style={{ marginLeft: '-12px' }}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormCheckBox
                control={control}
                name="editMetaFieldsAndTitle"
                label={t('editMetaFields')}
                disabled={readOnly}
                style={{ marginLeft: '-12px' }}
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormCheckBox
                control={control}
                name="disableNewsletter"
                label={t('disableNewsletter')}
                disabled={readOnly}
                style={{ marginLeft: '-12px' }}
              />
            </Grid>
            <Grid container sm={12} xs={12}>
              <Grid item sm={6} xs={6}>
                <p className="reach-boost-title">
                  Reach boost: {reachBoostStatus}
                </p>
              </Grid>
              {isIdValid(id) ? (
                <Grid item sm={6} xs={6}>
                  <Button
                    color="primary"
                    variant="contained"
                    className="request-reach-boost-btn float-right"
                    onClick={openReachBoostDialog}
                    disabled={false}
                  >
                    {t('editReachBoost')}
                  </Button>
                </Grid>
              ) : null}
            </Grid>
          </Grid>

          <Grid item sm={6} xs={12} style={{ backgroundColor: '#ecf7ff' }}>
            <Grid item sm={12} xs={12}>
              <FormLabel>{t('priceListForThisBrand')}</FormLabel>
            </Grid>{' '}
            <Grid item sm={12} xs={12}>
              <FormTextField
                control={control}
                name="pricePerCoupon"
                label={t('pricePerCoupon')}
                type="number"
                step={0.01}
                rules={{
                  required:
                    brandPackageTemplateIdWatch !== -1 &&
                    t('brandPricingFieldRequired'),
                  min: {
                    value: 0,
                    message: t('brandPricingFieldMustNotBeNegative'),
                  },
                }}
                disabled={readOnly}
                errors={errors}
                className="brand-prices"
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormTextField
                control={control}
                name="pricePerCouponUpdate"
                label={t('pricePerRefresh')}
                type="number"
                step={0.01}
                rules={{
                  required:
                    brandPackageTemplateIdWatch !== -1 &&
                    t('brandPricingFieldRequired'),
                  min: {
                    value: 0,
                    message: t('brandPricingFieldMustNotBeNegative'),
                  },
                }}
                disabled={readOnly}
                errors={errors}
                className="brand-prices"
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormTextField
                control={control}
                name="pricePerImageVideo"
                label={t('pricePerImageVideo')}
                type="number"
                step={0.01}
                rules={{
                  required:
                    brandPackageTemplateIdWatch !== -1 &&
                    t('brandPricingFieldRequired'),
                  min: {
                    value: 0,
                    message: t('brandPricingFieldMustNotBeNegative'),
                  },
                }}
                disabled={readOnly}
                errors={errors}
                className="brand-prices"
              />
            </Grid>
            <Grid item sm={12} xs={12}>
              <FormTextField
                control={control}
                name="pricePerUnboxingVideo"
                label={t('pricePerUnboxingVideo')}
                type="number"
                step={0.01}
                rules={{
                  required:
                    brandPackageTemplateIdWatch !== -1 &&
                    t('brandPricingFieldRequired'),
                  min: {
                    value: 0,
                    message: t('brandPricingFieldMustNotBeNegative'),
                  },
                }}
                disabled={readOnly}
                errors={errors}
                className="brand-prices"
              />
            </Grid>
          </Grid>
        </Grid>
      </BoxWrap>
    </>
  );
};

export default BrandPricingDetails;
