import * as TestedForYouTypes from './TestedForYouTypes';
import * as TestedForYouAPI from 'repository/testedForYou';

// Interface to track image changes
export interface ImageChange {
    file: File;
    oldPath?: string;
}

export interface TestedForYouImageChanges {
    logo?: ImageChange;
    image?: ImageChange;
}

export const uploadTestedForYouImage = (file: File, testedForYouId?: number, imageType: string = 'testedForYouLogo') => {
    return async (dispatch: any) => {
        dispatch({
            type: TestedForYouTypes.UPLOAD_TESTED_FOR_YOU_IMAGE,
        });

        try {
            const response = await TestedForYouAPI.uploadFile(file, testedForYouId, imageType);

            dispatch({
                type: TestedForYouTypes.UPLOAD_TESTED_FOR_YOU_IMAGE_SUCCESS,
                payload: response.data,
            });

            return response.data.fileName;
        } catch (error) {
            dispatch({
                type: TestedForYouTypes.UPLOAD_TESTED_FOR_YOU_IMAGE_ERROR,
                payload: error,
            });
            throw error;
        }
    };
};

export const getTestedForYous = (request: TestedForYouAPI.TestedForYouListRequest) => async (dispatch) => {
    dispatch({ type: TestedForYouTypes.GET_TESTED_FOR_YOUS });
    try {
        const response = await TestedForYouAPI.getTestedForYous(request);
        dispatch({ type: TestedForYouTypes.GET_TESTED_FOR_YOUS_SUCCESS, payload: response.data });
    } catch (error) {
        dispatch({ type: TestedForYouTypes.GET_TESTED_FOR_YOUS_ERROR, payload: error });
    }
};

export const getTestedForYouById = (testedForYouId: number) => async (dispatch) => {
    dispatch({ type: TestedForYouTypes.GET_TESTED_FOR_YOU });
    try {
        const response = await TestedForYouAPI.getTestedForYouById(testedForYouId);
        dispatch({ type: TestedForYouTypes.GET_TESTED_FOR_YOU_SUCCESS, payload: response });
    } catch (error) {
        dispatch({ type: TestedForYouTypes.GET_TESTED_FOR_YOU_ERROR, payload: error });
    }
};

export const createTestedForYou = (data: TestedForYouAPI.TestedForYou) => async (dispatch) => {
    dispatch({ type: TestedForYouTypes.CREATE_TESTED_FOR_YOU });
    try {
        const response = await TestedForYouAPI.createTestedForYou(data);
        dispatch({ type: TestedForYouTypes.CREATE_TESTED_FOR_YOU_SUCCESS, payload: response });
    } catch (error) {
        dispatch({ type: TestedForYouTypes.CREATE_TESTED_FOR_YOU_ERROR, payload: error });
    }
};

export const updateTestedForYou = (data: TestedForYouAPI.TestedForYou) => async (dispatch) => {
    dispatch({ type: TestedForYouTypes.UPDATE_TESTED_FOR_YOU });
    try {
        const response = await TestedForYouAPI.updateTestedForYou(data);
        dispatch({ type: TestedForYouTypes.UPDATE_TESTED_FOR_YOU_SUCCESS, payload: response });
    } catch (error) {
        dispatch({ type: TestedForYouTypes.UPDATE_TESTED_FOR_YOU_ERROR, payload: error });
    }
};

export const deleteTestedForYou = (testedForYouId: number) => async (dispatch) => {
    dispatch({ type: TestedForYouTypes.DELETE_TESTED_FOR_YOU });
    try {
        await TestedForYouAPI.deleteTestedForYou(testedForYouId);
        dispatch({ type: TestedForYouTypes.DELETE_TESTED_FOR_YOU_SUCCESS, payload: testedForYouId });
    } catch (error) {
        dispatch({ type: TestedForYouTypes.DELETE_TESTED_FOR_YOU_ERROR, payload: error });
    }
};
