import React from "react";

function Discount({
  value,
  className = "",
  type = "PERCENTAGE",
  hasLongWords,
}) {
  return type === "FREE" ? (
    <div
      className={`CardHorizontal__discount-free ${
        hasLongWords ? "CardHorizontal__discount-free--long-words" : ""
      } ${className}`}
    >
      {value || ""}
    </div>
  ) : (
    <div className={`CardHorizontal__discount ${className}`}>
      {value
        ? type === "PERCENTAGE"
          ? value + "%"
          : (value % 1 != 0
              ? value.toLocaleString("de-DE", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
              : value) + "€"
        : null}
    </div>
  );
}
export default Discount;
