import React from 'react';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableFooter from '@material-ui/core/TableFooter';
import TablePagination from '@material-ui/core/TablePagination';
import TableRow from '@material-ui/core/TableRow';
import TableHead from '@material-ui/core/TableHead';

import IconButton from '@material-ui/core/IconButton';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import { Sorter } from 'components/Sorter/Sorter';
import { Link } from 'react-router-dom';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  DialogActions,
} from '@material-ui/core';
import { useTranslation } from 'react-i18next';

export const BrandUsersTable = ({
  users,
  pagination,
  handleDelete,
  filter,
  onFilterChange,
  children,
}) => {
  const [page, setPage] = React.useState(filter.page);
  const [selectedUser, setSelectedUser] = React.useState();
  const [rowsPerPage, setRowsPerPage] = React.useState(filter.pageSize);
  const [open, setOpen] = React.useState(false);
  const { t }: { t: any } = useTranslation();

  const roleNames = ['', t('admin'), t('contentManager'), '', t('brandAdmin')];

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);

    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };

  const handleClickOpen = (id) => {
    setSelectedUser(id);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  if (!users.length) return <h2>{t('noResultsFound')}</h2>;
  return (
    <TableContainer>
      {children}
      <Table aria-label="custom pagination table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter
                label={t('id')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'id', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('name')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'firstName', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('email')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'username', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('brandPage')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'brandName', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('status')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'status', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>{t('role')}</TableCell>
            <TableCell>{t('stripeCustomerId')}</TableCell>
            <TableCell>{t('restricted')}</TableCell>
            <TableCell>{t('actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell scope="user">{user.id}</TableCell>
              <TableCell scope="user">
                {user.firstName + ' ' + user.surname}
              </TableCell>
              <TableCell scope="user">{user.username}</TableCell>
              <TableCell scope="user">{user.brandName}</TableCell>
              <TableCell scope="user">{user.status}</TableCell>
              <TableCell component="th" scope="user">
                {roleNames[user.roleId]}
              </TableCell>
              <TableCell scope="user">{user.stripeCustomerId}</TableCell>
              <TableCell scope="user">{user.restrictSeoFields ? t('yes') : t('no')}</TableCell>
              <TableCell component="th" scope="user">
                <Link to={`/dashboard/brand-user/${user.id}`}>
                  <IconButton aria-label="edit" color="default">
                    <EditIcon />
                  </IconButton>
                </Link>
                <IconButton
                  aria-label="delete"
                  color="secondary"
                  onClick={() => handleClickOpen(user.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={pagination?.last || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>

      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteUser')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('warningNoUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              handleDelete(selectedUser);
              handleClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>
    </TableContainer>
  );
};
