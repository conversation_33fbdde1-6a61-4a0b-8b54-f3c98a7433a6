import * as BannerSliderActionTypes from './BannerSliderTypes';

const INITIAL_STATE = {
  loading: false,
  error: null,
  bannerSliders: [],
  pagination: {
    current: 1,
    first: 1,
    last: 1,
  },
};

export default (state: any = INITIAL_STATE, action: any) => {
  switch (action.type) {
    case BannerSliderActionTypes.CHANGE_PAGINATION:
      return { ...state, filter: action.payload };
    case BannerSliderActionTypes.HOMEPAGE_START:
      return { ...state, loading: true, error: null };
    case BannerSliderActionTypes.HOMEPAGE_SUCCESS:
      return { ...state, loading: false };
    case BannerSliderActionTypes.HOMEPAGE_ERROR:
      return { ...state, loading: false, error: action.payload };
    case BannerSliderActionTypes.GET_BANNER_SLIDERS:
      return { ...state, loading: true };
    case BannerSliderActionTypes.GET_BANNER_SLIDERS_SUCCESS:
      return {
        ...state,
        loading: false,
        bannerSliders: action.payload.data,
        pagination: {
          current: action.payload.number + 1,
          first: 1,
          last: action.payload.total,
        },
      };
    case BannerSliderActionTypes.GET_BANNER_SLIDERS_ERROR:
      return { ...state, loading: false, error: action.payload };
    case BannerSliderActionTypes.CREATE_BANNER_SLIDER:
      return { ...state, loading: true };
    case BannerSliderActionTypes.CREATE_BANNER_SLIDER_SUCCESS:
      return {
        ...state,
        loading: false,
        bannerSliders: [...state.bannerSliders, action.payload],
      };
    case BannerSliderActionTypes.CREATE_BANNER_SLIDER_ERROR:
      return { ...state, loading: false, error: action.payload };
    case BannerSliderActionTypes.UPDATE_BANNER_SLIDER:
      return { ...state, loading: true };
    case BannerSliderActionTypes.UPDATE_BANNER_SLIDER_SUCCESS:
      return {
        ...state,
        loading: false,
        bannerSliders: state.bannerSliders.map((slider: any) =>
          slider.id === action.payload.id ? action.payload : slider
        ),
      };
    case BannerSliderActionTypes.UPDATE_BANNER_SLIDER_ERROR:
      return { ...state, loading: false, error: action.payload };
    case BannerSliderActionTypes.DELETE_BANNER_SLIDER:
      return { ...state, loading: true };
    case BannerSliderActionTypes.DELETE_BANNER_SLIDER_SUCCESS:
      return {
        ...state,
        loading: false,
        bannerSliders: state.bannerSliders.filter(
          (slider: any) => slider.id !== action.payload
        ),
      };
    case BannerSliderActionTypes.DELETE_BANNER_SLIDER_ERROR:
      return { ...state, loading: false, error: action.payload };
    default:
      return state;
  }
};