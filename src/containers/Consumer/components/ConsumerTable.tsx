import React from 'react';
import { Link } from 'react-router-dom';

import VisibilityIcon from '@material-ui/icons/Visibility';
import DeleteIcon from '@material-ui/icons/Delete';
import { Sorter } from 'components/Sorter/Sorter';
import { useTranslation } from 'react-i18next';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TablePagination,
  TableRow,
  TableHead,
  IconButton,
} from '@material-ui/core';

export const ConsumerTable = ({
  consumers,
  pagination,
  handleDelete,
  filter,
  onFilterChange,
}) => {
  const { t }: { t: any } = useTranslation();
  const [page, setPage] = React.useState(filter.page);
  const [rowsPerPage, setRowsPerPage] = React.useState(filter.pageSize);
  const [selectedConsumer, setSelectedConsumer] = React.useState();
  const [open, setOpen] = React.useState(false);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);
    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };

  const handleClickOpen = (id) => {
    setSelectedConsumer(id);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <TableContainer>
      <Table aria-label="custom pagination table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter
                label={t('id')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'id', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('name')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'firstName', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('email')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'email', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('subscriptionStartDate')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'startDate', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>{t('actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {consumers.map((row) => (
            <TableRow key={row.id}>
              <TableCell scope="row">{row.id}</TableCell>
              <TableCell scope="row">
                {row.firstName + ' ' + row.surname}
              </TableCell>
              <TableCell scope="row">{row.username}</TableCell>
              <TableCell scope="row">
                {row.startDate
                  ? new Date(row.startDate).toLocaleDateString()
                  : '/'}
              </TableCell>
              <TableCell component="th" scope="row">
                <Link to={`/dashboard/customer/${row.id}`}>
                  <IconButton aria-label="view" color="default">
                    <VisibilityIcon />
                  </IconButton>
                </Link>
                <IconButton
                  aria-label="delete"
                  color="secondary"
                  onClick={() => handleClickOpen(row.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={pagination?.last || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>

      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteConsumer')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('noUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              handleDelete(selectedConsumer);
              handleClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>
    </TableContainer>
  );
};
