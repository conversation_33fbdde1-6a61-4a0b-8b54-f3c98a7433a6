.circle {
  height: 29px;
  width: 29px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
  &.big {
    height: 100px;
    width: 100px;
  }
  &.gray {
    background-color: gray;
  }
  &.red {
    background-color: red;
  }
  &.yellow {
    background-color: #ffeb00;
  }
  &.green {
    background-color: #00ff4e;
  }
  &.primary-label {
    margin-top: auto;
    margin-bottom: auto;
  }
}

.CardHorizontal {
  max-width: 730px;
  height: max-content;
}

.centered-block {
  margin-left: auto;
  margin-right: auto;
}
.margin-right {
  margin-right: 50px;
}
.preview-wrapper {
  .CardHorizontal {
    width: 730px;
    height: max-content;
    margin: auto;
  }
}

.CardHorizontal__logo {
  max-width: 85% !important;
}

.feedback-colors {
  .MuiSvgIcon-root {
    font-size: 35px !important;
  }
}
.section-margin-2 {
  margin-top: 2rem;
}

.section-margin-5 {
  margin-top: 5rem;
}

.section-margin-3 {
  margin-top: 3rem;
}

.card-preview {
  border: 1px solid black;
}
