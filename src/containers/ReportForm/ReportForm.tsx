import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Snackbar } from '@material-ui/core';

import { ReportFormView } from './ReportFormView';
import {
  createCategory,
  editCategory,
  getCategory,
  clearCategory,
  clearSuccess,
} from './ReportFormActions';

export default (props) => {
  const dispatch = useDispatch();
  const couponForm = useSelector((state: any) => state.couponForm);
  const [createCouponId, setCreatedCouponId] = useState('');

  const { t }: { t: any } = useTranslation();

  useEffect(() => {
    if (props.match.params.id) {
      dispatch(getCategory(props.match.params.id));
    }

    return () => {
      dispatch(clearCategory());
    };
  }, [dispatch, props]);

  const submitForm = (type: string, data: any) => {
    const success = (data) => {
      setCreatedCouponId(data.id);
      clearCategory();
    };
    if (props.match.params.id) {
      dispatch(editCategory(type, props.match.params.id, data, success));
    } else {
      dispatch(createCategory(type, data, success));
    }
  };

  const resetSuccess = () => {
    dispatch(clearSuccess());
  };

  return (
    <>
      <ReportFormView submitForm={submitForm} loading={couponForm?.loading} />

      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        open={couponForm?.success}
        onClose={resetSuccess}
        autoHideDuration={6000}
        message={
          props.match.params.id ? (
            <span id="message-id">{t('formUpdateSuccess')}</span>
          ) : (
            <span id="message-id">
              {t('formCreateSuccess')}{' '}
              <Link to={'/dashboard/users/' + createCouponId}>
                {t('formEditLink')}
              </Link>
            </span>
          )
        }
      />
    </>
  );
};
