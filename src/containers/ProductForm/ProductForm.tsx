import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { ProductFormView } from './ProductFormView';
import { getProduct, clearProduct } from './ProductFormActions';

export default (props) => {
  const dispatch = useDispatch();
  const productForm = useSelector((state: any) => state.productForm);

  useEffect(() => {
    if (props.match.params.id !== 'create') {
      dispatch(getProduct(props.match.params.id));
    }
    return () => {
      dispatch(clearProduct());
    };
  }, [dispatch, props]);

  return <ProductFormView loading={productForm?.loading} />;
};
