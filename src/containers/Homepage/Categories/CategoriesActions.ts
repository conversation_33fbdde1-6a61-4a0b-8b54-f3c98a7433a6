import * as CategoriesTypes from './CategoriesTypes';
import * as CategoriesAPI from 'repository/categories';
import { Dispatch } from 'redux';

interface CategoryData {
  id?: number | string;
  categoryId: number | string;
  couponIds: (number | string)[];
}

export const getCategories = (couponIds: number[] = []) => async (dispatch: Dispatch) => {
  dispatch({ type: CategoriesTypes.GET_CATEGORIES });
  try {
    const response = await CategoriesAPI.getCategories({ couponIds });
    dispatch({ type: CategoriesTypes.GET_CATEGORIES_SUCCESS, payload: response.data });
  } catch (error) {
    dispatch({ type: CategoriesTypes.GET_CATEGORIES_ERROR, payload: error });
  }
};

export const getCategoryById = (categoryId: number) => async (dispatch: Dispatch) => {
  dispatch({ type: CategoriesTypes.GET_CATEGORY });
  try {
    const response = await CategoriesAPI.getCategoryById(categoryId);
    dispatch({ type: CategoriesTypes.GET_CATEGORY_SUCCESS, payload: response });
  } catch (error) {
    dispatch({ type: CategoriesTypes.GET_CATEGORY_ERROR, payload: error });
  }
};

export const createCategory = (data: CategoryData) => async (dispatch: Dispatch) => {
  dispatch({ type: CategoriesTypes.CREATE_CATEGORY });
  try {
    // Ensure data is in the correct format for the API
    const categoryData = {
      categoryId: Number(data.categoryId),
      couponIds: data.couponIds.map((id: number | string) => Number(id))
    };

    console.log('Creating category with data:', categoryData);
    const response = await CategoriesAPI.createCategory(categoryData);
    dispatch({ type: CategoriesTypes.CREATE_CATEGORY_SUCCESS, payload: response });
    return response;
  } catch (error) {
    console.error('Error creating category:', error);
    dispatch({ type: CategoriesTypes.CREATE_CATEGORY_ERROR, payload: error });
    throw error;
  }
};

export const updateCategory = (data: CategoryData) => async (dispatch: Dispatch) => {
  dispatch({ type: CategoriesTypes.UPDATE_CATEGORY });
  try {
    // Ensure data is in the correct format for the API
    const categoryData = {
      id: Number(data.id),
      categoryId: Number(data.categoryId),
      couponIds: data.couponIds.map((id: number | string) => Number(id))
    };

    console.log('Updating category with data:', categoryData);
    const response = await CategoriesAPI.updateCategory(categoryData);
    dispatch({ type: CategoriesTypes.UPDATE_CATEGORY_SUCCESS, payload: response });
    return response;
  } catch (error) {
    console.error('Error updating category:', error);
    dispatch({ type: CategoriesTypes.UPDATE_CATEGORY_ERROR, payload: error });
    throw error;
  }
};

export const deleteCategory = (categoryId: number | string) => async (dispatch: Dispatch) => {
  dispatch({ type: CategoriesTypes.DELETE_CATEGORY });
  try {
    await CategoriesAPI.deleteCategory(Number(categoryId));
    dispatch({ type: CategoriesTypes.DELETE_CATEGORY_SUCCESS, payload: categoryId });
  } catch (error) {
    console.error('Error deleting category:', error);
    dispatch({ type: CategoriesTypes.DELETE_CATEGORY_ERROR, payload: error });
  }
};
