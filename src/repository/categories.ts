// Repository for Categories API
import API from 'services/api';

export interface Category {
  id?: number;
  name: string;
  description: string;
  status: string;
}

export interface HomepageCategory {
  id?: number;
  categoryId: number | string;
  couponIds: (number | string)[];
}

export interface CategoryListRequest {
  couponIds: number[];
}

export interface CategoryResponse {
  data: HomepageCategory[];
  total: number;
}

export const getCategories = async (request: CategoryListRequest): Promise<CategoryResponse> => {
  const response = await API.post('/homepage-categories/list', request);
  return response.data;
};

export const getCategoryById = async (categoryId: number): Promise<HomepageCategory> => {
  const response = await API.get(`/homepage-categories/${categoryId}`);
  return response.data;
};

export const createCategory = async (data: HomepageCategory): Promise<HomepageCategory> => {
  const response = await API.post('/homepage-categories', data);
  return response.data;
};

export const updateCategory = async (data: HomepageCategory): Promise<HomepageCategory> => {
  const response = await API.put('/homepage-categories', data);
  return response.data;
};

export const deleteCategory = async (categoryId: number): Promise<void> => {
  await API.delete(`/homepage-categories/${categoryId}`);
};
