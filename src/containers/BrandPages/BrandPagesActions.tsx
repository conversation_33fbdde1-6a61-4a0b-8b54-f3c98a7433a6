import * as BrandPageActionTypes from './BrandPagesTypes';
import * as BrandPageService from 'repository/brand-page';

export const changeFilter = (filter) => {
  return async (dispatch) => {
    dispatch({
      type: BrandPageActionTypes.CHANGE_PAGINATION,
      payload: {
        page: filter.page,
        pageSize: filter.pageSize,
        sort: { ...filter.sort },
      },
    });
  };
};

export const getBrandPages = (filter = {}) => {
  return async (dispatch) => {
    dispatch({
      type: BrandPageActionTypes.GET_BRAND_PAGES,
    });

    try {
      const response = await BrandPageService.getBrandPages(filter);
      dispatch({
        type: BrandPageActionTypes.GET_BRAND_PAGES_SUCCESS,
        payload: response.data.data,
      });

      dispatch({
        type: BrandPageActionTypes.BRAND_PAGES_PAGINATION,
        payload: {
          current: response.data.number + 1,
          first: 1,
          last: response.data.total,
        },
      });
    } catch (error) {
      const errorData = error.response?.data;
      dispatch({
        type: BrandPageActionTypes.GET_BRAND_PAGES_ERROR,
        payload: errorData,
      });
    }
  };
};

export const deleteBrandPage = (brandPage) => {
  return async (dispatch) => {
    dispatch({
      type: BrandPageActionTypes.DELETE_BRAND_PAGE,
    });

    // TODO this is disabled on the UI, should we remove the logic?

    try {
      const response = await BrandPageService.deleteBrandPage(brandPage);

      dispatch({
        type: BrandPageActionTypes.DELETE_BRAND_PAGE_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: BrandPageActionTypes.DELETE_BRAND_PAGE_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const enableBrandPage = (brandPage) => {
  return async (dispatch) => {
    dispatch({
      type: BrandPageActionTypes.ENABLE_BRAND_PAGE,
    });

    try {
      const response = await BrandPageService.enableBrandPage(brandPage);

      dispatch({
        type: BrandPageActionTypes.ENABLE_BRAND_PAGE_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: BrandPageActionTypes.ENABLE_BRAND_PAGE_ERROR,
        payload: error.response?.data,
      });
    }
  };
}

export const disableBrandPage = (brandPage) => {
  return async (dispatch) => {
    dispatch({
      type: BrandPageActionTypes.DISABLE_BRAND_PAGE,
    });

    try {
      const response = await BrandPageService.disableBrandPage(brandPage);

      dispatch({
        type: BrandPageActionTypes.DISABLE_BRAND_PAGE_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: BrandPageActionTypes.DISABLE_BRAND_PAGE_ERROR,
        payload: error.response?.data,
      });
    }
  };
}

export const getBrandPagesMap = () => async (dispatch) => {
  dispatch({
    type: BrandPageActionTypes.GET_ALL_BRAND_PAGES_MAP,
  });

  try {
    const response = await BrandPageService.getAllBrandPagesMap();

    dispatch({
      type: BrandPageActionTypes.GET_ALL_BRAND_PAGES_MAP_SUCCESS,
      payload: response.data,
    });
  } catch (error) {
    dispatch({
      type: BrandPageActionTypes.GET_ALL_BRAND_PAGES_MAP_ERROR,
      payload: error.response?.data,
    });
  }
};
