import React, { useEffect, useState, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, useHistory, Prompt } from 'react-router-dom';
import { useParams } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import DateFnsUtils from '@date-io/date-fns';
import {
  MuiPickersUtilsProvider,
  KeyboardDatePicker,
} from '@material-ui/pickers';
import {
  Button,
  FormLabel,
  FormControl,
  Grid,
  InputLabel,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
} from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/Delete';
import CheckBoxRoundedIcon from '@material-ui/icons/CheckBoxRounded';
import ErrorIcon from '@material-ui/icons/Error';
import { Alert } from '@material-ui/lab';
import { makeStyles } from '@material-ui/styles';
import {
  BoxWrap,
  InputError,
  InfoButton,
  Loading,
  UploadBox,
  ReachBoostRequestDialog,
  InfoDialog,
} from 'components';
import { FormTextField } from 'components/FormInputs';
import BrandPricingDetails from '../components/BrandPricingDetails';
import BrandBannerDetails from '../components/BrandBannerDetail';
import CouponSteps from '../components/CouponSteps';
import { BrandAdminsTable } from '../components/BrandAdminsTable';
import useAPIError from 'components/APIErrorNotification/useAPIError';

import * as RoleManager from 'services/role';
import { validateSlug, validateUrl } from 'services/validation';
import {
  copyTextToClipboard,
  checkDateBetweenDates,
  isRichTextEmpty,
} from 'utils/helpers';

import { ErrorMessage } from '@hookform/error-message';
import * as brandPageService from 'repository/brand-page';
import * as brandPageActions from '../BrandPageFormActions';
import * as brandAccountActions from '../../BrandAccount/BrandAccountActions';
import * as CONSTANTS from 'utils/constants';
import { getUsers, clearUsers } from '../../User/UserActions';

import { ReachBoostUpgradeApproval } from 'repository/brand-page';
import { editReachBoostRequest } from '../BrandPageFormActions';
import {
  getCurrentReachBoostData,
  getReachBoostStatus,
  getRequestedReachBoostData,
} from 'containers/BrandAccount/BrandAccount';

import {
  RichTextInput,
  ReadOnlyTextField,
  FormCheckBox,
  SelectMenu,
} from 'components/FormInputs';

import '../BrandPageForm.scss'


const useStyles = makeStyles({
  dialog: {
    '&>.MuiDialog-container': {
      '&>.MuiPaper-root': {
        borderRadius: '10px',
        '&>.MuiDialogContent-root': {
          border: "none",
          fontSize: '16px',
          fontWeight: 500,
        },
        '&>.MuiDialogActions-root': {
          padding: '16px 24px'
        }
      }
    },
  }
});

const EMPTY_BRAND_PACKAGE_TEMPLATE = -1;
let BRAND_DESCRIPTION_MIN_LENGTH = 1800;
const BRAND_DESCRIPTION_MAX_LENGTH = 10_000;

const isIdValid = (id) => id !== 'create';

const brandAdminsFilter = (brandId) => ({
  page: 0,
  pageSize: 10,
  sort: {
    field: 'username',
    dir: 'DESC',
  },
  filters: [
    {
      field: 'roleId',
      operator: 'EQUAL',
      value: 4,
    },
    {
      field: 'brandId',
      operator: 'EQUAL',
      value: brandId,
    },
  ],
  search: '',
});

type FormValues = {
  id: string;
  name: string;
  websiteLink: string;
  slug: string;
  metaTitle: string;
  metaDescription: string;
  shortDescription: string;
  image: string;
  isPopular?: boolean | '0' | '1';
  logo: string;
  isHiddenForNonregisteredConsumer: boolean;
  isHiddenForRegisteredConsumer: boolean;
  s1Title: string;
  s1Media: string;
  s1Description: string;
  s1ShowLearnMoreButton: boolean;
  s1Link: string;
  s1IsHidden: boolean;
  s2BrandHeadline: string;
  s2BrandDescription: string;
  s3MediaLink: string;
  s3IsHidden: boolean;
  hideBrand: string;
  brandPackageId: number | null;
  brandPackageTemplateId: number;
  isBrandInstructionUpdated: boolean;
  step1: string;
  step2: string;
  step3: string;
  isPaywallDisabled: boolean;
  isBlocked: boolean;
  isVisible: boolean;
  deactivateBrandPageRecommendation: boolean;
  isCountDownEnabled: boolean;
  enableSoftRenewal: boolean;
  editMetaFieldsAndTitle: boolean;
  disableNewsletter: boolean;
  bannerText: string;
  bannerRedirectUrl: string;
  enableBanner: boolean;
  bannerScheduledDate: null;
  bannerExpiryDate: null;
};

const step1DefaultValue = 'Suche dir bei CaptainCoupon deinen passenden BRANDNAME Gutschein aus und achte dabei auf die Gutscheinbedingungen.';
const step2DefaultValue = 'Klicke auf “Gutscheincode”, um den BRANDNAME Rabattcode anzuzeigen und kopiere den Code.';
const step3DefaultValue = 'Klicke auf den Link zum BRANDNAME Online Shop und gib den BRANDNAME Gutscheincode bei deinem Einkauf im Warenkorb an.';

const idOfBasicPackageTemplate = (brandPagePackageTemplates) =>
  brandPagePackageTemplates.find((template) => template.name === 'BASIC').id;

interface AdminFormProps {
  brandPage: any;
  brandPagePackageTemplates: any;
  brandInfoTexts: any;
}

interface ReachBoostType {
  active: boolean;
  requested: any;
  current: any;
}

export const AdminForm = ({
  brandPage,
  brandPagePackageTemplates = [],
  brandInfoTexts,
}: AdminFormProps) => {

  const defaultFormValues: FormValues = {
    id: '',
    name: '',
    websiteLink: '',
    slug: '',
    metaTitle: '[[highest-offer]] [[brand]] Gutscheincode – [[month]] [[year]] exklusiv auf CaptainCoupon',
    metaDescription: '[[highest-offer]] Gutschein von [[brand]]. Mit diesem exklusiven Angebot von CaptainCoupon erhältst du bis zum [[exp-date]] [[highest-offer]] Rabatt bei [[brand]]',
    shortDescription: '',
    image: '',
    isPopular: undefined,
    logo: '',
    isHiddenForNonregisteredConsumer: false,
    isHiddenForRegisteredConsumer: false,
    s1Title: '',
    s1Media: '',
    s1Description: '<p><br></p>',
    s1ShowLearnMoreButton: false,
    s1Link: '',
    s1IsHidden: false,
    s2BrandHeadline: 'Exklusiv [[highest-offer]] [[brand]] Rabattcode gültig und geprüft',
    s2BrandDescription: '<p><br></p>',
    s3MediaLink: '',
    s3IsHidden: false,
    hideBrand: 'NONE',
    brandPackageId: null,
    brandPackageTemplateId: EMPTY_BRAND_PACKAGE_TEMPLATE,
    isBrandInstructionUpdated: false,
    step1: step1DefaultValue,
    step2: step2DefaultValue,
    step3: step3DefaultValue,
    isPaywallDisabled: false,
    isBlocked: false,
    isVisible: true,
    deactivateBrandPageRecommendation: false,
    isCountDownEnabled: true,
    enableSoftRenewal: false,
    editMetaFieldsAndTitle: false,
    disableNewsletter: false,
    bannerText: '',
    bannerRedirectUrl: '',
    enableBanner: false,
    bannerScheduledDate: null,
    bannerExpiryDate: null,
  };

  const { t }: { t: any } = useTranslation();
  let { id } = useParams();
  const { addMessage } = useAPIError();
  const classes = useStyles();

  const users = useSelector((state: any) => state.user);
  const brandPageForm = useSelector((state) => state.brandPageForm);
  const brandAccount = useSelector((state) => state.brandAccount);
  const reachBoostRequestSuccess = useSelector(
    (state) => state.brandAccount?.didReachBoostRequestSucceed
  );
  const { errorSave: saveBrandPageError } = brandPageForm;

  const isFreeImageVideoEnabled = brandPage?.freeImageVideo;
  const isFreeUnboxingVideoEnabled = brandPage?.freeUnboxingVideo;

  const [showSnackIncompleteBrand, setShowSnackIncompleteBrand] = useState(
    brandPage?.hideBrand === 'ALL'
  );

  const replaceWithBrandPageName = (inputString: string, brandName: string): string => {
    if(!brandName) return inputString;
    return inputString.replace(/BRANDNAME/g, brandName)
  };

  const [newStep1DefaultValue, setNewStep1DefaultValue] = useState(
    brandPage ? replaceWithBrandPageName(defaultFormValues.step1, brandPage.name) : ""
  )
  const [newStep2DefaultValue, setNewStep2DefaultValue] = useState(
    brandPage ? replaceWithBrandPageName(defaultFormValues.step2, brandPage.name) : ""
  )
  const [newStep3DefaultValue, setNewStep3DefaultValue] = useState(
    brandPage ? replaceWithBrandPageName(defaultFormValues.step3, brandPage.name) : ""
  )

  defaultFormValues.step1 = newStep1DefaultValue;
  defaultFormValues.step2 = newStep2DefaultValue;
  defaultFormValues.step3 = newStep3DefaultValue;

  const [defaultValues] = useState(
    brandPage
      ? { ...defaultFormValues, ...brandPage }
      : { ...defaultFormValues }
  );

  const [isReachBoostDialogOpen, setIsReachBoostDialogOpen] = useState(false);
  const [infoHtmlText, setInfoHtmlText] = useState<string | undefined>();

  const onInfoClick = useCallback(
    (translationKey) => setInfoHtmlText(t(translationKey)),
    [t, setInfoHtmlText]
  );

  const onInfoClose = useCallback(
    () => setInfoHtmlText(undefined),
    [setInfoHtmlText]
  );

  let selectedTemplate: any = {};
  if (RoleManager.isAbleTo('brand_page', 'editPricing')) {
    selectedTemplate = brandPagePackageTemplates?.find(
      (template) =>
        template.id === defaultValues.brandPackageTemplateId ||
        idOfBasicPackageTemplate(brandPagePackageTemplates)
    );
  }
  const formValues = useForm<FormValues>({
    criteriaMode: 'all',
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: {
      ...(RoleManager.isAbleTo('brand_page', 'editPricing')
        ? selectedTemplate
        : {}),
      ...defaultValues,
    },
  });

  const canEditMetaFieldsAndTitle = RoleManager.isAbleTo(
    'brand_page', 'editPricing'
  ) || brandPage?.editMetaFieldsAndTitle;

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    control,
    errors,
    setError,
    clearErrors,
    trigger,
    watch,
    formState: { isDirty },
  } = formValues;

  register('brandPackageId');

  const hideBrandWatcher = watch('hideBrand');
  const brandPageNameWatcher = watch('name');
  const s1ShowLearnMoreButtonWatcher = watch('s1ShowLearnMoreButton');
  const s1IsHiddenWatcher = watch('s1IsHidden');
  const s3IsHiddenWatcher = watch('s3IsHidden');
  const isBrandInstructionUpdatedWatcher = watch('isBrandInstructionUpdated');

  const dispatch = useDispatch();
  const history = useHistory();

  const NONE_SELECTED = t('noFileSelected');

  const hideBrandOptions = [
    {
      name: t('none'),
      value: 'NONE',
    },
    {
      name: t('all'),
      value: 'ALL',
    },
    {
      name: t('unregistered'),
      value: 'UNREGISTERED',
    },
  ];

  const userProfile = useSelector((state: any) => state.user?.userProfile);

  const restrictSeoFields = !!userProfile?.restrictSeoFields

  const parseFileName = (path) => {
    if (!path) return NONE_SELECTED;
    return path.split('/').slice(-1)[0].split('-').slice(2).join('');
  };


  // put helper states here
  const [s1richTextField, setS1RichTextField]: any = useState('');
  const [s2richTextField, setS2RichTextField]: any = useState('');

  const [brandPictureFile, setBrandPictureFile] = useState<any>();
  const [brandPictureFileName, setBrandPictureFileName] = useState(
    !!brandPage && brandPage?.image
      ? parseFileName(brandPage.image)
      : NONE_SELECTED
  );

  const [brandLogoFile, setBrandLogoFile] = useState<any>();
  const [brandLogoFileName, setBrandLogoFileName] = useState(
    !!brandPage && brandPage?.logo
      ? parseFileName(brandPage.logo)
      : NONE_SELECTED
  );

  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const [isLogoUpdated, setIsLogoUpdated] = useState(false);
  const [typeForDelete, setTypeForDelete] = useState('');
  const [delConfOpen, setDelConfOpen] = useState(false);
  const [dialogForVideo, setDialogForVideo] = useState(false);
  const [dialogVideoType, setDialogVideoType] = useState();
  const [isFormSubmitted, setIsFormSubmitted] = useState(false);

  const {
    sfBillingCity,
    sfBillingPostalCode,
    sfBillingStreet,
    sfBillingCountry,
    sfCategory,
    sfInterestedInWeVoucher,
    sfName,
    sfWebsite,
  } = brandPage?.private || {};

  const hasSalesforceAddressInformation =
    sfBillingCity || sfBillingPostalCode || sfBillingStreet || sfBillingCountry;

  const hasSalesforceAccountInformation =
    sfCategory || sfInterestedInWeVoucher || sfName || sfWebsite;

  let reachBoost: ReachBoostType = {
    active: false,
    requested: undefined,
    current: undefined,
  };

  if (brandPageForm.activeBrandPage) {
    const { reachBoosts, reachBoost: active } = brandPageForm.activeBrandPage;
    const requested = getRequestedReachBoostData(reachBoosts);
    const current = getCurrentReachBoostData(reachBoosts);

    reachBoost = {
      active,
      requested,
      current,
    };
  }
  const reachBoostStatus = getReachBoostStatus(reachBoost, t);
  const isActive = (reachBoost) => {
    return (
      reachBoost && reachBoost.status === CONSTANTS.reachBoostStatus.ACTIVE
    );
  };

  useEffect(() => {
    window.addEventListener('beforeunload', (event) => {
      if (isDirty && !isFormSubmitted) {
        event.preventDefault();
        event.returnValue = t(`areYouSureYouWantToLeave`);
      }
    });

    if (
      brandPage?.id &&
      brandPage?.hideBrand !== 'NONE' &&
      RoleManager.isAbleTo('brand_page', 'editPricing')
    ) {
      trigger();
    }

    if (!isIdValid(id)) {
      setBrandPictureFileName(NONE_SELECTED);
      setBrandLogoFileName(NONE_SELECTED);
    } else {
      dispatch(clearUsers());
      if (RoleManager.isAbleTo('brand_page', 'editPricing')) {
        dispatch(getUsers(brandAdminsFilter(brandPage.id)));
      }
    }

    return () => dispatch(brandPageActions.clearBrandPage());
  }, []);

  useEffect(() => {
    const name = getValues('name')
    var newStep1DefaultValue : string;
    var newStep2DefaultValue : string;
    var newStep3DefaultValue : string;

    if(!!name.length) {
      newStep1DefaultValue = replaceWithBrandPageName(step1DefaultValue, name);
      newStep2DefaultValue = replaceWithBrandPageName(step2DefaultValue, name);
      newStep3DefaultValue = replaceWithBrandPageName(step3DefaultValue, name);
    } else {
      newStep1DefaultValue = "";
      newStep2DefaultValue = "";
      newStep3DefaultValue = "";
    }

    setNewStep1DefaultValue(newStep1DefaultValue)
    setNewStep2DefaultValue(newStep2DefaultValue)
    setNewStep3DefaultValue(newStep3DefaultValue)

    if(!isBrandInstructionUpdatedWatcher) {
      setValue('step1', newStep1DefaultValue);
      setValue('step2', newStep2DefaultValue);
      setValue('step3', newStep3DefaultValue);
    }
  }, [brandPageNameWatcher, isBrandInstructionUpdatedWatcher])

  useEffect(() => {
    if (!isBrandInstructionUpdatedWatcher) {
      setValue('step1', newStep1DefaultValue);
      setValue('step2', newStep2DefaultValue);
      setValue('step3', newStep3DefaultValue);
    } else if (defaultFormValues.step1 !== '') {
      setValue('step1', getValues('step1') || brandPage?.step1 || defaultFormValues.step1);
      setValue('step2', getValues('step2') || brandPage?.step2 || defaultFormValues.step2);
      setValue('step3', getValues('step3') || brandPage?.step3 || defaultFormValues.step3);
    }
  }, [isBrandInstructionUpdatedWatcher, defaultFormValues, setValue]);

  const handleVideoDialogOpen = (type) => {
    setDialogVideoType(type);
    setDialogForVideo(true);
  };
  const handleVideoDialogClose = () => {
    setDialogForVideo(false);
  };

  const handleDelConfClose = () => {
    setDelConfOpen(false);
  };

  const handleDelConfOpen = () => {
    setDelConfOpen(true);
  };

  const onMediaDelete = () => {
    switch (typeForDelete) {
      case 'image':
        setIsImageUpdated(true);
        setBrandPictureFile(null);
        setBrandPictureFileName(NONE_SELECTED);
        break;
      case 'logo':
        setIsLogoUpdated(true);
        setBrandLogoFile(null);
        setBrandLogoFileName(NONE_SELECTED);
        break;
    }
  };

  const uploadFile = (file, type) => {
    if (!file.length) {
      return;
    }
    switch (type) {
      case 'brandPicture':
        setIsImageUpdated(true);
        setBrandPictureFile(file[0]);
        setBrandPictureFileName(file[0].name);
        break;
      case 'brandLogo':
        setIsLogoUpdated(true);
        setBrandLogoFile(file[0]);
        setBrandLogoFileName(file[0].name);
        break;
    }
  };

  useEffect(() => {
    if (saveBrandPageError) {
      switch (saveBrandPageError.errorCode) {
        case 'brandPageIncomplete':
          setError('hideBrand', {
            type: 'manual',
            message: t('incompleteBrandsMustBeHidden'),
            shouldFocus: true,
          });
          break;
        case 'slugExists':
          setError('slug', {
            type: 'manual',
            message: t('slugExists'),
            shouldFocus: true,
          });
          break;
      }
      addMessage(t(saveBrandPageError.errorCode || 'errorOccured'), 'error');
      dispatch(brandPageActions.clearBrandPageErrors());
    } else if (brandPageForm.errorReachBoost) {
      addMessage(
        t(brandPageForm.errorReachBoost.errorCode || 'errorOccured'),
        'error'
      );
      dispatch(brandPageActions.clearBrandPageErrors());
    } else if (brandPageForm.successReachBoost) {
      addMessage(t('successApprovedReachBoost'), 'success');
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else if (reachBoostRequestSuccess) {
      addMessage(t('successReachBoostRequest'), 'success');
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else if (brandPageForm.success || brandPageForm.editSuccess) {
      addMessage(t('successSaveBrand'), 'success');
      if (RoleManager.isAbleTo('brand_page', 'editPricing')) {
        history.push('/dashboard/brand-pages');
      }
    }
  }, [brandPageForm, saveBrandPageError, reachBoostRequestSuccess, addMessage]);

  useEffect(() => {
    if (brandAccount.didRequestSucceed) {
      addMessage(t('brandAccountRequestSuccess'), 'success');
    }
  }, [brandAccount.didRequestSucceed, addMessage, t]);

  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      const firstErrorKey = Object.keys(errors)[0];

      if (firstErrorKey && errors[firstErrorKey]?.ref) {
        const errorFieldContainer = document.querySelector(`[data-id-name="${firstErrorKey}"]`);
        if (errorFieldContainer) {
          errorFieldContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    }
  }, [errors]);

  const approveReachBoostRequest = (
    reachBoostRequestData: ReachBoostUpgradeApproval
  ) => {
    dispatch(editReachBoostRequest(reachBoostRequestData));
    setIsReachBoostDialogOpen(false);
  };

  const sendReachBoostRequest = (reachBoostRequestData) => {
    dispatch(
      brandAccountActions.sendReachBoostUpgradeRequest(reachBoostRequestData)
    );
    setIsReachBoostDialogOpen(false);
  };

  const onSubmitReachBoostDialog = RoleManager.isAbleTo(
    'brand_page',
    'editPricing'
  )
    ? approveReachBoostRequest
    : sendReachBoostRequest;

  const submitReachBoostButtonLabel = RoleManager.isAbleTo(
    'brand_page',
    'editPricing'
  )
    ? t('approveChanges')
    : t('requestChanges');

  const submit = (data) => {
    setIsFormSubmitted(true);
    if (id && isIdValid(id) && isLogoUpdated) {
      dispatch(brandPageActions.deleteMedia(brandPage?.logo));
    }

    if (id && isIdValid(id) && isImageUpdated) {
      dispatch(brandPageActions.deleteMedia(brandPage?.image));
    }

    if (!data.isBrandInstructionUpdated) {
      delete data.step1;
      delete data.step2;
      delete data.step3;
    }

    dispatch(
      brandPageActions.saveBrandPage(
        isIdValid(typeof id !== 'undefined' ? id : null) ? id : null,
        data,
        RoleManager.isAbleTo('brand_page', 'editPricing')
      )
    );
  };

  const handleUpload = (submit) => async (data) => {
    let imageLocation = brandPage?.image;
    let logoLocation = brandPage?.logo;

    if (brandPictureFile) {
      try {
        const formData = new FormData();
        formData.append('file', brandPictureFile);
        const response = await brandPageService.uploadFile(formData, brandPage?.id, 'banner');
        imageLocation = response.data.fileName;
      } catch {
        addMessage(t('errorOccured'), 'error');
        return;
      }
    }

    if (brandLogoFile) {
      try {
        const formData = new FormData();
        formData.append('file', brandLogoFile);
        const response = await brandPageService.uploadFile(formData, brandPage?.id, 'logo');
        logoLocation = response.data.fileName;
      } catch {
        addMessage(t('errorOccured'), 'error');
        return;
      }
    }
    submit({ ...data, image: imageLocation, logo: logoLocation });
  };

  const validateForCCAdmin = (data) => {
    let ccAdminValidationPassed = true;
    if (!data.name) {
      ccAdminValidationPassed = false;
      setError('name', {
        type: 'required',
        message: t('brandNameRequired'),
        shouldFocus: true,
      });
    } else if (data.name.length > 128) {
      ccAdminValidationPassed = false;
      setError('name', {
        type: 'maxLength',
        message: t('brandNameLength128'),
        shouldFocus: true,
      });
    }

    if (data.brandPackageTemplateId !== EMPTY_BRAND_PACKAGE_TEMPLATE) {
      const brandPricingFields = [
        'coupons',
        'couponUpdates',
        'pricePerCoupon',
        'pricePerCouponUpdate',
        'pricePerImageVideo',
        'pricePerUnboxingVideo',
      ];
      brandPricingFields.forEach((field: string) => {
        if (!data[field] && typeof data[field] !== 'number') {
          ccAdminValidationPassed = false;
          //@ts-ignore
          setError(field, {
            type: 'required',
            message: t('brandPricingFieldRequired'),
            shouldFocus: true,
          });
        } else if (Number.parseFloat(data[field]) < 0) {
          ccAdminValidationPassed = false;
          //@ts-ignore
          setError(field, {
            type: 'min',
            message: t('brandPricingFieldMustNotBeNegative'),
            shouldFocus: true,
          });
        }
      });
    }

    return ccAdminValidationPassed;
  };

  const customHandleSubmit = (onSubmit) => (event) => {
    event.preventDefault();

    if (
      RoleManager.isAbleTo('brand_page', 'editPricing') &&
      hideBrandWatcher === 'ALL'
    ) {
      if (validateForCCAdmin(getValues())) {
        setShowSnackIncompleteBrand(true);
        onSubmit(getValues());
      }
    } else {
      handleSubmit(onSubmit)(event);
    }

  };

  if(RoleManager.isAbleTo('brand_page', 'editPricing')) {
    BRAND_DESCRIPTION_MIN_LENGTH = 100;
  }

  return (
    <>
    {
      showSnackIncompleteBrand &&
      <Alert
        variant="filled"
        severity="info"
        onClose={() => setShowSnackIncompleteBrand(false)}
        className='mt-2 mb-3 alert-box'
      >
        {
          RoleManager.isAbleTo('brand_page', 'editPricing')
          ? t('brandIncompleteBannerText')
          : t('notLiveMessage')
        }
      </Alert>
    }
      <form onSubmit={customHandleSubmit(handleUpload(submit))}>
        <BoxWrap>
          <Grid container spacing={4}>
            <Grid
              item
              sm={6}
              xs={12}
            >
              <FormLabel>
                  {t('brandName')}
                  <InfoButton
                    className="float-right"
                    onClick={brandInfoTexts['brandNameInfo']}
                  />
              </FormLabel>
              <FormTextField
                name="name"
                label={''}
                margin="none"
                size="small"
                variant="outlined"
                className="rounded-input-20"
                formValues={formValues}
                rules={{
                  required: t('brandNameRequired'),
                  maxLength: {
                    value: 128,
                    message: t('brandNameLength128'),
                  },
                }}
              />
            </Grid>
            <Grid
              item
              sm={6}
              xs={12}
            >
              <FormLabel>
                  {t('brandWebsite')}
                  <InfoButton
                    className="float-right"
                    onClick={brandInfoTexts['brandWebsiteLinkInfo']}
                  />
              </FormLabel>
              <FormTextField
                name="websiteLink"
                label={''}
                formValues={formValues}
                margin="none"
                size="small"
                variant="outlined"
                className="rounded-input-20"
                rules={{
                  maxLength: {
                    value: 256,
                    message: t('brandWebsiteLength256'),
                  },
                  validate: (url) => {
                    if (!validateUrl(url)) {
                      return t('brandWebsiteValidUrl');
                    }
                    return true;
                  },
                }}
                helperText={[
                  <i key="nameHelperI">
                    {t('example')} http://yourwebsite.com
                    <br />
                  </i>,
                  errors.websiteLink ? (
                    <InputError
                      message={errors.websiteLink.message}
                      key="nameHelperM"
                    />
                  ) : null,
                ]}
              />
            </Grid>
            <Grid
              item
              sm={6}
            >
              <FormLabel disabled={restrictSeoFields}>
                {t('slug')}
                <InfoButton
                  className="float-right"
                  onClick={brandInfoTexts['brandSlugInfo']}
                  disabled={restrictSeoFields}
                />
              </FormLabel>
              <FormTextField
                name="slug"
                label={''}
                margin="none"
                formValues={formValues}
                size="small"
                variant="outlined"
                className="rounded-input-20"
                disabled={restrictSeoFields}
                rules={{
                  validate: (slug) => {
                    const isSlugValid = validateSlug(slug);
                    if (!isSlugValid) {
                      return t('slugNoSpacesOrCharacters');
                    }
                    return true;
                  },
                  required: t('slugRequired'),
                  maxLength: {
                    value: 64,
                    message: t('slugLessThan64'),
                  },
                }}
                onChange={(event) =>
                  formValues.setValue('slug', event.target.value.toLowerCase())
                }
                helperText={
                  errors.slug && <InputError message={errors.slug.message} />
                }
                showRestrictInfo={restrictSeoFields}
                brandInfoTexts={brandInfoTexts}
              />
            </Grid>
            <Grid
              item
              sm={6}
            >
              <FormLabel>
                  {t('linkTitle')}
                  <InfoButton
                    className="float-right"
                    onClick={brandInfoTexts['brandWebsiteLinkTitleInfo']}
                  />
              </FormLabel>
              <FormTextField
                name="websiteLinkTitle"
                label={''}
                margin="none"
                formValues={formValues}
                size="small"
                variant="outlined"
                className="rounded-input-20"
                rules={{
                  maxLength: {
                    value: 256,
                    message: t('linkTitleLess256'),
                  },
                }}
              />
            </Grid>

            <Grid
              item
              sm={6}
            >
                <FormLabel disabled={restrictSeoFields}>
                  {t('metaTitle')}
                  <InfoButton
                    className="float-right"
                    onClick={brandInfoTexts['brandMetaTitleInfo']}
                    disabled={restrictSeoFields}
                  />
                </FormLabel>
                <FormTextField
                  name="metaTitle"
                  label=""
                  margin="none"
                  formValues={formValues}
                  multiline={true}
                  rows={4}
                  size="small"
                  variant="outlined"
                  className="rounded-input-10"
                  disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  style={{ marginTop: '10px', paddingRight: '24px' }}
                  rules={{
                    maxLength: {
                      value: 250,
                      message: t('metaTitleLess250'),
                    },
                  }}
                  onChange={(event) =>
                    formValues.setValue('metaTitle', event.target.value)
                  }
                  helperText={
                    errors.metaTitle && (
                      <InputError message={errors.metaTitle.message} />
                    )
                  }
                  showRestrictInfo={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  brandInfoTexts={brandInfoTexts}
                />
            </Grid>

            <Grid
              item
              sm={6}
            >
                <FormLabel disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}>
                  {t('metaDescription')}
                  <InfoButton
                    className="float-right"
                    onClick={brandInfoTexts['brandMetaDescriptionInfo']}
                    disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  />
                </FormLabel>
                <FormTextField
                  name="metaDescription"
                  label=""
                  margin="none"
                  formValues={formValues}
                  multiline={true}
                  rows={4}
                  style={{ marginTop: '10px' }}
                  size="small"
                  className="rounded-input-10"
                  variant="outlined"
                  disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  rules={{
                    maxLength: {
                      value: 500,
                      message: t('metaDescriptionLess500'),
                    },
                  }}
                  onChange={(event) =>
                    formValues.setValue('metaDescription', event.target.value)
                  }
                  helperText={
                    errors.metaDescription && (
                      <InputError message={errors.metaDescription.message} />
                    )
                  }
                  showRestrictInfo={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  brandInfoTexts={brandInfoTexts}
                />
            </Grid>

            <Grid
              item
              sm={6}
            >
                <FormLabel disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}>
                  {t('shortDescription')}
                  <InfoButton
                    className="float-right"
                    onClick={brandInfoTexts['brandMetaDescriptionInfo']}
                    disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  />
                </FormLabel>
                <FormTextField
                  name="shortDescription"
                  label=""
                  margin="none"
                  formValues={formValues}
                  multiline={true}
                  rows={4}
                  style={{ marginTop: '10px' }}
                  size="small"
                  className="rounded-input-10"
                  variant="outlined"
                  disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  rules={{
                    maxLength: {
                      value: 500,
                      message: t('shortDescriptionLess500'),
                    },
                  }}
                  onChange={(event) =>
                    formValues.setValue('shortDescription', event.target.value)
                  }
                  helperText={
                    errors.shortDescription && (
                      <InputError message={errors.shortDescription.message} />
                    )
                  }
                  showRestrictInfo={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                  brandInfoTexts={brandInfoTexts}
                />
            </Grid>
          </Grid>

          <Grid
            container
            spacing={4}
            style={{
              marginTop: '30px',
            }}
          >
            <Grid item xs={12} sm={6} md={6}>
              <FormLabel>
                {t('brandPicture')}
                <span className="secondary-label pl-1">
                  {t('brandPictureSecondaryLabel')}
                </span>
                <InfoButton
                  className="float-right"
                  onClick={brandInfoTexts['brandImageInfo']}
                />
              </FormLabel>
              <Controller
                name="image"
                control={control}
                rules={{
                  validate: function () {
                    if (brandPictureFileName === NONE_SELECTED) {
                      return t('brandPictureRequired');
                    } else if (brandPictureFileName.length > 221) {
                      return t('brandPictureNameLess221');
                    }
                    return true;
                  },
                }}
                as={
                  <div className='pt-15px'>
                    <UploadBox
                      acceptedFiles=".png,.jpg,.jpeg,.gif"
                      onDrop={(file) => {
                        uploadFile(file, 'brandPicture');
                        setTimeout(() => trigger('image'), 0);
                      }}
                    >
                      {t('browseImage')}
                    </UploadBox>
                    <a
                      style={{ display: 'inline-block', marginLeft: '10px', color: "#000000" }}
                      href={!!brandPage && brandPage?.image}
                      target='blank'
                    >
                      {brandPictureFileName}
                    </a>
                    {brandPictureFileName !== NONE_SELECTED && (
                      <IconButton
                        aria-label="delete"
                        color="secondary"
                        onClick={() => {
                          handleDelConfOpen();
                          setTypeForDelete('image');
                        }}
                      >
                        <DeleteIcon className='delete-icon' />
                      </IconButton>
                    )}
                  </div>
                }
              />
              <br />
              <ErrorMessage
                errors={errors}
                name="image"
                render={({ message }) => <InputError message={message} />}
              />
            </Grid>

            {RoleManager.isAbleTo('brand_page', 'editPricing') ? (
              <Grid item xs={6}>
                <Grid container spacing={4}>
                  <Grid item xs={6}>
                    <FormLabel>
                      {t('isPopular')}
                      <InfoButton
                        onClick={brandInfoTexts['brandIsPopularInfo']}
                      />
                    </FormLabel>
                    <Controller
                      control={control}
                      name="isPopular"
                      rules={{
                        validate: {
                          req: function (value) {
                            return value === true || value === false;
                          },
                        },
                      }}
                      render={({ value }) => (
                        <div className='pt-15px'>
                          <label style={{ marginRight: '20px' }}>
                            <input
                              type="radio"
                              value="1"
                              name="isPopular1"
                              onChange={(event) => {
                                setValue('isPopular', true);
                                trigger('isPopular');
                              }}
                              checked={value === true || value === '1'}
                            />
                            {t('yes')}
                          </label>

                          <label>
                            <input
                              type="radio"
                              value="0"
                              name="isPopular0"
                              onChange={(event) => {
                                setValue('isPopular', false);
                                trigger('isPopular');
                              }}
                              checked={value === false || value === '0'}
                            />
                            {t('no')}
                          </label>
                        </div>
                      )}
                    />
                    {errors.isPopular && (
                      <InputError message={t('isPopularIsRequired')} />
                    )}
                  </Grid>
                  <Grid item xs={6}>
                    <FormCheckBox
                      control={control}
                      name="isPaywallDisabled"
                      label={t('disablePaywall')}
                      style={{ marginLeft: '-12px', marginTop: '20px', display: 'flex', alignItems: 'center' }}
                      checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                    />
                  </Grid>
                </Grid>
              </Grid>
            ) : null}
            <Grid item xs={12} sm={8} md={6}>
              <FormLabel>
                {t('brandLogo')}
                <span className="secondary-label pl-1">
                  {t('brandLogoSecondaryLabel')}
                </span>
                <InfoButton
                  className="float-right"
                  onClick={brandInfoTexts['brandLogoInfo']}

                />
              </FormLabel>
              <Controller
                name="logo"
                control={control}
                rules={{
                  validate: () => {
                    if (brandLogoFileName === NONE_SELECTED) {
                      return t('brandLogoRequired');
                    } else if (brandLogoFileName.length > 221) {
                      return t('brandLogoLess221');
                    }
                    return true;
                  },
                }}
                as={
                  <div className='pt-15px'>
                    <UploadBox
                      acceptedFiles=".png,.jpg,.jpeg,.gif"
                      onDrop={(file) => {
                        uploadFile(file, 'brandLogo');
                        setTimeout(() => trigger('logo'), 0);
                      }}
                    >
                      {t('browseImage')}
                    </UploadBox>
                    <a
                      style={{ display: 'inline-block', marginLeft: '10px', color: "#000000" }}
                      href={!!brandPage && brandPage?.logo}
                      target='blank'
                    >
                      {brandLogoFileName}
                    </a>
                    {brandLogoFileName !== NONE_SELECTED && (
                      <IconButton
                        aria-label="delete"
                        color="secondary"
                        onClick={() => {
                          handleDelConfOpen();
                          setTypeForDelete('logo');
                        }}
                      >
                        <DeleteIcon className='delete-icon' />
                      </IconButton>
                    )}
                  </div>
                }
              />
              <br />
              <ErrorMessage
                errors={errors}
                name="logo"
                render={({ message }) => <InputError message={message} />}
              />
            </Grid>
            {RoleManager.isAbleTo('brand_page', 'editPricing') ? (
              <Grid
                item
                xs={6}
              >
                <FormControl fullWidth margin="normal">
                  <InputLabel htmlFor="hideBrand">
                    {t('hideBrandForUsers')}
                    <InfoButton onClick={brandInfoTexts['brandHideInfo']} />
                  </InputLabel>
                  <SelectMenu
                    name="hideBrand"
                    control={control}
                    rules={{ required: true }}
                    selectOptions={hideBrandOptions}
                    itemKeyProperty="value"
                    itemValueProperty="value"
                    itemNameProperty="name"
                  />
                  <ErrorMessage
                    errors={errors}
                    name="hideBrand"
                    render={({ message }) => (
                      <InputError message={t('hideBrandOptionRequired')} />
                    )}
                  />
                </FormControl>
              </Grid>
            ) : null}
          </Grid>

          <Grid container spacing={5} style={{ marginTop: '40px' }}>
            <Grid item sm={12} xl={12} md={12}>
              <FormLabel disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}>
                {t('brandHeadline')}
              </FormLabel>
              <InfoButton
                className="float-right"
                onClick={brandInfoTexts['brandS2BrandHeadlineInfo']}
                disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
              />
              <Grid item sm={12} xl={12}>
                  <FormTextField
                    name="s2BrandHeadline"
                    label=""
                    margin="none"
                    formValues={formValues}
                    size="small"
                    variant="outlined"
                    className="rounded-input-20"
                    disabled={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                    rules={{
                      required: t('brandHeadlineRequired'),
                      maxLength: {
                        value: 256,
                        message: t('brandHeadlineLess256'),
                      },
                    }}
                    showRestrictInfo={(restrictSeoFields || !canEditMetaFieldsAndTitle)}
                    brandInfoTexts={brandInfoTexts}
                  />
              </Grid>
              <Grid
                item
                sm={12}
                xl={12}
                md={12}
                className="brandDescription"
                style={{ marginTop: '50px' }}
              >
                <FormLabel>
                  {t('brandDescription')}
                  <span className="secondary-label pl-1">
                    {t('brandDescriptionLimit')}
                  </span>
                </FormLabel>
                <InfoButton
                  className="float-right"
                  onClick={brandInfoTexts['brandS2BrandDescriptionInfo']}
                />
                <RichTextInput
                  name="s2BrandDescription"
                  control={control}
                  validate={{
                    required: (value) => {
                      const isValid = s2richTextField.trim().length > 0 || !isRichTextEmpty(value);
                      return isValid || t('brandDescriptionRequired');
                    },
                    minLength: (value) => {
                      const textLength = s2richTextField.length;
                      return textLength >= BRAND_DESCRIPTION_MIN_LENGTH ||
                        t('brandDescriptionMinMaxLength', { minLength: BRAND_DESCRIPTION_MIN_LENGTH });
                    },
                    maxLength: (value) => {
                      const textLength = s2richTextField.length;
                      return textLength <= BRAND_DESCRIPTION_MAX_LENGTH ||
                        t('brandDescriptionMinMaxLength', { maxLength: BRAND_DESCRIPTION_MAX_LENGTH });
                    }
                  }}
                  onChangeHandler={(onChange) => (content, delta, source, editor) => {
                    onChange(content);
                    setS2RichTextField(editor.getText());
                  }}
                  theme="snow"
                  style={{ marginTop: '10px' }}
                />

                {errors.s2BrandDescription && (
                  <ErrorMessage
                    errors={errors}
                    name="s2BrandDescription"
                    render={({ message }) => <InputError message={message} />}
                  />
                )}
              </Grid>
            </Grid>
          </Grid>

          <Grid container spacing={1} style={{ marginTop: '50px' }}>
            <Grid item sm={6} xl={12} md={12}>
              <FormLabel>{t('imageVideo')}</FormLabel>
              <Grid
                container
                spacing={4}
                style={{ marginTop: '0px', paddingRight: '29px' }}
              >
                <Grid item sm={6}>
                  <FormLabel style={{ fontSize: '12px' }}>
                    {t('youtubeOrVimeoLink')}
                    <InfoButton
                      className="float-right"
                      onClick={brandInfoTexts['brands3MediaLinkInfo']}
                    />
                  </FormLabel>
                  <FormTextField
                    name="s3MediaLink"
                    label={''}
                    margin="none"
                    size="small"
                    variant="outlined"
                    className="rounded-input-20"
                    formValues={formValues}
                    rules={{
                      maxLength: {
                        value: 256,
                        message: t('mediaLinkLess256'),
                      },
                      validate: (value) =>
                        (isFreeImageVideoEnabled ||
                          RoleManager.isAbleTo(
                            'brand_page',
                            'editPricing'
                          )) &&
                        !s3IsHiddenWatcher &&
                        !validateUrl(value)
                          ? t('mediaLinkValidUrl')
                          : true,
                    }}
                    disabled={
                      !isFreeImageVideoEnabled &&
                      !RoleManager.isAbleTo('brand_page', 'editPricing')
                    }
                  />
                </Grid>
                {RoleManager.isAbleTo('brand_page', 'editPricing') ? null : (
                  <Grid item sm={6}>
                    <Button
                      onClick={() => handleVideoDialogOpen('imageVideo')}
                      variant="contained"
                      color="primary"
                      style={{ float: 'right' }}
                      disabled={isFreeImageVideoEnabled}
                      className='upload-chip'
                    >
                      {t('buyImageVideo')}
                    </Button>
                  </Grid>
                )}
                <Grid item sm={6}>
                  <FormCheckBox
                    name="s3IsHidden"
                    label={t('hideSection')}
                    control={control}
                    className='checkbox-section checkbox-label-14'
                    disabled={
                      !isFreeImageVideoEnabled &&
                      !RoleManager.isAbleTo('brand_page', 'editPricing')
                    }
                    checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item sm={6} xl={12} md={12} style={{ marginTop: '50px' }}>
              <FormLabel>{t('unboxingVideo')}</FormLabel>
              <Grid container spacing={4}>
                <Grid
                  item
                  sm={12}
                  xl={12}
                  style={{ marginTop: '15px' }}
                >
                  <FormLabel className='form-sub-label'>
                    {t('title')}
                    <InfoButton
                      className="float-right"
                      onClick={brandInfoTexts['brandS1TitleInfo']}
                    />
                  </FormLabel>
                  <FormTextField
                    name="s1Title"
                    label={''}
                    margin="none"
                    size="small"
                    variant="outlined"
                    className="rounded-input-20"
                    formValues={formValues}
                    rules={{
                      required:
                        (isFreeUnboxingVideoEnabled ||
                          RoleManager.isAbleTo(
                            'brand_page',
                            'editPricing'
                          )) &&
                        !s1IsHiddenWatcher &&
                        t('unboxingVideoTitleRequired'),
                      maxLength: {
                        value: 128,
                        message: t('unboxingVideoTitleLess128'),
                      },
                    }}
                    disabled={
                      !isFreeUnboxingVideoEnabled &&
                      !RoleManager.isAbleTo('brand_page', 'editPricing')
                    }
                  />
                </Grid>

                <Grid item sm={6}>
                  <Grid
                    item
                    sm={12}
                    style={{
                      paddingRight: '14px',
                    }}
                  >
                    <FormLabel className='form-sub-label'>
                    {t('media')}
                    <InfoButton
                      className="float-right"
                      onClick={brandInfoTexts['brandS1MediaInfo']}
                    />
                  </FormLabel>
                    <FormTextField
                      name="s1Media"
                      label={''}
                      margin="none"
                      size="small"
                      variant="outlined"
                      className="rounded-input-20"
                      formValues={formValues}
                      rules={{
                        maxLength: {
                          value: 256,
                          message: t('mediaLinkLess256'),
                        },
                        validate: (value) =>
                          (isFreeUnboxingVideoEnabled ||
                            RoleManager.isAbleTo(
                              'brand_page',
                              'editPricing'
                            )) &&
                          !s1IsHiddenWatcher &&
                          !validateUrl(value)
                            ? t('mediaLinkValidUrl')
                            : true,
                      }}
                      disabled={
                        !isFreeUnboxingVideoEnabled &&
                        !RoleManager.isAbleTo('brand_page', 'editPricing')
                      }
                      style={{
                        marginBottom: '20px',
                      }}
                    />
                  </Grid>

                  {RoleManager.isAbleTo('brand_page', 'editPricing') ? (
                    <Grid item sm={12}>
                      <FormCheckBox
                        name="s1ShowLearnMoreButton"
                        label={t('showCTABtn')}
                        control={control}
                        style={{ marginLeft: '-12px', marginBottom: '-12px', display: 'flex', alignItems: 'center' }}
                        disabled={
                          !isFreeUnboxingVideoEnabled &&
                          !RoleManager.isAbleTo('brand_page', 'editPricing')
                        }
                        checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                      />
                      {s1ShowLearnMoreButtonWatcher ? (
                        <Grid
                          item
                          sm={6}
                          style={{ display: 'inherit', marginTop: '10px' }}
                        >
                          <FormTextField
                            name={'s1Link'}
                            label={t('CTABtnLink')}
                            margin="none"
                            formValues={formValues}
                            size="small"
                            variant="outlined"
                            className="rounded-input-20"
                            rules={{
                              maxLength: {
                                value: 256,
                                message: t('brandLinkLess256'),
                              },
                              validate: (value) =>
                                !s1IsHiddenWatcher && !validateUrl(value)
                                  ? t('brandLinkValidUrl')
                                  : true,
                            }}
                            hidden={!s1ShowLearnMoreButtonWatcher}
                          />
                          <InfoButton
                            onClick={brandInfoTexts['brandS1LinkInfo']}
                          />
                        </Grid>
                      ) : null}
                      <FormCheckBox
                        name="s1IsHidden"
                        label={t('hideSection')}
                        control={control}
                        style={{ marginLeft: '-12px', marginTop: '20px', display: 'flex', alignItems: 'center' }}
                        disabled={
                          !isFreeUnboxingVideoEnabled &&
                          !RoleManager.isAbleTo('brand_page', 'editPricing')
                        }
                        checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                      />
                    </Grid>
                  ) : null}
                </Grid>
                <Grid item sm={6} className="s1description">
                  <FormLabel className='form-sub-label'>{t('description')}</FormLabel>{' '}
                  <InfoButton
                    onClick={brandInfoTexts['brandS1BrandDescriptionInfo']}
                    style={{ float: 'right', marginRight: '4px' }}
                  />
                  <RichTextInput
                    name="s1Description"
                    control={control}
                    validate={{
                      req: function (value) {
                        return (isFreeUnboxingVideoEnabled ||
                          RoleManager.isAbleTo(
                            'brand_page',
                            'editPricing'
                          )) &&
                          !s1IsHiddenWatcher &&
                          s1richTextField.trim().length === 0 &&
                          isRichTextEmpty(value)
                          ? t('unboxingVideoDescriptionRequired')
                          : true;
                      },
                      maxLen: (value) => {
                        const MAX_LENGTH = 750;
                        const maxLengthErrorMessage = t(
                          'unboxingVideoDescriptionLessThan',
                          { maxLength: MAX_LENGTH }
                        );
                        return value?.length > MAX_LENGTH * 2 ||
                          s1richTextField.length > MAX_LENGTH
                          ? t(maxLengthErrorMessage)
                          : true;
                      },
                    }}
                    onChangeHandler={(onChange) =>
                      (content, delta, source, editor) => {
                        onChange(content);
                        setS1RichTextField(editor.getText());
                      }}
                    theme="snow"
                    style={{ marginTop: '10px' }}
                    readOnly={
                      !isFreeUnboxingVideoEnabled &&
                      !RoleManager.isAbleTo('brand_page', 'editPricing')
                    }
                  />
                  {errors.s1Description && (
                    <ErrorMessage
                      errors={errors}
                      name="s1Description"
                      render={({ message }) => (
                        <InputError message={message} />
                      )}
                    />
                  )}
                </Grid>
              </Grid>
              <Grid sm={12}>
                {RoleManager.isAbleTo('brand_page', 'editPricing') ? null : (
                  <Grid
                    item
                    sm={12}
                    style={{ marginTop: '15px', paddingRight: '29px' }}
                  >
                    {isFreeUnboxingVideoEnabled ? (
                      <Button
                        variant="contained"
                        color="primary"
                        style={{ float: 'right' }}
                        href={`mailto:${encodeURIComponent(
                          process.env.REACT_APP_MAILTO ||
                            '<EMAIL>'
                        )}?subject=${encodeURIComponent(
                          t('subjectUnboxingMailReq', {
                            brandName: watch('name'),
                          })
                        )}&body=${encodeURIComponent(
                          t('bodyUnboxingMailReq', {
                            brandAdminName: `${
                              JSON.parse(
                                localStorage.getItem('account') || ''
                              ).firstName
                            } ${
                              JSON.parse(
                                localStorage.getItem('account') || ''
                              ).lastName
                            }`,
                          })
                        )}`}
                      >
                        {t('requestUnboxingChanges')}
                      </Button>
                    ) : (
                      <Button
                        onClick={() => handleVideoDialogOpen('unboxingVideo')}
                        variant="contained"
                        color="primary"
                        style={{ float: 'right' }}
                        disabled={isFreeUnboxingVideoEnabled}
                        className='primary-button h-37'
                      >
                        {t('buyUnboxingVideo')}
                      </Button>
                    )}
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Grid>
          {!RoleManager.isAbleTo('brand_page', 'editPricing') ? (
            <Grid container spacing={5}>
              <Grid item sm={12} xs={12} style={{ marginTop: '20px', paddingBottom: '0px' }}>
                <FormLabel>{t('reachBoost')}</FormLabel>
              </Grid>

              {reachBoost.requested || reachBoost.current ? (
                <Grid item sm={6} xs={12}>
                  <TextField
                    label={t('targetUrl')}
                    fullWidth={true}
                    value={
                      reachBoost.requested
                        ? reachBoost.requested.targetUrl
                        : reachBoost.current.targetUrl
                    }
                    InputProps={{
                      readOnly: true,
                    }}
                    size="small"
                    variant="outlined"
                    className="rounded-input-20"
                    margin="none"
                  />
                  {isActive(reachBoost.current) &&
                  checkDateBetweenDates(
                    reachBoost.current.startDate,
                    reachBoost.current.endDate
                  ) ? (
                    <Button
                      onClick={() => {
                        copyTextToClipboard(reachBoost.current.targetUrl);
                      }}
                      variant="contained"
                      color="primary"
                      style={{ float: 'right' }}
                    >
                      {t('copyLink')}
                    </Button>
                  ) : null}
                </Grid>
              ) : null}

              {reachBoost.requested || isActive(reachBoost.current) ? (
                <>
                  <Grid item sm={6} xs={12}></Grid>
                  <Grid item sm={6} xs={12}>
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      <KeyboardDatePicker
                        disableToolbar
                        fullWidth={true}
                        label={t('startDate')}
                        format={'dd-MM-yyyy'}
                        value={
                          isActive(reachBoost.current)
                            ? reachBoost.current.startDate
                            : reachBoost.requested.startDate
                        }
                        invalidDateMessage=""
                        clearable
                        variant="dialog"
                        KeyboardButtonProps={{
                          'aria-label': 'change date',
                          className: 'display-none',
                        }}
                        onChange={() => {}}
                        inputProps={{ disabled: true }}
                        InputLabelProps={{ shrink: true }}
                      />
                    </MuiPickersUtilsProvider>
                  </Grid>
                  <Grid item sm={6} xs={12}>
                    <MuiPickersUtilsProvider utils={DateFnsUtils}>
                      <KeyboardDatePicker
                        disableToolbar
                        fullWidth={true}
                        label={t('endDate')}
                        format={'dd-MM-yyyy'}
                        value={
                          isActive(reachBoost.current)
                            ? reachBoost.current.endDate
                            : reachBoost.requested.endDate
                        }
                        invalidDateMessage=""
                        clearable
                        variant="dialog"
                        KeyboardButtonProps={{
                          'aria-label': 'change date',
                          className: 'display-none',
                        }}
                        onChange={() => {}}
                        inputProps={{ disabled: true }}
                        InputLabelProps={{ shrink: true }}
                      />
                    </MuiPickersUtilsProvider>
                  </Grid>
                </>
              ) : null}
              <Grid item sm={12} xs={12}>
                <Grid item sm={12} xs={12}>
                  {!reachBoost.requested && !reachBoost.current ? (
                    <Alert severity="error" color="info" className="custom-mui-error" icon={<ErrorIcon style={{ color: '#555555' }} />}>
                      {t('reachBoostErrorMsg')}
                    </Alert>
                  ) : reachBoost.current ? (
                    checkDateBetweenDates(
                      reachBoost.current.startDate,
                      reachBoost.current.endDate
                    ) ? (
                      <Alert severity="success" className="custom-mui-success">
                        {t('reachBoostActiveMsg')}
                      </Alert>
                    ) : (
                      <Alert severity="info" className="custom-mui-error">
                        {t('reachBoostWrongTimeRangeMsg')}
                      </Alert>
                    )
                  ) : null}
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <Grid item xs={12}>
                  <Button
                    onClick={() => setIsReachBoostDialogOpen(true)}
                    variant="contained"
                    color="primary"
                    style={{ float: 'right' }}
                    className='primary-button h-37'
                  >
                    {t('requestChanges')}
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          ) : null}

          <CouponSteps control={control} errors={errors} watch={watch} />
        </BoxWrap>

        {RoleManager.isAbleTo('brand_page', 'editPricing') ? (
          <BrandPricingDetails
            brandPagePackageTemplates={brandPagePackageTemplates}
            reachBoostStatus={reachBoostStatus}
            readOnly={!RoleManager.isAbleTo('brand_page', 'editPricing')}
            control={control}
            register={register}
            setValue={setValue}
            clearErrors={clearErrors}
            errors={errors}
            watch={watch}
            openReachBoostDialog={() => setIsReachBoostDialogOpen(true)}
          />
        ) : null}

        {RoleManager.isAbleTo('brand_page', 'editBrandBanner') ? (
          <BrandBannerDetails
            formValues={formValues}
            control={control}
            register={register}
            setValue={setValue}
            clearErrors={clearErrors}
            errors={errors}
            watch={watch}
            trigger={trigger}
          />
        ) : null}

        {hasSalesforceAccountInformation &&
          RoleManager.isAbleTo('brand_page', 'viewPrivate') && (
            <BoxWrap>
              {hasSalesforceAccountInformation ? (
                <fieldset>
                  <legend>{t('account')}</legend>
                  <Grid container spacing={4}>
                    <ReadOnlyTextField
                      label={t('name')}
                      value={defaultValues?.sfName || ''}
                      type={'text'}
                    />

                    <ReadOnlyTextField
                      label={t('website')}
                      value={defaultValues?.sfWebsite || ''}
                      type={'url'}
                    />

                    <ReadOnlyTextField
                      label={t('category')}
                      value={defaultValues?.sfCategory || ''}
                      type={'text'}
                    />

                    <ReadOnlyTextField
                      label={t('interestedInWeVoucherProducts')}
                      value={defaultValues?.sfInterestedInWeVoucher || ''}
                      type={'text'}
                    />
                  </Grid>
                </fieldset>
              ) : null}

              {hasSalesforceAddressInformation ? (
                <fieldset>
                  <legend>{t('address')}</legend>
                  <Grid container spacing={4}>
                    <ReadOnlyTextField
                      label={t('billingStreet')}
                      value={defaultValues?.sfBillingStreet || ''}
                      type={'text'}
                    />

                    <ReadOnlyTextField
                      label={t('billingPostalCode')}
                      value={defaultValues?.sfBillingPostalCode || ''}
                      type={'text'}
                    />

                    <ReadOnlyTextField
                      label={t('billingCity')}
                      value={defaultValues?.sfBillingCity || ''}
                      type={'text'}
                    />

                    <ReadOnlyTextField
                      label={t('billingCountry')}
                      value={defaultValues?.sfBillingCountry || ''}
                      type={'text'}
                    />
                  </Grid>
                </fieldset>
              ) : null}

              {defaultValues?.contacts?.length > 0 ? (
                <fieldset>
                  <legend>{t('contacts')}</legend>
                  {defaultValues?.contacts?.map((sfContact, index) => (
                    <fieldset key={index}>
                      <Grid container spacing={4}>
                        <ReadOnlyTextField
                          label={t('title')}
                          value={sfContact.sfTitle || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('salutation')}
                          value={sfContact.sfSalutation || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('firstName')}
                          value={sfContact.sfFirstName || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('lastName')}
                          value={sfContact.sfLastName || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('email')}
                          value={sfContact.sfEmail || ''}
                          type={'email'}
                        />
                        <ReadOnlyTextField
                          label={t('phone')}
                          value={sfContact.sfPhone || ''}
                          type={'text'}
                        />
                        <ReadOnlyTextField
                          label={t('mobilePhone')}
                          value={sfContact.sfMobilePhone || ''}
                          type={'text'}
                        />
                        <ReadOnlyTextField
                          label={t('position')}
                          value={sfContact.sfPosition || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('id')}
                          value={sfContact.sfContactId || ''}
                          type={'text'}
                        />
                      </Grid>
                    </fieldset>
                  ))}
                </fieldset>
              ) : null}

              {defaultValues?.products?.length > 0 ? (
                <fieldset>
                  <legend>{t('products')}</legend>
                  {defaultValues?.products?.map((sfProduct, index) => (
                    <fieldset key={index}>
                      <Grid container spacing={4}>
                        <ReadOnlyTextField
                          label={t('name')}
                          value={sfProduct.sfName || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('productCode')}
                          value={sfProduct.sfProductCode || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('startDate')}
                          value={sfProduct.sfUnitPrice || ''}
                          type={'text'}
                        />

                        <ReadOnlyTextField
                          label={t('duration')}
                          value={sfProduct.sfStartDate.substr(0, 10) || ''}
                          type={'date'}
                        />

                        <ReadOnlyTextField
                          label={t('duration')}
                          value={sfProduct.sfDuration || ''}
                          type={'text'}
                        />
                      </Grid>
                    </fieldset>
                  ))}
                </fieldset>
              ) : null}
            </BoxWrap>
          )}

        {RoleManager.isAbleTo('brand_page', 'editPricing') && (
          <Grid container>
            <Grid item xs={6} md={6}>
              <Box style={{marginTop: '30px' }}>
                <FormLabel>{t('blockUser')}</FormLabel>
                <InfoButton
                    onClick={brandInfoTexts['blockUserInfo']}
                  />
                <FormCheckBox
                  control={control}
                  name="isBlocked"
                  label={t('blockUser')}
                  style={{ marginLeft: '-12px', display: 'flex', alignItems: 'center' }}
                  checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                />
              </Box>
            </Grid>

            <Grid item xs={6} md={6}>
              <Box style={{marginTop: '30px' }}>
                <FormLabel>{t('brandVisibility')}</FormLabel>
                <InfoButton
                    onClick={brandInfoTexts['empty']}
                  />
                <FormCheckBox
                  control={control}
                  name="isVisible"
                  label={t('brandVisibility')}
                  style={{ marginLeft: '-12px', display: 'flex', alignItems: 'center' }}
                  checkedIcon={<CheckBoxRoundedIcon className='checkbox-icon' />}
                />
              </Box>
            </Grid>
          </Grid>
        )}

        <Prompt
          when={isDirty && !isFormSubmitted}
          message={t(`areYouSureYouWantToLeave`)}
        />
        <Dialog
          open={dialogForVideo}
          onClose={handleVideoDialogClose}
          aria-labelledby="responsive-dialog-title"
        >
          <DialogTitle>
            {dialogVideoType === 'imageVideo'
              ? t('requestForImageVideo')
              : t('requestForUnboxingVideo')}
          </DialogTitle>
          <DialogActions>
            <Button
              autoFocus
              onClick={handleVideoDialogClose}
              color={'inherit'}
              variant="contained"
            >
              {t('no')}
            </Button>
            <Button
              onClick={() => {
                dispatch(
                  brandAccountActions.sendUpgradeRequest({
                    coupons: 0,
                    couponUpdates: 0,
                    freeImageVideo: dialogVideoType === 'imageVideo',
                    freeUnboxingVideo: dialogVideoType === 'unboxingVideo',
                  })
                );
                handleVideoDialogClose();
              }}
              color="primary"
              variant="contained"
            >
              {t('yes')}
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={delConfOpen}
          onClose={handleDelConfClose}
          aria-labelledby="responsive-dialog-title"
          className={classes.dialog}
        >
          <DialogTitle>{t('sureDeleteImage')}</DialogTitle>
          <DialogContent dividers className="pad-tb-3">
            {t('warningNoUndo')}
          </DialogContent>
          <DialogActions>
            <Button
              autoFocus
              onClick={handleDelConfClose}
              color={'inherit'}
              variant="contained"
              className="cancel-button"
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={() => {
                onMediaDelete();
                handleDelConfClose();
              }}
              color="primary"
              variant="contained"
              className="submit-button primary-button"
            >
              {t('ok')}
            </Button>
          </DialogActions>
        </Dialog>

        <Box style={{ marginTop: '30px' }}>
          <Button
            className="submit-button primary-button"
            type="submit"
            variant="contained"
            color="primary"
          >
            {hideBrandWatcher === 'ALL' &&
            !RoleManager.isAbleTo('brand_page', 'editPricing')
              ? t('saveReqApproval')
              : t('save')}
          </Button>
          <Button
            component={Link}
            to="/dashboard/brand-pages/"
            className='cancel-button'
            style={{ marginLeft: '10px' }}
          >
            {t('cancel')}
          </Button>
        </Box>
        {RoleManager.isAbleTo('brand_page', 'editPricing') && isIdValid(id) ? (
          <>
            <div className="brand-page-table-title">
              <FormLabel>{t('manageBrandUsers')}</FormLabel>
            </div>
            <BrandAdminsTable
              users={users.data || []}
              pagination={users.pagination}
              handleDelete={() => {}}
              filter={brandAdminsFilter(id)}
              onFilterChange={null}
            >
              {users.loading && <Loading />}
            </BrandAdminsTable>
          </>
        ) : null}
      </form>

      <InfoDialog
        open={!!infoHtmlText}
        textHtml={infoHtmlText}
        onCloseClick={onInfoClose}
      />

      <ReachBoostRequestDialog
        data={reachBoost}
        onCloseClick={() => setIsReachBoostDialogOpen(false)}
        onInfoClick={() => onInfoClick('reachBoostInfoHtml')}
        onOKClick={onSubmitReachBoostDialog}
        submitButtonLabel={submitReachBoostButtonLabel}
        isOpen={isReachBoostDialogOpen}
      />
    </>
  );
};
