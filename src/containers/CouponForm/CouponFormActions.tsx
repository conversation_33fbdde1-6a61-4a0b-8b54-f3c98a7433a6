import {
  <PERSON><PERSON><PERSON>_COUPON,
  <PERSON><PERSON>AR_COUPON_SUCCESS,
  CREATE_COUPON,
  CREATE_COUPON_ERROR,
  CREATE_COUPON_SUCCESS,
  EDIT_COUPON,
  EDIT_COUPON_ERROR,
  EDIT_COUPON_SUCCESS,
  GET_COUPONS,
  GET_COUPONS_ERROR,
  GET_COUPONS_SUCCESS,
  DELETE_MEDIA,
  DELETE_MEDIA_SUCCESS,
  DELETE_MEDIA_ERROR,
} from './CouponFormTypes';
import * as CouponService from 'repository/coupon';
import * as RoleManager from 'services/role';

export const deleteMedia = (mediaFile) => {
  return async (dispatch) => {
    dispatch({
      type: DELETE_MEDIA,
    });

    try {
      const response = await CouponService.deleteFile(mediaFile);
      dispatch({
        type: DELETE_MEDIA_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: DELETE_MEDIA_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const createCoupon = (coupon: any) => {
  return async (dispatch) => {
    dispatch({
      type: CREATE_COUPON,
    });

    try {
      if (RoleManager.isAbleTo('coupon', 'create')) {
        await CouponService.createCoupon(coupon);
      } else if (RoleManager.isAbleTo('brand_coupon', 'create')) {
        await CouponService.createCouponAsBrandAdmin(coupon);
      }
      dispatch({
        type: CREATE_COUPON_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: CREATE_COUPON_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const editCoupon = (id: number, coupon: any) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_COUPON,
    });

    try {
      if (RoleManager.isAbleTo('coupon', 'edit')) {
        await CouponService.editCoupon(id, coupon);
      } else if (RoleManager.isAbleTo('brand_coupon', 'edit')) {
        await CouponService.editCouponAsBrandAdmin(id, coupon);
      }
      dispatch({
        type: EDIT_COUPON_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: EDIT_COUPON_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getCoupons = (id: number) => {
  return async (dispatch) => {
    dispatch({
      type: GET_COUPONS,
    });

    try {
      let response = { data: {} };
      if (RoleManager.isAbleTo('coupon', 'view')) {
        response = await CouponService.getCoupon(id);
      } else if (RoleManager.isAbleTo('brand_coupon', 'view')) {
        response = await CouponService.getCouponAsBrandAdmin(id);
      }
      dispatch({
        type: GET_COUPONS_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: GET_COUPONS_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearCoupon = () => ({
  type: CLEAR_COUPON,
});

export const removeError = () => ({
  type: CREATE_COUPON_ERROR,
  payload: null,
});

export const clearSuccess = () => ({
  type: CLEAR_COUPON_SUCCESS,
  payload: null,
});
