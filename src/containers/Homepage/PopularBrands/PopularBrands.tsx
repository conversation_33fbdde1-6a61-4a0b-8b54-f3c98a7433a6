import React, { useState, useEffect, useRef } from 'react';
import {
  Grid,
  Button,
  TextField,
  Chip,
  Snackbar
} from '@material-ui/core';
import { Autocomplete, Alert } from '@material-ui/lab';
import DeleteIcon from '@material-ui/icons/Delete';
import SearchIcon from '@material-ui/icons/Search';
import DragIndicatorIcon from '@material-ui/icons/DragIndicator';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import * as PopularBrandsActions from './PopularBrandsActions';

interface Brand {
  id: string;
  name: string;
}

interface PopularBrandFormData {
  selectedBrand: Brand | null;
}

interface PopularBrandsProps {
  brands: Brand[];
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

const PopularBrands: React.FC<PopularBrandsProps> = ({ brands }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { setValue } = useForm<PopularBrandFormData>({
    defaultValues: {
      selectedBrand: null
    }
  });
  const [selectedBrands, setSelectedBrands] = useState<Brand[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Memoize brands to prevent infinite loops
  const memoizedBrands = React.useMemo(() => brands, [brands]);

  // Dragging functionality
  const [draggedItem, setDraggedItem] = useState<Brand | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const brandContainerRef = useRef<HTMLDivElement>(null);

  // Add CSS for better drag visual effects
  useEffect(() => {
    // Add CSS to head
    const styleSheet = document.createElement("style");
    styleSheet.textContent = `
      .brand-item {
        transition: transform 0.15s ease, opacity 0.15s ease;
      }
      .brand-item.dragging {
        opacity: 0.7;
        transform: scale(1.05);
      }
      .brand-item.placeholder {
        opacity: 0.2;
        background-color: #e0e0e0 !important;
        border: 2px dashed #aaa;
      }
      .brand-item.placeholder * {
        visibility: hidden;
      }
    `;
    document.head.appendChild(styleSheet);

    return () => {
      document.head.removeChild(styleSheet);
    };
  }, []);

  // Fetch popular brands on component mount
  useEffect(() => {
    let isMounted = true;
    let hasError = false;

    const fetchPopularBrands = async () => {
      if (hasError) return; // Prevent additional API calls if there was an error

      try {
        console.log('Fetching popular brands...');
        const response = await dispatch(PopularBrandsActions.getPopularBrands({
          page: 0,
          pageSize: 100 // Fetch a large number to ensure we get all popular brands
        }));

        console.log('Popular brands response:', response);

        if (isMounted && response && response.brandIds && memoizedBrands.length > 0) {
          console.log('Brand IDs from API:', response.brandIds);
          console.log('Available brands:', memoizedBrands);

          // Find the brands that match the IDs from the API and preserve their order
          const matchedBrands: Brand[] = [];

          // Preserve the order of brandIds from the API response
          response.brandIds.forEach((brandId: number) => {
            const matchingBrand = memoizedBrands.find(brand => Number(brand.id) === brandId);
            if (matchingBrand) {
              matchedBrands.push(matchingBrand);
            }
          });

          console.log('Matched brands in order:', matchedBrands);

          if (matchedBrands.length > 0) {
            setSelectedBrands(matchedBrands);
          } else {
            console.log('No matching brands found between API response and available brands');
          }
        } else {
          console.log('No brand IDs in response or component unmounted');
        }
      } catch (error) {
        console.error('Error fetching popular brands:', error);
        hasError = true;

        if (isMounted) {
          setSnackbar({
            open: true,
            message: 'Failed to load popular brands',
            severity: 'error'
          });
        }
      }
    };

    // Only fetch if we have brands available
    if (brands.length > 0) {
      fetchPopularBrands();
    } else {
      console.log('No brands available yet, skipping popular brands fetch');
    }

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [dispatch, memoizedBrands.length, memoizedBrands]); // Include memoizedBrands as a dependency, but it's memoized so it won't cause infinite loops

  const handleAddBrand = (brand: Brand | null) => {
    if (brand && !selectedBrands.some(b => b.id === brand.id)) {
      setSelectedBrands([...selectedBrands, brand]);
    }
    setValue('selectedBrand', null);
    setInputValue('');
  };

  const handleRemoveBrand = (id: string) => {
    setSelectedBrands(selectedBrands.filter(brand => brand.id !== id));
  };

  // Track the target index for placeholder
  const [targetIndex, setTargetIndex] = useState<number | null>(null);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, brand: Brand, index: number) => {
    // Set the dragged item and its original index
    setDraggedItem(brand);
    setDraggedIndex(index);

    // Make the element visually draggable with a ghost image
    if (e.dataTransfer) {
      e.dataTransfer.effectAllowed = 'move';
      // Create a custom drag image - optional
      const dragImage = e.currentTarget.cloneNode(true) as HTMLDivElement;
      dragImage.style.opacity = '0.7';
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px';
      document.body.appendChild(dragImage);
      e.dataTransfer.setDragImage(dragImage, 20, 20);

      // Clean up after drag image is captured
      setTimeout(() => {
        document.body.removeChild(dragImage);
      }, 0);
    }

    // Add dragging class to the dragged element
    e.currentTarget.classList.add('dragging');
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedItem === null) return;

    // Don't do anything if hovering over the same item being dragged
    if (index === draggedIndex) return;

    // Set the target index for the placeholder
    setTargetIndex(index);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedItem === null) return;

    // Don't do anything if hovering over the same item being dragged
    if (index === draggedIndex) return;

    // Set the target index for the placeholder if not already set
    if (targetIndex !== index) {
      setTargetIndex(index);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    // Only clear the target if we're leaving a child element
    if (e.currentTarget.contains(e.relatedTarget as Node)) {
      return;
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedItem === null) return;

    // Don't do anything if dropping on the same item being dragged
    if (index === draggedIndex) return;

    // Create new array with updated order
    const newBrands = [...selectedBrands];
    const draggedItemContent = newBrands[draggedIndex];

    // Remove the item from its original position
    newBrands.splice(draggedIndex, 1);
    // Insert at the new position
    newBrands.splice(index, 0, draggedItemContent);

    // Update the state with new order
    setSelectedBrands(newBrands);

    // Clear the target index
    setTargetIndex(null);
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    // If we have a target index and haven't dropped yet, update the order
    if (targetIndex !== null && draggedIndex !== null) {
      const newBrands = [...selectedBrands];
      const draggedItemContent = newBrands[draggedIndex];

      // Remove the item from its original position
      newBrands.splice(draggedIndex, 1);
      // Insert at the new position
      newBrands.splice(targetIndex, 0, draggedItemContent);

      // Update the state with new order
      setSelectedBrands(newBrands);
    }

    // Reset drag state
    setDraggedItem(null);
    setDraggedIndex(null);
    setTargetIndex(null);

    // Remove dragging class from all elements
    document.querySelectorAll('.brand-item').forEach(el => {
      el.classList.remove('dragging');
      el.classList.remove('placeholder');
    });

    // Log the new order for debugging
    console.log('New order of brand IDs:', selectedBrands.map(brand => brand.id));
  };

  const handleSave = async () => {
    // Remove validation that prevents saving with no brands
    // Allow saving with empty selection

    setLoading(true);
    try {
      // Extract brand IDs from selected brands
      const brandIds = selectedBrands.map(brand => Number(brand.id));
      console.log('Saving popular brands with IDs:', brandIds);

      // Call API to update popular brands
      const result = await dispatch(PopularBrandsActions.updatePopularBrands({ brandIds }));
      console.log('Save result:', result);

      setSnackbar({
        open: true,
        message: 'Popular brands saved successfully!',
        severity: 'success'
      });

      // Refresh the data after saving
      console.log('Refreshing data after save...');
      const response = await dispatch(PopularBrandsActions.getPopularBrands({
        page: 0,
        pageSize: 100
      }));

      console.log('Refresh response:', response);

      if (response && response.brandIds && memoizedBrands.length > 0) {
        // Find the brands that match the IDs from the API and preserve their order
        const matchedBrands: Brand[] = [];

        // Preserve the order of brandIds from the API response
        response.brandIds.forEach((brandId: number) => {
          const matchingBrand = memoizedBrands.find(brand => Number(brand.id) === brandId);
          if (matchingBrand) {
            matchedBrands.push(matchingBrand);
          }
        });

        console.log('Updated matched brands after save in order:', matchedBrands);

        // Only update state if there's a change to prevent unnecessary re-renders
        if (JSON.stringify(matchedBrands.map(brand => brand.id)) !== JSON.stringify(selectedBrands.map(brand => brand.id))) {
          setSelectedBrands(matchedBrands);
        }
      }
    } catch (error) {
      console.error('Error saving popular brands:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save popular brands',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const availableBrands = brands.filter(
    (brand: Brand) => !selectedBrands.some(selectedBrand => selectedBrand.id === brand.id)
  );

  return (
    <div className="popular-brands-section">
      <div className="section-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 className="section-title" style={{ margin: 0 }}>{t('popularBrand')}</h2>
        <Button
          variant="contained"
          className="save-button"
          onClick={handleSave}
          disabled={loading}
        >
          {loading ? t('saving') : t('save').toUpperCase()}
        </Button>
      </div>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <div style={{ marginBottom: '8px' }}>
            <Autocomplete
              options={availableBrands}
              getOptionLabel={(option: Brand) => option ? option.name : ''}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  variant="outlined"
                  size="small"
                  placeholder={t('search')}
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <div style={{ paddingLeft: '8px', paddingRight: '16px' }}>
                        <SearchIcon style={{ color: '#757575' }} />
                      </div>
                    ),
                    style: {
                      borderRadius: '12px',
                      fontSize: 11,
                      height: 36
                    }
                  }}
                  className="search-input"
                />
              )}
              onChange={(_event, newValue) => handleAddBrand(newValue)}
              inputValue={inputValue}
              onInputChange={(_event, newInputValue) => {
                setInputValue(newInputValue);
              }}
              value={null}
              ListboxProps={{
                style: {
                  maxHeight: 200,
                  fontSize: 11,
                  overflowY: 'auto',
                  borderRadius: 8,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  zIndex: 99999
                }
              }}
              style={{ zIndex: 10000 }}
            />
          </div>

          <div
            className="brand-container"
            ref={brandContainerRef}
            style={{
              padding: '16px',
              border: '1px solid #707070',
              borderRadius: '12px',
              minHeight: '100px',
              position: 'relative',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))',
              gridGap: '12px',
              alignContent: 'flex-start',
              zIndex: 1
            }}
          >
            {selectedBrands.length === 0 && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                color: '#aaa',
                fontSize: '14px'
              }}>
                {t('dragAndDropBrandsHere') || 'Drag and drop brands here'}
              </div>
            )}

            {selectedBrands.map((brand, index) => (
              <div
                key={brand.id}
                className={`brand-item ${draggedIndex === index ? 'dragging' : ''} ${targetIndex === index && draggedIndex !== index ? 'placeholder' : ''}`}
                draggable={true}
                onDragStart={(e) => handleDragStart(e, brand, index)}
                onDragEnter={(e) => handleDragEnter(e, index)}
                onDragOver={(e) => handleDragOver(e, index)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, index)}
                onDragEnd={handleDragEnd}
                style={{
                  userSelect: 'none',
                  background: draggedIndex === index ? '#f0f0f0' : '#f5f5f5',
                  borderRadius: '15px',
                  boxShadow: draggedIndex === index ? '0 2px 5px rgba(0,0,0,0.2)' : 'none',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '4px',
                  zIndex: draggedIndex === index ? 9999 : 1,
                  minHeight: '38px',
                  opacity: draggedIndex === index ? 0.7 : 1,
                  cursor: 'grab',
                  border: targetIndex === index && draggedIndex !== index ? '2px dashed #aaa' : 'none'
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0 8px 0 4px',
                    height: '100%'
                  }}
                >
                  <DragIndicatorIcon style={{ fontSize: 16, color: '#666' }} />
                </div>

                <Chip
                  label={brand.name}
                  onDelete={() => handleRemoveBrand(brand.id)}
                  className="brand"
                  size="small"
                  style={{
                    borderRadius: '15px',
                    fontSize: 11,
                    height: 30,
                    backgroundColor: 'transparent',
                    display: 'flex',
                    alignItems: 'center',
                    paddingBottom: '2px',
                    paddingTop: '2px',
                    paddingLeft: 0,
                    flexGrow: 1,
                    width: 'calc(100% - 24px)' // Account for the drag handle
                  }}
                  deleteIcon={
                    <DeleteIcon
                      style={{
                        fontSize: 16,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%'
                      }}
                    />
                  }
                />
              </div>
            ))}
          </div>
        </Grid>
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default PopularBrands;
