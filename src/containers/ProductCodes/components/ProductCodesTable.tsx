import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TablePagination,
  TableRow,
  TableHead,
} from '@material-ui/core';
import { useTranslation } from 'react-i18next';

import { Sorter } from 'components/Sorter/Sorter';

export const ProductCodesTable = ({
  productSubscriptionCodes,
  pagination,
  onFilterChange,
  filter,
  children,
}) => {
  const { t }: { t: any } = useTranslation();
  const [page, setPage] = React.useState(filter.page);
  const [rowsPerPage, setRowsPerPage] = React.useState(filter.pageSize);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);

    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };
  return (
    <TableContainer>
      {children}
      <Table aria-label="custom pagination table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter
                label={t('code')}
                handleSort={(direction) =>
                  onFilterChange({ sort: { field: 'code', dir: direction } })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('uploadedOn')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'timeAdded', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              {' '}
              <Sorter
                label={t('numberOfUsagesLeft')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'numberOfLeftUsages', dir: direction },
                  })
                }
              />
            </TableCell>
          </TableRow>
        </TableHead>

        <TableBody>
          {productSubscriptionCodes.map((row) => (
            <TableRow key={row.id}>
              <TableCell scope="row">{row.code}</TableCell>
              <TableCell scope="row">
                {new Date(row.timeAdded).toLocaleDateString()}
              </TableCell>
              <TableCell scope="row">{row.numberOfLeftUsages}</TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={pagination || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  );
};
