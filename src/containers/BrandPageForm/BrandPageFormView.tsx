import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router';
import { Redirect } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

import { AdminForm } from './Forms';
import { PageTitle, Loading, InfoDialog } from 'components';
import * as brandPageActions from './BrandPageFormActions';
import { useInfoText } from 'utils/useInfoText';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import * as RoleManager from 'services/role';

import './BrandPageForm.scss';

const isIdValid = (id) => id !== 'create';

export const BrandPageFormView = ({ loading }) => {
  const { t }: { t: any } = useTranslation();
  const { id } = useParams();
  const { addMessage } = useAPIError();

  const [infoHtmlText, setInfoHtmlText] = useState<string | undefined>();
  const brandInfoTexts = useInfoText(
    [
      'brandNameInfo',
      'brandWebsiteLinkInfo',
      'brandSlugInfo',
      'brandWebsiteLinkTitleInfo',
      'brandMetaTitleInfo',
      'brandMetaDescriptionInfo',
      'brandImageInfo',
      'brandLogoInfo',
      'brandIsPopularInfo',
      'brandHideInfo',
      'brandS1TitleInfo',
      'brandS1MediaInfo',
      'brandS1LinkInfo,',
      'brandS1BrandDescriptionInfo',
      'brandS2BrandHeadlineInfo',
      'brandS2BrandDescriptionInfo',
      'brands3MediaLinkInfo',
      'couponDiscountTypeAmountInfo',
      'couponDiscountTypePercentageInfo',
      'couponDiscountTypeFreeInfo',
      'couponDiscountCodeTypeGenericInfo',
      'couponDiscountCodeTypeUniqueInfo',
      'couponDiscountCodeTypeStationaryInfo',
      'couponAvailableForNewCustomersInfo',
      'couponImageInfo',
      'blockUserInfo',
      'expertModeText'
    ],
    setInfoHtmlText
  );
  const onInfoClose = useCallback(
    () => setInfoHtmlText(undefined),
    [setInfoHtmlText]
  );

  const activeBrandPage = useSelector(
    (state: any) => state.brandPageForm.activeBrandPage
  );
  const brandPageError = useSelector((state) => state.brandPageForm.error);
  const brandPagePackageTemplates = useSelector(
    (state: any) => state.brandPageForm.brandPackageTemplates
  );
  const brandPackagesError = useSelector(
    (state: any) => state.brandPageForm.brandPackageError
  );

  useEffect(() => {
    if (isIdValid(id)) {
      brandPageActions.getBrandPage(
        id,
        RoleManager.isAbleTo('brand_pages', 'view')
      );
    }
  }, [id]);

  useEffect(() => {
    if (brandPackagesError) {
      addMessage(t(brandPackagesError.errorCode), 'error');
    }
  }, [brandPackagesError]);

  return (
    <div className="user-create-page">
      <PageTitle
        title={isIdValid(id) ? t('editBrand') : t('createBrandHeadline')}
      />
      {loading && <Loading />}
      {brandPageError && brandPageError.errorCode === 'brandNotFound' ? (
        <Redirect to="/dashboard/404" />
      ) : (
        (activeBrandPage || !isIdValid(id)) && (
          <AdminForm
            brandPage={activeBrandPage}
            brandPagePackageTemplates={brandPagePackageTemplates}
            brandInfoTexts={brandInfoTexts}
          />
        )
      )}

      <InfoDialog
        open={!!infoHtmlText}
        textHtml={infoHtmlText}
        onCloseClick={onInfoClose}
      />
    </div>
  );
};
