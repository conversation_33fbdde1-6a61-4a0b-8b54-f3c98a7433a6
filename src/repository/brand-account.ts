import API from 'services/api';

export type HistoryActionType = 'ADD' | 'USE' | 'REQUEST';
export type HistoryResourceType =
  | 'COUPON_USAGE'
  | 'COUPON_UPDATE'
  | 'IMAGE_VIDEO'
  | 'UNBOXING_VIDEO'
  | 'REACH_BOOST';

export interface HistoryItem {
  id: string;
  lastModified: string;
  type: HistoryActionType;
  action: HistoryResourceType;
  amount: number;
}

export interface BrandUser {
  firstName: string;
  surname: string;
  username: string;
  role: string;
}

export interface BrandAccount {
  templateName: string;
  coupons: number;
  usedCoupons: number;
  couponUpdates: number;
  freeImageVideo: boolean;
  freeUnboxingVideo: boolean;
  history: HistoryItem[];
  pricePerCoupon: number;
  pricePerCouponUpdate: number;
  pricePerImageVideo: number;
  pricePerUnboxingVideo: number;
  brandUsers: BrandUser[];
  reachBoost: boolean;
  reachBoosts: ReachBoost[];
}

export interface AccountUpgradeRequest {
  coupons: number;
  couponUpdates: number;
  freeImageVideo: boolean;
  freeUnboxingVideo: boolean;
}

export interface ReachBoost {
  status: string;
  startDate: string;
  endDate: string;
  targetUrl: string;
}

export interface ReachBoostUpgradeRequest {
  status: boolean;
  startDate: string;
  endDate: string;
}

export const getBrandHistoryAsBrandAdmin = async () => {
  // expected error messages from BE
  // - brandForUserNotFound
  // - brandPackageForBrandNotFound
  return await API.get('/brand-admin/page/history');
};

export const getBrandUsersAsBrandAdmin = async () => {
  // expected error messages from BE
  // - brandForUserNotFound
  return await API.get('/brand-admin/page/users');
};

export const getBrandPackageForBrandAdmin = async () => {
  // expected error messages from BE
  // - brandForUserNotFound
  // - brandPackageForBrandNotFound
  return await API.get('/brand-admin/page/package');
};

export const sendUpgradeRequest = async (upgradeRequestData) => {
  // expected error messages from BE
  // - upgradeRequestNotValid
  // - brandForUserNotFound
  // - brandPackageForBrandNotFound
  return await API.post(
    '/brand-admin/page/package/upgrade',
    upgradeRequestData
  );
};

export const sendReachBoostUpgradeRequest = async (reachBoostRequestData) => {
  // expected error messages from BE
  // upgradeRequestNotValid
  // brandPageHidden
  return await API.post(
    '/brand-admin/page/reach-boost/upgrade',
    reachBoostRequestData
  );
};
