export const validateUrl = (urlToTest: string) => {
  // console.log('urlToTest', urlToTest)
  // const pattern = new RegExp('^(https?:\\/\\/)?'+ // protocol
  //   '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|'+ // domain name
  //   '((\\d{1,3}\\.){3}\\d{1,3}))'+ // ip (v4) address
  //   '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*'+ //port
  //   '(\\?[;&amp;a-z\\d%_.~+=-]*)?'+ // query string
  //   '(\\#[-a-z\\d_]*)?$','i');
  // return pattern.test(urlToTest);
  let url;

  try {
    url = new URL(urlToTest);
  } catch (_) {
    return false;
  }

  return url.protocol === "http:" || url.protocol === "https:";
}


export const validateSlug = (stringToTest: string) =>  {
  const pattern = /^[a-zA-Z0-9-_]+$/;
  return pattern.test(stringToTest);
}