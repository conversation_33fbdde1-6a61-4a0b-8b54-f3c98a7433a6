import * as TextBannerTypes from './TextBannerTypes';
import { TextBanner } from 'repository/textBanner';

interface State {
    loading: boolean;
    error: any;
    textBanner: TextBanner | null;
}

const initialState: State = {
    loading: false,
    error: null,
    textBanner: null
};

export default function reducer(state = initialState, action: any) {
    switch (action.type) {
        case TextBannerTypes.GET_TEXT_BANNER:
        case TextBannerTypes.UPDATE_TEXT_BANNER:
            return {
                ...state,
                loading: true,
                error: null,
            };

        case TextBannerTypes.GET_TEXT_BANNER_SUCCESS:
            return {
                ...state,
                loading: false,
                textBanner: action.payload,
            };

        case TextBannerTypes.UPDATE_TEXT_BANNER_SUCCESS:
            return {
                ...state,
                loading: false,
                textBanner: action.payload,
            };

        case TextBannerTypes.GET_TEXT_BANNER_ERROR:
        case TextBannerTypes.UPDATE_TEXT_BANNER_ERROR:
            return {
                ...state,
                loading: false,
                error: action.payload,
            };

        default:
            return state;
    }
}
