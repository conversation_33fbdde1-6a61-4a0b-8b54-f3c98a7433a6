import { makeStyles, TextField } from '@material-ui/core';
import React, { ChangeEvent, useCallback } from 'react';

const useStyles = makeStyles({
  root: {
    width: '100%',
  },
});

interface Props {
  value: number;
  maxValue?: number;
  onChange(value: number): void;
}

const QuantityRequestInput = ({ value, maxValue, onChange }: Props) => {
  const classes = useStyles();

  const _onChange = useCallback(
    (evt: ChangeEvent<HTMLInputElement>) =>
      onChange(parseInt(evt.currentTarget.value)),
    [onChange]
  );

  return (
    <TextField
      className={classes.root}
      type="number"
      variant="outlined"
      value={value}
      InputLabelProps={{
        shrink: true,
      }}
      inputProps={{
        min: 0,
        max: maxValue,
      }}
      onChange={_onChange}
    />
  );
};

export default QuantityRequestInput;
