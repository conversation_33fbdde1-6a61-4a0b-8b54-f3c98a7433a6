import React from 'react';

import { Link } from 'react-router-dom';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableFooter from '@material-ui/core/TableFooter';
import TablePagination from '@material-ui/core/TablePagination';
import TableRow from '@material-ui/core/TableRow';
import TableHead from '@material-ui/core/TableHead';

import IconButton from '@material-ui/core/IconButton';
import EditIcon from '@material-ui/icons/Edit';
import PublishIcon from '@material-ui/icons/Publish';
import DeleteIcon from '@material-ui/icons/Delete';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import { Button, DialogContent } from '@material-ui/core';
import DialogTitle from '@material-ui/core/DialogTitle';
import Switch from '@material-ui/core/Switch';
import Select from '@material-ui/core/Select';
import MenuItem from '@material-ui/core/MenuItem';
import { useHistory } from 'react-router-dom';

import { Sorter } from 'components/Sorter/Sorter';
import { useEffect } from 'react';
import * as productRepository from 'repository/product';
import { useTranslation } from 'react-i18next';

enum STATUS {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'DEACTIVATED',
}

const TRANSLATION_KEYS_BY_STATUS: Record<STATUS, string> = {
  DRAFT: 'productStatusDraft',
  ACTIVE: 'productStatusActive',
  DEACTIVATED: 'productStatusDeactivated',
};

export const ProductTable = ({
  products,
  count,
  filter,
  onFilterChange,
  onDeleteProduct,
  onMarkProduct,
  onSetProductFeatured,
  children,
}) => {
  const { t }: { t: any } = useTranslation();
  const [page, setPage] = React.useState(filter.page);
  const [selectedProduct, setSelectedProduct] = React.useState();
  const [rowsPerPage, setRowsPerPage] = React.useState(filter.pageSize);
  const [freePositions, setFreePositions] = React.useState<number[]>([]);

  const fetchFreePositions = async () => {
    try {
      const response = await productRepository.getFreePositions();
      setFreePositions([...response?.data?.positions]);
    } catch (error) {
      console.log(t(error?.errorCode));
    }
  };

  useEffect(() => {
    fetchFreePositions();
  }, [products]);

  const history = useHistory();

  const [delConfOpen, setDelConfOpen] = React.useState(false);

  const handleDelConfClose = () => {
    setDelConfOpen(false);
  };

  const handleDelConfOpen = (id) => {
    setSelectedProduct(id);
    setDelConfOpen(true);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);

    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };

  const handleSort = (fieldName) => (direction) =>
    onFilterChange({ sort: { field: fieldName, dir: direction } });

  if (!products.length) {
    return <h2>{t('noResultsFound')}</h2>;
  }

  const handleSetFeatured = (product, position) => {
    onSetProductFeatured({
      id: product?.id,
      position: position,
    });
  };

  const onToggleFeatured = (
    event: React.ChangeEvent<HTMLInputElement>,
    id: number
  ) => {
    if (!event.target.checked) {
      handleSetFeatured({ id }, null);
    } else {
      onMarkProduct(id);
    }
  };

  return (
    <TableContainer>
      {children}
      <Table aria-label="custom pagination table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter label={t('id')} handleSort={handleSort('id')} />
            </TableCell>
            <TableCell>
              <Sorter label={t('title')} handleSort={handleSort('title')} />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('initialPeriodDuration')}
                handleSort={handleSort('duration')}
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('initialAmount')}
                handleSort={handleSort('initialAmount')}
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('recurring')}
                handleSort={handleSort('recurring')}
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('recurringAmount')}
                handleSort={handleSort('recurringAmount')}
              />
            </TableCell>
            <TableCell>{t('status')}</TableCell>
            <TableCell align="center">
              {' '}
              <Sorter
                label={t('currentlySubscribed')}
                handleSort={handleSort('numberOfSubscribers')}
              />
            </TableCell>
            <TableCell align="center">{t('actions')}</TableCell>
            <TableCell align="center">{t('featured')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {products.map((product) => (
            <TableRow key={`${page}/${product.id}/${product.featured}`}>
              <TableCell>{product.id}</TableCell>
              <TableCell scope="row">
                {product.position ? <b>{product.title}</b> : product.title}
              </TableCell>
              <TableCell align="center">{product.duration}</TableCell>
              <TableCell align="center">{product.initialAmount}</TableCell>
              <TableCell align="center">
                {t(product.recurring ? 'yes' : 'no')}
              </TableCell>
              <TableCell align="center">
                {product.recurringAmount.toFixed(2)}
              </TableCell>
              <TableCell>
                {t(TRANSLATION_KEYS_BY_STATUS[product.status])}
              </TableCell>
              <TableCell align="center">
                {product.numberOfSubscribers || 0}
              </TableCell>

              <TableCell>
                <div className="d-inline-flex">
                  <IconButton
                    aria-label="edit"
                    color="secondary"
                    onClick={() =>
                      history.push(`/dashboard/memberships/${product.id}`)
                    }
                  >
                    <EditIcon />
                  </IconButton>

                  {product.status !== STATUS.INACTIVE ? (
                    <IconButton
                      aria-label="delete"
                      color="secondary"
                      onClick={() => handleDelConfOpen(product.id)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  ) : null}

                  {product.status !== STATUS.INACTIVE ? (
                    <Link to={`/dashboard/memberships/codes/${product.id}`}>
                      <IconButton aria-label="upload" color="default">
                        <PublishIcon />
                      </IconButton>
                    </Link>
                  ) : null}
                </div>
              </TableCell>

              <TableCell>
                <div className="d-inline-flex">
                  <Switch
                    checked={product.featured}
                    onChange={(event) => onToggleFeatured(event, product.id)}
                    disabled={
                      product.status !== 'ACTIVE' ||
                      (!product.featured && !freePositions.length)
                    }
                    color="primary"
                    inputProps={{ 'aria-label': 'secondary checkbox' }}
                  />
                  {product.featured ? (
                    <Select
                      value={product.position}
                      onChange={(event) =>
                        handleSetFeatured(product, event.target.value)
                      }
                      displayEmpty
                      inputProps={{ 'aria-label': 'Position' }}
                    >
                      {freePositions.map((position) => {
                        return (
                          <MenuItem key={position} value={position}>
                            {position}
                          </MenuItem>
                        );
                      })}
                      {product.position && (
                        <MenuItem value={product.position}>
                          {product.position}
                        </MenuItem>
                      )}
                    </Select>
                  ) : null}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={count || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>

      <Dialog
        open={delConfOpen}
        onClose={handleDelConfClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteProduct')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('warningNoUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleDelConfClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              onDeleteProduct(selectedProduct);
              handleDelConfClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>
    </TableContainer>
  );
};
