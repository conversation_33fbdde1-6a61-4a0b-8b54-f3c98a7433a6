import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import TrafficoLogo from '../../assets/images/traffico_logo.jpeg';

import './NotFound.scss';

export default () => {
  const { t }: { t: any } = useTranslation();
  return (
    <div className="page-not-found">
      <div className="box">
        <img src={TrafficoLogo} alt='traffico_logo' />

        <h1>{t('error404')}</h1>
        <h3>{t('pageNotFound')}</h3>
        <Link to="/">&larr; {t('backToHome')}</Link>
      </div>
    </div>
  );
};
