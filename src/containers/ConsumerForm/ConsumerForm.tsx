import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { useParams } from 'react-router';
import { ConsumerFormView } from './ConsumerFormView';
import * as consumerFormActions from './ConsumerFormActions';
import {
  getProduct,
  clearProduct,
} from 'containers/ProductForm/ProductFormActions';
import { Loading } from 'components';

export default (props) => {
  const dispatch = useDispatch();
  const consumerForm = useSelector((state: any) => state.consumerForm);
  const productForm = useSelector((state: any) => state.productsForm);

  let { id } = useParams();

  const getConsumer = () => {
    dispatch(consumerFormActions.getConsumer(id));
  };
  const changeSubscription = (packageId) => {
    dispatch(
      consumerFormActions.changeSubscription(Number.parseInt(id), packageId)
    );
  };

  useEffect(() => {
    if (id !== 'create') {
      getConsumer();
    }
  }, [dispatch, id]);

  useEffect(() => {
    if (id !== 'create') {
      if (consumerForm?.data?.productPackageId) {
        dispatch(getProduct(consumerForm.data.productPackageId));
      }
    }
    return () => {
      dispatch(clearProduct());
    };
  }, [dispatch, consumerForm?.data]);

  if (
    consumerForm.error &&
    consumerForm.error.errorCode === 'consumerNotFound'
  ) {
    return <Redirect to="/dashboard/customers/404" />;
  } else {
    return consumerForm.data ? (
      <ConsumerFormView
        data={{
          ...consumerForm.data,
          consumerId: id,
          productPackage: productForm?.activeProduct,
        }}
        loading={consumerForm?.loading || productForm?.loading}
        getConsumer={getConsumer}
        changeSubscription={changeSubscription}
      />
    ) : (
      <Loading />
    );
  }
};
