import React, { useEffect } from 'react';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableFooter from '@material-ui/core/TableFooter';
import TablePagination from '@material-ui/core/TablePagination';
import TableRow from '@material-ui/core/TableRow';
import TableHead from '@material-ui/core/TableHead';

import IconButton from '@material-ui/core/IconButton';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import { Sorter } from 'components/Sorter/Sorter';
import { useParams } from 'react-router';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  DialogActions,
} from '@material-ui/core';

import { roleNames } from 'utils/constants';

export const BrandAdminsTable = ({
  users,
  pagination,
  handleDelete,
  filter,
  onFilterChange,
  children,
}) => {
  const [page, setPage] = React.useState(filter.page);
  const [selectedUser, setSelectedUser] = React.useState();
  const [rowsPerPage, setRowsPerPage] = React.useState(filter.pageSize);
  const [open, setOpen] = React.useState(false);
  const { t }: { t: any } = useTranslation();
  const { id } = useParams();
  const history = useHistory();

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);

    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };

  const handleClickOpen = (id) => {
    setSelectedUser(id);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleEditClicked = (userId) => {
    localStorage.setItem(
      'redirectAfterBrandUserSaveTo',
      `/dashboard/brand-pages/${id}`
    );
    history.push(`/dashboard/brand-user/${userId}`);
  };

  return (
    <TableContainer>
      {children}
      <Table
        aria-label="custom pagination table"
        style={{ backgroundColor: 'white' }}
      >
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter
                label={t('name')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'firstName', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('email')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'username', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>{t('role')}</TableCell>
            <TableCell>{t('actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {users.map((row) => (
            <TableRow key={row.id}>
              <TableCell scope="row">
                {row.firstName + ' ' + row.surname}
              </TableCell>
              <TableCell scope="row">{row.username}</TableCell>
              <TableCell component="th" scope="row">
                {roleNames[row.roleId]}
              </TableCell>
              <TableCell component="th" scope="row">
                <IconButton
                  aria-label="edit"
                  color="default"
                  onClick={() => handleEditClicked(row.id)}
                >
                  <EditIcon />
                </IconButton>

                <IconButton
                  aria-label="delete"
                  disabled={true}
                  onClick={() => handleClickOpen(row.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={pagination?.last || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>

      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteUser')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('warningNoUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              handleDelete(selectedUser);
              handleClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>
    </TableContainer>
  );
};
