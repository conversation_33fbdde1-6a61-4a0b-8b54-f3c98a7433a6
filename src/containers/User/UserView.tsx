import React, { useState, useCallback } from 'react';
import { Grid, Button, TextField } from '@material-ui/core';
import { PageTitle, BoxWrap } from 'components';
import SearchIcon from '@material-ui/icons/Search';
import { debounce } from 'utils/debouncer';
import { useTranslation } from 'react-i18next';

import './User.scss';
import { Link } from 'react-router-dom';

export const UserView = ({ onFilterChange, filter, children }) => {
  const { t }: { t: any } = useTranslation();
  const [userQuery, setUserQuery] = useState('');
  const delayedQuery = useCallback(
    debounce((q) => {
      onFilterChange({
        ...filter,
        search: q,
      });
    }, 500),
    [filter]
  );

  const onChange = (e) => {
    setUserQuery(e.target.value);
    delayedQuery(e.target.value);
  };

  return (
    <div className="user-page">
      <PageTitle title={t('userManagement')}>
        <Button
          component={Link}
          to="/dashboard/user/create"
          color="primary"
          variant="contained"
        >
          {t('createUser')}
        </Button>
      </PageTitle>
      <BoxWrap>
        <BoxWrap.Toolbar>
          <Grid container spacing={1} alignItems="flex-end">
            <Grid item>
              <SearchIcon />
            </Grid>
            <Grid item>
              <TextField
                id="input-with-icon-grid"
                label={t('searchUsers')}
                onChange={onChange}
                value={userQuery}
              />
            </Grid>
          </Grid>
        </BoxWrap.Toolbar>
        {children}
      </BoxWrap>
    </div>
  );
};
