import React from 'react';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableFooter from '@material-ui/core/TableFooter';
import TablePagination from '@material-ui/core/TablePagination';
import TableRow from '@material-ui/core/TableRow';
import TableHead from '@material-ui/core/TableHead';

import IconButton from '@material-ui/core/IconButton';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import CheckIcon from '@material-ui/icons/Check';
import ClearIcon from '@material-ui/icons/Clear';
import { Link } from 'react-router-dom';
import { Sorter } from '../../../components/Sorter/Sorter';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import { Button, DialogContent } from '@material-ui/core';
import DialogTitle from '@material-ui/core/DialogTitle';
import { useTranslation } from 'react-i18next';

export const CategoriesTable = ({
  categories,
  pagination,
  onFilterChange,
  filter,
  handleDelete,
  children,
}) => {
  const { t }: { t: any } = useTranslation();
  const [page, setPage] = React.useState(filter.page);
  const [selectedCategory, setSelectecCategory] = React.useState();
  const [rowsPerPage, setRowsPerPage] = React.useState(filter.pageSize);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);

    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };

  const [open, setOpen] = React.useState(false);

  const handleClickOpen = (id) => {
    setSelectecCategory(id);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  if (categories.length === 0) return <h2>{t('noResultsFound')}</h2>;
  return (
    <TableContainer>
      {children}
      <Table aria-label="custom pagination table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter
                label={t('id')}
                handleSort={(direction) =>
                  onFilterChange({ sort: { field: 'id', dir: direction } })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('name')}
                handleSort={(direction) =>
                  onFilterChange({ sort: { field: 'name', dir: direction } })
                }
              />
            </TableCell>
            <TableCell>{t('slug')}</TableCell>
            <TableCell>
              {' '}
              <Sorter
                label={t('isPopular')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'isPopular', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>{t('actions')}</TableCell>
          </TableRow>
        </TableHead>

        <TableBody>
          {categories.map((row) => (
            <TableRow key={row.id}>
              <TableCell scope="row">{row.id}</TableCell>
              <TableCell scope="row">{row.name}</TableCell>
              <TableCell scope="row">{row.slug || '-'}</TableCell>
              <TableCell component="th" scope="row">
                {row.isPopular ? (
                  <CheckIcon></CheckIcon>
                ) : (
                  <ClearIcon></ClearIcon>
                )}
              </TableCell>
              <TableCell component="th" scope="row">
                <Link to={`/dashboard/category/${row.id}`}>
                  <IconButton aria-label="edit" color="default">
                    <EditIcon />
                  </IconButton>
                </Link>
                <IconButton
                  aria-label="delete"
                  color="secondary"
                  onClick={() => handleClickOpen(row.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={pagination?.last || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>

      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="responsive-dialog-title"
      >
        <DialogTitle>{t('sureDeleteCategory')}</DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('warningNoUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              handleDelete(selectedCategory);
              handleClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>
    </TableContainer>
  );
};
