import * as TextBannerTypes from './TextBannerTypes';
import * as TextBannerAP<PERSON> from 'repository/textBanner';

interface ImageChange {
  file: File;
  oldPath?: string;
}

export const uploadImage = (file: File, textBannerId?: number) => {
  return async (dispatch: any) => {
    dispatch({
      type: TextBannerTypes.UPLOAD_IMAGE,
    });

    try {
      const response = await TextBannerAPI.uploadFile(file, textBannerId);

      dispatch({
        type: TextBannerTypes.UPLOAD_IMAGE_SUCCESS,
        payload: response.data,
      });

      return response.data.fileName;
    } catch (error) {
      dispatch({
        type: TextBannerTypes.UPLOAD_IMAGE_ERROR,
        payload: error?.response?.data,
      });
      throw error;
    }
  };
};



export const getTextBanner = () => async (dispatch: any) => {
    dispatch({ type: TextBannerTypes.GET_TEXT_BANNER });
    try {
        const response = await TextBannerAPI.getTextBanner();
        console.log('TextBanner API response:', response);
        dispatch({ type: TextBannerTypes.GET_TEXT_BANNER_SUCCESS, payload: response });
        return response;
    } catch (error) {
        console.error('TextBanner API error:', error);
        dispatch({ type: TextBannerTypes.GET_TEXT_BANNER_ERROR, payload: error });
        throw error;
    }
};

export const updateTextBanner = (data: TextBannerAPI.TextBanner, imageChange?: ImageChange) => async (dispatch: any) => {
    dispatch({ type: TextBannerTypes.UPDATE_TEXT_BANNER });
    try {
        console.log('updateTextBanner action received data:', data);
        console.log('Image URL in payload before processing:', data.image);

        // If there's a new image, upload it first
        if (imageChange?.file) {
            console.log('New image detected, uploading...');
            const fileName = await dispatch(uploadImage(imageChange.file, data.id));
            data.image = fileName;
            console.log('Image uploaded, new filename:', fileName);

            // No need to delete old image as backend will handle it
        } else if (data.image) {
            console.log('Using existing image URL:', data.image);
        } else {
            console.log('No image in payload');
        }

        console.log('Final payload before API call:', data);
        const response = await TextBannerAPI.updateTextBanner(data);
        console.log('updateTextBanner API response:', response);

        dispatch({ type: TextBannerTypes.UPDATE_TEXT_BANNER_SUCCESS, payload: response });
        return response;
    } catch (error) {
        console.error('updateTextBanner API error:', error);
        dispatch({ type: TextBannerTypes.UPDATE_TEXT_BANNER_ERROR, payload: error });
        throw error;
    }
};
