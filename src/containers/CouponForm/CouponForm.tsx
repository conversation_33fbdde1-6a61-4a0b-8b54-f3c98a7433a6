import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { CouponFormView } from './CouponFormView';
import { getCoupons, clearCoupon } from './CouponFormActions';

export default (props) => {
  const dispatch = useDispatch();
  const couponForm = useSelector((state: any) => state.couponForm);
  const brandPages = useSelector((state: any) => state.brandPages);
  const categories = useSelector((state: any) => state.categories);
  useEffect(() => {
    if (props.match.params.id !== 'create') {
      dispatch(getCoupons(props.match.params.id));
    }
    return () => {
      dispatch(clearCoupon());
    };
  }, [dispatch, props]);
  return (
    <CouponFormView
      loading={
        couponForm?.loading || brandPages?.loading || categories?.loading
      }
    />
  );
};
