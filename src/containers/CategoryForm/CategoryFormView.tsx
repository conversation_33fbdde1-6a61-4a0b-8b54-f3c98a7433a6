import React from 'react';
import { AdminForm } from './Forms';
import { PageTitle, Loading } from 'components';
import { useTranslation } from 'react-i18next';

import './CategoryForm.scss';

export const CategoryFormView = ({ data, loading }) => {
  const { t }: { t: any } = useTranslation();
  return (
    <div className="user-create-page">
      <PageTitle
        title={
          data?.activeCategory.id ? t('editCategory') : t('createCategory')
        }
      />
      {loading && <Loading />}
      <AdminForm category={data.activeCategory} />
    </div>
  );
};
