import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { UserView } from './UserView';
import { getUsers, deleteUser, changeFilter } from './UserActions';
import { UserTable } from './components/UserTable';
import { Loading } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { CLEAR_USER_ERROR_MESSAGES } from 'containers/UserForm/UserFormTypes';
import { CLEAR_USERS } from 'containers/User/UserTypes';
import { useTranslation } from 'react-i18next';

export default () => {
  const { t }: { t: any } = useTranslation();
  const dispatch = useDispatch();
  const users = useSelector((state: any) => state.user);
  const userForm = useSelector((state: any) => state.userForm);
  const userError = useSelector((state: any) => state.user.error);
  const userDeleteSuccess = useSelector(
    (state: any) => state.user.deleteSuccess
  );

  const [filter, setFilter] = useState({
    pageSize: users?.filter?.pageSize || 10,
    page: users?.filter?.page || 0,
    sort: users?.filter?.sort || {
      field: 'username',
      dir: 'DESC',
    },
    filters: [
      {
        field: 'roleId',
        operator: 'EQUAL',
        value: 1,
      },
    ],
    search: '',
  });

  const onFilterChange = (data) => {
    const filterUpdate = { ...filter, ...data };
    setFilter(filterUpdate);
    dispatch(getUsers(filterUpdate));
    dispatch(changeFilter(filterUpdate));
  };

  const { addMessage } = useAPIError();

  useEffect(() => {
    if (userDeleteSuccess) {
      addMessage(t('successDeleteUser'), 'warning');
    }
    if (userForm.editSuccess) {
      addMessage(t('successUpdateUser'), 'success');
    }
    if (userForm.success) {
      addMessage(t('accountVerificationEmailSent'), 'success');
    }
    if (userError) {
      addMessage(t(userError.errorCode || 'errorOccured'), 'error');
    }
    dispatch({
      type: CLEAR_USER_ERROR_MESSAGES,
    });
    dispatch({
      type: CLEAR_USERS,
    });
    dispatch(getUsers(filter));
  }, [dispatch, userDeleteSuccess, userError]);

  const handleDelete = (selectedUser) => {
    dispatch(deleteUser(selectedUser));
  };

  return (
    <UserView onFilterChange={onFilterChange} filter={filter}>
      {users.data && (
        <UserTable
          users={users.data}
          pagination={users.pagination}
          handleDelete={handleDelete}
          filter={filter}
          onFilterChange={onFilterChange}
        >
          {users.loading && <Loading />}
        </UserTable>
      )}
    </UserView>
  );
};
