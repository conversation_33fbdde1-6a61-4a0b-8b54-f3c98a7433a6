import * as CouponOfTheWeekTypes from './CouponOfTheWeekTypes';
import { CouponOfTheWeek } from 'repository/couponOfTheWeek';

interface State {
    loading: boolean;
    error: any;
    couponOfTheWeeks: CouponOfTheWeek[];
    selectedCouponOfTheWeek: CouponOfTheWeek | null;
    total: number;
}

const initialState: State = {
    loading: false,
    error: null,
    couponOfTheWeeks: [],
    selectedCouponOfTheWeek: null,
    total: 0,
};

export default function reducer(state = initialState, action: any) {
    switch (action.type) {
        case CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEKS:
        case CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEK:
        case CouponOfTheWeekTypes.CREATE_COUPON_OF_THE_WEEK:
        case CouponOfTheWeekTypes.UPDATE_COUPON_OF_THE_WEEK:
        case CouponOfTheWeekTypes.DELETE_COUPON_OF_THE_WEEK:
            return {
                ...state,
                loading: true,
                error: null,
            };

        case CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEKS_SUCCESS:
            return {
                ...state,
                loading: false,
                couponOfTheWeeks: action.payload,
                total: action.payload.length,
            };

        case CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEK_SUCCESS:
            return {
                ...state,
                loading: false,
                selectedCouponOfTheWeek: action.payload,
            };

        case CouponOfTheWeekTypes.CREATE_COUPON_OF_THE_WEEK_SUCCESS:
            return {
                ...state,
                loading: false,
                // Don't update the couponOfTheWeeks array here as we'll fetch fresh data
                error: null,
            };

        case CouponOfTheWeekTypes.UPDATE_COUPON_OF_THE_WEEK_SUCCESS:
            return {
                ...state,
                loading: false,
                // Don't update the couponOfTheWeeks array here as we'll fetch fresh data
                error: null,
            };

        case CouponOfTheWeekTypes.DELETE_COUPON_OF_THE_WEEK_SUCCESS:
            return {
                ...state,
                loading: false,
                // Don't update the couponOfTheWeeks array here as we'll fetch fresh data
                error: null,
            };

        case CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEKS_ERROR:
        case CouponOfTheWeekTypes.GET_COUPON_OF_THE_WEEK_ERROR:
        case CouponOfTheWeekTypes.CREATE_COUPON_OF_THE_WEEK_ERROR:
        case CouponOfTheWeekTypes.UPDATE_COUPON_OF_THE_WEEK_ERROR:
        case CouponOfTheWeekTypes.DELETE_COUPON_OF_THE_WEEK_ERROR:
            return {
                ...state,
                loading: false,
                error: action.payload,
            };

        default:
            return state;
    }
}
