import React, { useState } from 'react';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableFooter from '@material-ui/core/TableFooter';
import TablePagination from '@material-ui/core/TablePagination';
import TableRow from '@material-ui/core/TableRow';
import TableHead from '@material-ui/core/TableHead';
import { Link } from 'react-router-dom';

import IconButton from '@material-ui/core/IconButton';

import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import CancelIcon from '@material-ui/icons/Cancel';
import { Sorter } from '../../../components/Sorter/Sorter';

import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import { But<PERSON>, DialogContent } from '@material-ui/core';
import DialogTitle from '@material-ui/core/DialogTitle';
import * as RoleManager from 'services/role';
import { useTranslation } from 'react-i18next';

export const BrandPagesTable = ({
  brandPages,
  pagination,
  onFilterChange,
  handleDelete,
  handleEnableDisable,
  filter,
  children,
}) => {
  const { t }: { t: any } = useTranslation();
  const [page, setPage] = useState(filter.page);
  const [rowsPerPage, setRowsPerPage] = useState(filter.pageSize);
  const [selectedCategory, setSelectecCategory] = useState<{ status?: string }>({});
  const [delConfOpen, setDelConfOpen] = useState(false);
  const [actionType, setActionType] = useState('');

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    onFilterChange({
      page: newPage,
      pageSize: rowsPerPage,
    });
  };

  const handleChangeRowsPerPage = (event) => {
    const pageSize = parseInt(event.target.value, 10);
    setRowsPerPage(pageSize);

    setPage(0);
    onFilterChange({
      pageSize: pageSize,
      page: 0,
    });
  };

  const handleDeleteBrand = (brand) => {
    setSelectecCategory(brand);
    setActionType('delete');
    setDelConfOpen(true);
  }

  const handleEnableDisableBrand = (brand) => {
    setSelectecCategory(brand);
    setActionType(brand.status === "ACTIVE" ? 'disable' : 'enable');
    setDelConfOpen(true);
  }

  const handleClose = () => {
    setDelConfOpen(false);
  };

  if (brandPages.length === 0) return <h2>{t('noResultsFound')}</h2>;

  return (
    <TableContainer>
      {children}
      <Table aria-label="custom pagination table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Sorter
                label={t('id')}
                handleSort={(direction) =>
                  onFilterChange({ sort: { field: 'id', dir: direction } })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('title')}
                handleSort={(direction) =>
                  onFilterChange({ sort: { field: 'name', dir: direction } })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('brandAdmin')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'fullName', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              <Sorter
                label={t('paywallDisabled')}
                handleSort={(direction) =>
                  onFilterChange({
                    sort: { field: 'isPaywallDisabled', dir: direction },
                  })
                }
              />
            </TableCell>
            <TableCell>
              {t('isBlocked')}
            </TableCell>
            <TableCell>
              {t('brandVisibility')}
            </TableCell>
            <TableCell>
              {t('bannerEnabled')}
            </TableCell>
            <TableCell>{t('actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {brandPages.map((row) => (
            <TableRow key={row.id}>
              <TableCell scope="row">{row.id}</TableCell>
              <TableCell scope="row">{row.name}</TableCell>
              <TableCell scope="row">{row.fullName}</TableCell>
              <TableCell scope="row">
                {row.isPaywallDisabled ? t('yes') : t('no')}
              </TableCell>
              <TableCell scope="row">
                {row.isBlocked ? t('yes') : t('no')}
              </TableCell>
              <TableCell scope="row">
                {row.isVisible ? t('yes') : t('no')}
              </TableCell>
              <TableCell scope="row">
                {row.enableBanner ? t('yes') : t('no')}
              </TableCell>
              <TableCell component="th" scope="row">
                <div className='d-inline-flex'>
                  <IconButton
                    component={Link}
                    to={`/dashboard/brand-pages/${row.id}`}
                    aria-label="edit"
                    color="default"
                  >
                    <EditIcon />
                  </IconButton>
                  {RoleManager.isAbleTo('brand_page', 'disable') && (
                    <IconButton
                      aria-label="enable disable"
                      color="secondary"
                      onClick={() => handleEnableDisableBrand(row)}
                    >
                      <CancelIcon />
                    </IconButton>
                  )}
                  {RoleManager.isAbleTo('brand_page', 'delete') && (
                    <IconButton
                      aria-label="delete"
                      color="secondary"
                      onClick={() => handleDeleteBrand(row)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, { label: t('all'), value: -1 }]}
              colSpan={6}
              count={pagination?.last || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              labelRowsPerPage={t('rowsPerPage')}
              SelectProps={{
                inputProps: { 'aria-label': 'Rows per page:' },
                native: true,
              }}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>

      <Dialog
        open={delConfOpen}
        onClose={handleClose}
        aria-labelledby="responsive-dialog-title"
        disableBackdropClick
        disableEscapeKeyDown
        maxWidth="md"
      >
        <DialogTitle>
          {actionType === "delete" && t('sureDeleteBrand')}
          {actionType === "disable" && t('sureDisableBrand')}
          {actionType === "enable" && t('sureEnableBrand')}
        </DialogTitle>
        <DialogContent dividers className="pad-tb-3">
          {t('noUndo')}
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={handleClose}
            color={'inherit'}
            variant="contained"
          >
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              actionType === "delete" ? handleDelete(selectedCategory) : handleEnableDisable(selectedCategory);
              handleClose();
            }}
            color="primary"
            variant="contained"
          >
            {t('ok')}
          </Button>
        </DialogActions>
      </Dialog>
    </TableContainer>
  );
};
