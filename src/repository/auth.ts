import API from 'services/api';

export const loginUser = async ({ username, password }) => {
  // expected error messages from BE
  // - userDisabled
  // - invalidCredentials
  // - loggedInUserNotFound
  return await API.post('/authenticate', { username, password });
};

export const forgotPassword = async (mail) =>
  await API.get(
    `/users/verification/forgot-password?username=${encodeURIComponent(mail)}`
  );
// expected error messages from BE
// - userNotFound

export const setPasswordUser = async ({ password, token, newUser }) => {
  // expected error messages from BE
  // - verificationTokenNotFound
  // - verificationTokenExpired

  if (newUser) {
    return await API.post('/users/verification/confirm-registration', {
      token,
      newPassword: password,
    });
  } else {
    return await API.post('/users/verification/reset-password', {
      token,
      newPassword: password,
    });
  }
};
