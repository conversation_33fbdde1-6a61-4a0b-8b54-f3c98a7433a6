import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>abel, TextField, IconButton } from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/Delete';
import { Autocomplete } from '@material-ui/lab';
import { DatePickerInput } from 'components/FormInputs';
import { useForm, Controller } from 'react-hook-form';

interface CouponItem {
  id?: string | number;
  brand: string;
  coupon: string;
  startDate: Date | string | null;
  endDate: Date | string | null;
}

interface Brand {
  id: string;
  name: string;
}

interface CouponOption {
  id: string | number;
  brandName?: string;
  categoryName?: string;
  validationDate?: string;
  amountCondition?: string;
  status?: string;
  isCountDownEnabled?: boolean;
}

interface CouponRowProps {
  coupon: CouponItem;
  brands: Brand[];
  allCoupons?: CouponOption[];
  onDelete: () => void;
  onChange: (data: Partial<CouponItem>) => void;
  formatCouponDisplay?: (coupon: CouponOption) => string;
}

const labelStyle = { fontSize: 12, marginBottom: 2 };
const inputStyle = { borderRadius: '12px', fontSize: 11, height: 28 };
const columnStyle = { width: '20%', marginRight: '2%' };

const CouponRow: React.FC<CouponRowProps> = ({
  coupon,
  brands,
  allCoupons = [],
  onDelete,
  onChange,
  formatCouponDisplay
}) => {
  const { control, watch, setValue } = useForm({
    defaultValues: {
      brand: coupon.brand || '',
      coupon: coupon.coupon || '',
      startDate: coupon.startDate || null,
      endDate: coupon.endDate || null,
    },
  });

  const values = watch();
  const [brandCoupons, setBrandCoupons] = useState<CouponOption[]>([]);
  const [loading, setLoading] = useState(false);

  // Update parent component when values change
  useEffect(() => {
    onChange && onChange({ ...coupon, ...values });
    // eslint-disable-next-line
  }, [values.brand, values.coupon, values.startDate, values.endDate]);

  // Set date values when coupon changes
  useEffect(() => {
    if (coupon.startDate && setValue) {
      setValue('startDate', coupon.startDate);
    }

    if (coupon.endDate && setValue) {
      setValue('endDate', coupon.endDate);
    }

    // Set coupon value when it changes
    if (coupon.coupon && setValue) {
      setValue('coupon', coupon.coupon);
    }
  }, [coupon.startDate, coupon.endDate, coupon.coupon, setValue]);

  // Filter coupons when brand changes
  useEffect(() => {
    // Set loading state
    setLoading(true);

    if (values.brand) {
      // For now, return all coupons without filtering
      // Later, this will be updated to filter by brandId
      const filteredCoupons = allCoupons;
      console.log('Filtered coupons (currently showing all):', filteredCoupons);
      setBrandCoupons(filteredCoupons);
    } else {
      // If no brand selected, show all coupons
      setBrandCoupons(allCoupons);
    }

    // Set loading to false
    setLoading(false);
  }, [values.brand, allCoupons]);

  // Format the coupon display text as {categoryName}-{amountCondition}
  const getDisplayText = (option: CouponOption) => {
    if (!option) {
      console.log('Warning: Trying to format undefined coupon option');
      return '';
    }

    if (formatCouponDisplay) {
      return formatCouponDisplay(option);
    }

    // Format as categoryName-amountCondition
    const categoryName = option.categoryName || '';
    const amountCondition = option.amountCondition || '';

    const displayText = `${categoryName}-${amountCondition}`;
    console.log('Display text for coupon:', displayText, 'Option:', option);
    return displayText;
  };

  // Log brandCoupons when component renders
  console.log('brandCoupons when rendering:', brandCoupons);

  // Initialize with selected coupon
  useEffect(() => {
    // Find the selected coupon in allCoupons
    if (coupon.coupon && allCoupons.length > 0) {
      const selectedCoupon = allCoupons.find(c => c.id.toString() === coupon.coupon.toString());
      if (selectedCoupon) {
        console.log('Found selected coupon:', selectedCoupon);
        setBrandCoupons(prevCoupons => {
          // Make sure the selected coupon is in brandCoupons
          if (!prevCoupons.some(c => c.id.toString() === selectedCoupon.id.toString())) {
            return [...prevCoupons, selectedCoupon];
          }
          return prevCoupons;
        });
      }
    }
  }, [coupon.coupon, allCoupons]);

  return (
    <div className="coupon-row" style={{
      backgroundColor: '#f8f9fa',
      border: '1px solid #707070',
      borderRadius: '12px',
      padding: '20px',
      marginBottom: '10px',
      display: 'flex',
      justifyContent: 'center' // Center-align content like BannerRow
    }}>
      <div className="coupon-content" style={{ display: 'flex' }}>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>brand</FormLabel>
          <Controller
            name="brand"
            control={control}
            render={({ onChange, value }) => (
              <Autocomplete
                options={brands}
                getOptionLabel={(option) => option.name}
                value={brands.find(b => b.id === value || b.name === value) || null}
                onChange={(_event, newValue) => {
                  onChange(newValue ? newValue.id : '');
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    variant="outlined"
                    size="small"
                    InputProps={{
                      ...params.InputProps,
                      style: inputStyle
                    }}
                    inputProps={{
                      ...params.inputProps,
                      style: { fontSize: 11, minHeight: 28 }
                    }}
                  />
                )}
                ListboxProps={{
                  style: {
                    maxHeight: 120,
                    fontSize: 11,
                    overflowY: 'auto',
                    borderRadius: 8,
                    boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                  }
                }}
              />
            )}
          />
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>coupon</FormLabel>
          <Controller
            name="coupon"
            control={control}
            render={({ onChange, value }) => (
              <Autocomplete
                options={brandCoupons}
                getOptionLabel={option => getDisplayText(option)}
                value={brandCoupons.find(c => {
                  if (!c || !value) return false;
                  console.log('Comparing coupon id:', c?.id, 'with value:', value);
                  return c?.id?.toString() === value?.toString();
                }) || null}
                onChange={(_event, newValue) => {
                  console.log('Selected coupon:', newValue);
                  onChange(newValue ? newValue.id.toString() : '');
                }}
                loading={loading}
                noOptionsText="No coupons available"
                renderInput={params => (
                  <TextField
                    {...params}
                    fullWidth
                    variant="outlined"
                    size="small"
                    InputProps={{
                      ...params.InputProps,
                      style: inputStyle,
                      endAdornment: (
                        <React.Fragment>
                          {loading ? <span style={{ fontSize: 10 }}>Loading...</span> : null}
                          {params.InputProps.endAdornment}
                        </React.Fragment>
                      ),
                    }}
                    inputProps={{ ...params.inputProps, style: { fontSize: 11, minHeight: 28 } }}
                  />
                )}
                ListboxProps={{
                  style: {
                    maxHeight: 120,
                    fontSize: 11,
                    overflowY: 'auto',
                    borderRadius: 8,
                    boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                  }
                }}
              />
            )}
          />
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>start date</FormLabel>
          <DatePickerInput
            name="startDate"
            control={control}
            variant="outlined"
            size="small"
            label=""
            format="dd-MM-yyyy"
            InputProps={{ style: inputStyle }}
          />
        </div>
        <div style={columnStyle}>
          <FormLabel style={labelStyle}>until</FormLabel>
          <DatePickerInput
            name="endDate"
            control={control}
            variant="outlined"
            size="small"
            label=""
            format="dd-MM-yyyy"
            InputProps={{ style: inputStyle }}
          />
        </div>
      </div>
      {onDelete && (
        <div className="coupon-actions" style={{ marginLeft: 'auto', paddingLeft: '10px' }}>
          <IconButton
            onClick={onDelete}
            style={{ color: '#757575' }}
          >
            <DeleteIcon />
          </IconButton>
        </div>
      )}
    </div>
  );
};

export default CouponRow;