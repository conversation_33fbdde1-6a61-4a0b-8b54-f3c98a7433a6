import React from 'react';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ErrorMessage } from '@hookform/error-message';
import {
  FormLabel,
  Grid,
} from '@material-ui/core';

import { BoxWrap, InputError } from 'components';
import { FormTextField } from 'components/FormInputs';
import { validateUrl } from 'services/validation';
import { DatePickerInput } from 'components/FormInputs';

import './BrandBannerDetails.scss';

interface BrandPricingDetailsProps {
  control: any;
  setValue: any;
  clearErrors: any;
  register: any;
  errors: any;
  watch: any;
  formValues: Object;
  trigger: any
}

const BrandBannerDetails = ({
  formValues = {},
  control,
  setValue,
  errors,
  watch,
  trigger
}: BrandPricingDetailsProps) => {
  const { t }: { t: any } = useTranslation();

  const validationDateWatcherScheduled = watch('bannerScheduledDate');
  const validationDateWatcherExpiry = watch('bannerExpiryDate');
  const enableBanner = watch('enableBanner');

  const validationDateValidator = () => {
  
    const validationDate = new Date(validationDateWatcherScheduled);
    if (validationDate.toString() === 'Invalid Date') {
      return t('invalidDate');
    }
  
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set time to midnight to ignore time component
  
    validationDate.setDate(validationDate.getDate() + 1); // Adding one day
    validationDate.setHours(0, 0, 0, 0); // Set time to midnight to ignore time component
  
    if (validationDate < today) {
      return t('validationDateMustNotBeInPast');
    } else {
      return true;
    }
  };

  const validationDateValidatorExpiry = () => {

    const validationDateExpiry = new Date(validationDateWatcherExpiry);
    if (validationDateExpiry.toString() === 'Invalid Date') {
      return t('invalidDate');
    }

    const validationDateScheduled = new Date(validationDateWatcherScheduled);
    if(validationDateScheduled > validationDateExpiry) {
      return t('expiryDateMustBeAfterScheduledDate');
    }
    return true;
  }

  return (
    <>
      <div className="brand-banner-title">
        <FormLabel>{t('brandBanner')}</FormLabel>
      </div>
      <BoxWrap>
        <Grid container spacing={5}>
          <Grid item sm={6} xs={12}>
            <FormLabel>
            {t('bannerText')}
            </FormLabel>
            <FormTextField
              name="bannerText"
              label={''}
              margin="none"
              size="small"
              variant="outlined"
              className="rounded-input-20"
              formValues={formValues}
              rules={{
                required: enableBanner && t('bannerTextRequired'),
                maxLength: {
                  value: 200,
                  message: t('bannerTextLess200'),
                },
              }}
            />
          </Grid>
          <Grid item sm={6} xs={12}>
            <FormLabel>
            {t('bannerRedirectUrl')}
            </FormLabel>
            <FormTextField
              name="bannerRedirectUrl"
              label={''}
              margin="none"
              size="small"
              variant="outlined"
              className="rounded-input-20"
              formValues={formValues}
              rules={{
                required: enableBanner && t('bannerRedirectUrlRequired'),
                maxLength: {
                  value: 256,
                  message: t('bannerRedirectUrl256'),
                },
                validate: (url) => {
                  if (enableBanner && !validateUrl(url)) {
                    return t('bannerRedirectValidUrl');
                  }
                  return true;
                },
              }}
              helperText={[
                <i key="nameHelperI">
                  {t('example')} http://yourwebsite.com
                  <br />
                </i>,
                errors.bannerRedirectUrl ? (
                  <InputError
                    message={errors.bannerRedirectUrl.message}
                    key="nameHelperM"
                  />
                ) : null,
              ]}
            />
          </Grid>
          <Grid item sm={6} xs={12}>
            <FormLabel>
              {t('enableBanner')}
            </FormLabel>
            <Controller
              control={control}
              name="enableBanner"
              rules={{
                validate: {
                  req: function (value) {
                    return value === true || value === false;
                  },
                },
              }}
              render={({ value }) => (
                <div className='pt-15px'>
                  <label style={{ marginRight: '20px' }}>
                    <input
                      type="radio"
                      value="1"
                      name="enableBanner1"
                      onChange={(event) => {
                        setValue('enableBanner', true);
                        trigger('enableBanner');
                      }}
                      checked={value === true || value === '1'}
                    />
                    {t('yes')}
                  </label>

                  <label>
                    <input
                      type="radio"
                      value="0"
                      name="enableBanner0"
                      onChange={(event) => {
                        setValue('enableBanner', false);
                        trigger('enableBanner');
                      }}
                      checked={value === false || value === '0'}
                    />
                    {t('no')}
                  </label>
                </div>
              )}
            />
          </Grid>
          <Grid item sm={6} xs={12}>
            <div>
              <FormLabel>
                {t('bannerScheduledDate')}
              </FormLabel>
              <DatePickerInput
                name="bannerScheduledDate"
                control={control}
                variant='outlined'
                size="small"
                className="rounded-input-20 date-picker-input"
                rules={{
                  required: enableBanner && t('bannerScheduledDateRequired'),
                  validate: enableBanner,
                }}
                invalidDateMessage=""
                maxDateMessage={t('maxDateMessage')}
                label={''}
              />

              <ErrorMessage
                errors={errors}
                name="bannerScheduledDate"
                render={({ message }) => <InputError message={message} />}
              />
            </div>
          </Grid>
          <Grid item sm={6} xs={12}>
            <div>
              <FormLabel>
                {t('bannerExpiryDate')}
              </FormLabel>
              <DatePickerInput
                name="bannerExpiryDate"
                control={control}
                variant='outlined'
                size="small"
                className="rounded-input-20 date-picker-input"
                rules={{
                  required: enableBanner && t('bannerExpiryDateRequired'),
                  validate: enableBanner && validationDateValidatorExpiry,
                }}
                invalidDateMessage=""
                maxDateMessage={t('maxDateMessage')}
                label={''}
              />

              <ErrorMessage
                errors={errors}
                name="bannerExpiryDate"
                render={({ message }) => <InputError message={message} />}
              />
            </div>
          </Grid>
        </Grid>
      </BoxWrap>
    </>
  );
};

export default BrandBannerDetails;
