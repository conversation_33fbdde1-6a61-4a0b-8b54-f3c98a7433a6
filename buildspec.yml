version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: 14
  build:
    commands:
      - npm i -g yarn
      - yarn install
      - cp .env.$ENV .env.$ENV.tmp
      - mv .env.$ENV.tmp .env.production
      - NODE_ENV=production yarn build --verbose
      - aws s3 sync build/ s3://$BUCKET --grants read=uri=http://acs.amazonaws.com/groups/global/AllUsers
cache:
  paths:
    - "node_modules/**/*"
