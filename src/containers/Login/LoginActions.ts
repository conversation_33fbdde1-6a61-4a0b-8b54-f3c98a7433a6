import jwt from 'jsonwebtoken';
import { LOGIN_START, LOGIN_SUCCESS, LOGIN_ERROR } from './LoginTypes';
import * as AuthRepository from 'repository/auth';
import * as AuthService from 'services/auth';
import * as AccountService from 'services/account';
import * as RoleManager from 'services/role';

export const loginUser = (data, redirect = true) => {
  return async (dispatch) => {
    dispatch({
      type: LOGIN_START,
    });

    try {
      const response = await AuthRepository.loginUser(data);

      if (response.data && response.data.token) {
        const tokenData = jwt.decode(response.data.token, { complete: true });

        dispatch({
          type: LOGIN_SUCCESS,
          payload: response.data,
        });
        AuthService.saveUserToken(response.data.token, data.rememberMe);
        AccountService.saveAccount({
          firstName: tokenData.payload.firstName || '',
          lastName: tokenData.payload.surname || '',
          email: tokenData.payload.sub || data.username,
          authorities: [{ name: tokenData.payload.role }],
          isBlocked: tokenData.payload.isBlocked,
          restrictSeoFields: tokenData.payload.restrictSeoFields,
          stripeCustomerId: tokenData.payload.stripeCustomerId,
          stripeSessionUrl: tokenData.payload.stripeSessionUrl || '',
        });

        if (redirect) {
          if (RoleManager.isAbleTo('user', 'view')) {
            window.location.href = '/dashboard/admin-users';
          } else if (RoleManager.isAbleTo('brand_coupon', 'view')) {
            window.location.href = '/dashboard/brand-account';
          } else {
            window.location.href = '/dashboard/coupons';
          }
        }
      }
    } catch (error) {
      dispatch({
        type: LOGIN_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearErrors = () => ({
  type: LOGIN_ERROR,
  payload: null,
});
