import React, { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { useParams } from 'react-router';
import { useTranslation } from 'react-i18next';

import {
  setPasswordUser,
  clearErrors,
  setPasswordSetToken,
  getTokenErrorType,
} from './SetPasswordActions';
import { SetPasswordView } from './SetPasswordView';
import useAPIError from 'components/APIErrorNotification/useAPIError';

import './SetPassword.scss';

export default () => {
  const { t }: { t: any } = useTranslation();
  const dispatch = useDispatch();
  const setPasswordState = useSelector((state: any) => state.setPassword);
  const setPasswordError = useSelector((state: any) => state.setPassword.error);
  const setPasswordSuccess = useSelector(
    (state: any) => state.setPassword.success
  );

  const history = useHistory();
  const { addMessage } = useAPIError();

  const setPassword = useCallback(
    (data) => {
      const redirect = false;
      dispatch(setPasswordUser(data, redirect));
    },
    [dispatch, history]
  );

  const removeErrors = () => {
    dispatch(clearErrors());
  };

  const { signUptoken, changePasswordToken } = useParams();

  useEffect(() => {
    dispatch(setPasswordSetToken(signUptoken || changePasswordToken));
  }, [signUptoken, changePasswordToken, dispatch]);

  useEffect(() => {
    if (setPasswordError) {
      const reason = getTokenErrorType(setPasswordError.errorCode);
      history.push(`/invalid-token/${reason}`);
    } else if (setPasswordSuccess) {
      addMessage(t('operationSuccessful'), 'success');
    }
  }, [setPasswordError, setPasswordSuccess]);

  return (
    <SetPasswordView
      newUser={!!signUptoken}
      clearErrors={removeErrors}
      setPassword={setPassword}
      setPasswordState={setPasswordState}
    />
  );
};
