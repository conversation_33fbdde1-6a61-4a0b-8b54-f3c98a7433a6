import React, { useEffect } from 'react';
import { Loading } from 'components';
import { AdminForm } from './Forms/AdminForm';
import { useDispatch } from 'react-redux';
import * as globalSettingActions from './GlobalSettingActions';
import { useSelector } from 'react-redux';
import { gl } from 'date-fns/locale';

export default () => {

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(globalSettingActions.getGlobalSetting());
  }, [dispatch]);

  const globalSettingForm = useSelector((state: any) => state.globalSetting);

  console.log(globalSettingForm.loading)
  
  return (
    <div>
      {globalSettingForm.loading ? (<Loading />) : (
        <AdminForm {...globalSettingForm.data} />
      )}
    </div>
  );
};
