import React, { useRef, useEffect } from 'react';
import { Loading } from 'components';


export default ({ src, title, style={} }) => {

  const [loaded, setLoaded] = React.useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
      const iframe = iframeRef.current;
      if (iframe) {
          iframe.onload = () => {
            setLoaded(true);
          };
      }
  }, []);

  useEffect(() => {
    // Hide overflow (disable scroll) when the component mounts
    document.body.style.overflow = 'hidden';

    // Revert back to the original style when the component unmounts
    return () => {
        document.body.style.overflow = 'auto';
    };
  }, []);
  
  return (
    <div>
      {!loaded && <Loading isCenter={true} />}
      
      <iframe
        ref={iframeRef}
        src={src}
        title={title}
        style={{ width: '100%', height: '95vh', border: 'none', display: loaded ? 'block' : 'none', ...style }}
        sandbox="allow-scripts allow-popups allow-forms allow-top-navigation allow-same-origin"
      ></iframe>
    </div>
  )
}