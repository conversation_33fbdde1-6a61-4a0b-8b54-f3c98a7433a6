import { FormControl, TextField, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import React, { ChangeEvent, ReactNode, useCallback } from 'react';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
  },
  label: {
    flex: 3,
    fontSize: '0.8rem',
    fontWeight: 600,
    textTransform: 'uppercase',
    paddingRight: '0.8rem',
  },
  elementContainer: {
    flex: 1,
    textAlign: 'right',
  },
});

interface Props {
  label: string;
  children: ReactNode;
}

const RequestInputContainer = ({ label, children }: Props) => {
  const classes = useStyles();
  return (
    <FormControl className={classes.root}>
      <Typography className={classes.label}>{label}</Typography>
      <div className={classes.elementContainer}>{children}</div>
    </FormControl>
  );
};

export default RequestInputContainer;
