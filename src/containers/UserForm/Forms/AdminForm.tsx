import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

import {
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@material-ui/core';
import { useForm, Controller } from 'react-hook-form';

import { BoxWrap, InputError } from 'components';
import { useParams } from 'react-router';

import { NavLink, useHistory } from 'react-router-dom';
import * as userActions from '../UserFormActions';
import { emailRegex } from 'services/config';
import { ErrorMessage } from '@hookform/error-message';
import useAPIError from 'components/APIErrorNotification/useAPIError';

interface submitDataType {
  firstName: string;
  surname: string;
  roleId: number;
  username: string;
  brandId?: string;
  restrictSeoFields?: boolean
}

export const AdminForm = ({ user }) => {
  const { t }: { t: any } = useTranslation();
  const { register, handleSubmit, setValue, control, errors } = useForm({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: {
      ...user,
    },
    criteriaMode: 'all',
  });

  const dispatch = useDispatch();
  const history = useHistory();
  const { id } = useParams();
  const [inputVals, setInputVals]: any = useState(user);
  const [emailNotValid, setEmailNotValid]: any = useState(false);
  const userForm = useSelector((state: any) => state.userForm);

  const inputChange = (field, event) => {
    setInputVals({ ...inputVals, [field]: event.target.value });
    setEmailNotValid(false);
  };

  const { addMessage } = useAPIError();

  useEffect(() => {
    if (isNewUser(id)) {
      dispatch(userActions.clearUser());
    }
    setInputVals({ ...user });
    setValue('roleId', user?.roleId);
  }, [user, dispatch, setValue]);

  const isNewUser = (id) => {
    if (id !== 'create') {
      return false;
    } else {
      return true;
    }
  };

  const resendMail = useCallback(
    (event) => {
      event.stopPropagation();

      dispatch(userActions.resendActivationMail(id));
    },
    [dispatch, id]
  );

  useEffect(() => {
    if (userForm.error?.errorCode === 'usernameAlreadyExists') {
      setEmailNotValid(true);
    } else if (userForm.error) {
      addMessage(t(userForm.error.errorCode || 'unexpectedError'), 'error');
    } else if (userForm.success || userForm.editSuccess) {
      history.push('/dashboard/admin-users');
    }
  }, [userForm, addMessage]);

  const submit = (data: any) => {
    const submitData: submitDataType = {
      firstName: data.firstName,
      surname: data.surname,
      roleId: data.roleId,
      username: data.username,
      ...(user?.brandId ? { brandId: user?.brandId } : {}),
      restrictSeoFields: Boolean(user?.restrictSeoFields)
    };

    if (!isNewUser(id)) {
      dispatch(userActions.editUser(Number.parseInt(id), submitData));
    } else {
      dispatch(userActions.createUser(submitData));
    }
  };

  return (
    <form onSubmit={handleSubmit(submit)}>
      <BoxWrap>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <TextField
              inputRef={register({
                required: t('firstNameRequired'),
                maxLength: {
                  value: 64,
                  message: t('firstNameLess64'),
                },
              })}
              label={t('firstName')}
              fullWidth={true}
              inputProps={{
                name: 'firstName',
              }}
              value={inputVals?.firstName || ''}
              onChange={(event) => inputChange('firstName', event)}
              helperText={
                errors.firstName && (
                  <InputError message={errors.firstName.message} />
                )
              }
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              inputRef={register({
                required: t('lastNameRequired'),
                maxLength: {
                  value: 64,
                  message: t('lastNameLess64'),
                },
              })}
              label={t('lastName')}
              fullWidth={true}
              inputProps={{
                name: 'surname',
              }}
              value={inputVals?.surname || ''}
              onChange={(event) => inputChange('surname', event)}
              helperText={
                errors.surname && (
                  <InputError message={errors.surname.message} />
                )
              }
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              inputRef={register({
                required: t('emailRequired'),
                pattern: {
                  value: emailRegex,
                  message: t('emailNotValid'),
                },
                maxLength: {
                  value: 100,
                  message: t('emailLess100'),
                },
              })}
              label={t('email')}
              fullWidth={true}
              inputProps={{
                name: 'username',
              }}
              value={inputVals?.username || ''}
              onChange={(event) => inputChange('username', event)}
              helperText={
                (errors.username && (
                  <InputError message={errors.username.message} />
                )) ||
                (emailNotValid && (
                  <InputError message={t('emailAlreadyExists')} />
                ))
              }
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel htmlFor="roleId">{t('role')}</InputLabel>
              <Controller
                name="roleId"
                control={control}
                rules={{ required: true }}
                defaultValue={inputVals?.roleId || ''}
                as={
                  <Select
                    onChange={(event: any) => {
                      setValue('roleId', event.target.value);
                    }}
                    fullWidth={true}
                  >
                    <MenuItem value={1}>{t('admin')}</MenuItem>
                    <MenuItem value={2}>{t('contentManager')}</MenuItem>
                  </Select>
                }
              />
              <ErrorMessage
                errors={errors}
                name="roleId"
                render={({ message }) => (
                  <InputError message={t('roleRequired')} />
                )}
              />
            </FormControl>
          </Grid>
        </Grid>
      </BoxWrap>
      <Button
        className="submit-button"
        type="submit"
        variant="contained"
        color="primary"
      >
        {isNewUser(id) ? t('create') : t('save')}
      </Button>

      <Button
        component={NavLink}
        to="/dashboard/admin-users/"
        className={'btn btn-cancel'}
        color="primary"
      >
        {t('cancel')}
      </Button>
      {!isNewUser(id) &&
      (user?.status === 'ACTIVATION_EXPIRED' ||
        user?.status === 'ACTIVATION_EMAIL_SENT') ? (
        <Button
          onClick={resendMail}
          className="submit-button"
          variant="contained"
          color="secondary"
        >
          {t('resetActivationEmail')}
        </Button>
      ) : null}
    </form>
  );
};
