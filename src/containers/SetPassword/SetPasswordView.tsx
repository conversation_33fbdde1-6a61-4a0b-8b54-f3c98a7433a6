import React from 'react';
import { IntroCard, SetPasswordForm } from 'components';
import { useTranslation } from 'react-i18next';

import './SetPassword.scss';

export const SetPasswordView = ({
  newUser,
  setPasswordState,
  setPassword,
  clearErrors,
}) => {
  const { t }: { t: any } = useTranslation();
  return (
    <div className="set-password-page">
      <IntroCard
        title={t('couponManagementSystem')}
        subtitle={newUser ? t('signUpToYourAccount') : t('changeYourPassword')}
      >
        <SetPasswordForm
          newUser={newUser}
          setPasswordState={setPasswordState}
          setPassword={setPassword}
          clearErrors={clearErrors}
        />
      </IntroCard>
    </div>
  );
};
