export const HOMEPAGE_START = 'homepage/start';
export const HOMEPAGE_SUCCESS = 'homepage/success';
export const HOMEPAGE_ERROR = 'homepage/error';

// Existing action types
export const GET_BANNER_SLIDERS = 'GET_BANNER_SLIDERS';
export const GET_BANNER_SLIDERS_SUCCESS = 'GET_BANNER_SLIDERS_SUCCESS';
export const GET_BANNER_SLIDERS_ERROR = 'GET_BANNER_SLIDERS_ERROR';

// New image handling action types
export const UPLOAD_BANNER_IMAGE = 'UPLOAD_BANNER_IMAGE';
export const UPLOAD_BANNER_IMAGE_SUCCESS = 'UPLOAD_BANNER_IMAGE_SUCCESS';
export const UPLOAD_BANNER_IMAGE_ERROR = 'UPLOAD_BANNER_IMAGE_ERROR';

// Media delete will be handled by backend

// Create/Update/Delete banner slider action types
export const CREATE_BANNER_SLIDER = 'CREATE_BANNER_SLIDER';
export const CREATE_BANNER_SLIDER_SUCCESS = 'CREATE_BANNER_SLIDER_SUCCESS';
export const CREATE_BANNER_SLIDER_ERROR = 'CREATE_BANNER_SLIDER_ERROR';

export const UPDATE_BANNER_SLIDER = 'UPDATE_BANNER_SLIDER';
export const UPDATE_BANNER_SLIDER_SUCCESS = 'UPDATE_BANNER_SLIDER_SUCCESS';
export const UPDATE_BANNER_SLIDER_ERROR = 'UPDATE_BANNER_SLIDER_ERROR';

export const DELETE_BANNER_SLIDER = 'DELETE_BANNER_SLIDER';
export const DELETE_BANNER_SLIDER_SUCCESS = 'DELETE_BANNER_SLIDER_SUCCESS';
export const DELETE_BANNER_SLIDER_ERROR = 'DELETE_BANNER_SLIDER_ERROR';

export const CHANGE_PAGINATION = 'homepage/change-pagination';