import React, { useState, useCallback } from 'react';
import { Grid, Button, TextField } from '@material-ui/core';
import SearchIcon from '@material-ui/icons/Search';
import { PageTitle, BoxWrap, Loading } from 'components';
import './Categories.scss';
import { Link } from 'react-router-dom';
import { debounce } from 'utils/debouncer';
import { useTranslation } from 'react-i18next';

export const CategoriesView = ({ onFilterChange, filter, children }) => {
  const { t }: { t: any } = useTranslation();
  const [userQuery, setUserQuery] = useState('');
  const delayedQuery = useCallback(
    debounce((q) => {
      onFilterChange({
        ...filter,
        search: q,
      });
    }, 500),
    [filter]
  );
  const onChange = (e) => {
    setUserQuery(e.target.value);
    delayedQuery(e.target.value);
  };

  return (
    <div className="page categories">
      <PageTitle title={t('categoriesManagement')}>
        <Button
          component={Link}
          to="/dashboard/category/create"
          color="primary"
          variant="contained"
        >
          {t('createCategory')}
        </Button>
      </PageTitle>
      <BoxWrap>
        <BoxWrap.Toolbar>
          <Grid container spacing={1} alignItems="flex-end">
            <Grid item>
              <SearchIcon />
            </Grid>
            <Grid item>
              <TextField
                id="input-with-icon-grid"
                label={t('searchCategories')}
                onChange={onChange}
                value={userQuery}
              />
            </Grid>
          </Grid>
        </BoxWrap.Toolbar>
        {children}
      </BoxWrap>
    </div>
  );
};
