import React from 'react';

import NurInDenFilialenIcon from '../../CustomIcons/NurInDenFilialenIcon';
import NeukundenRabattIcon from '../../CustomIcons/NeukundenRabattIcon';

function CouponImageBadge({ className = '', coupon }) {
  return (
    <div>
      {coupon.isForNewCustomers && (
        <NeukundenRabattIcon className="image-badge" />
      )}
      {coupon.discountCodeType === 'STATIONARY' && (
        <NurInDenFilialenIcon className="image-badge" />
      )}
    </div>
  );
}
export default CouponImageBadge;
