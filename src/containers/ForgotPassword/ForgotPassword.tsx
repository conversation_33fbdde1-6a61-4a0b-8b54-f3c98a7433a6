import React from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { ForgotPasswordView } from './ForgotPasswordView';
import { forgotPassword, clearErrors } from './ForgotPasswordActions';

export default () => {
  const dispatch = useDispatch();
  const forgotPasswordState = useSelector((state: any) => state.forgotPassword);

  const submitForgotPassword = (email) => {
    dispatch(forgotPassword(email));
  };

  const removeErrors = () => {
    dispatch(clearErrors());
  };

  return (
    <ForgotPasswordView
      resetPassword={submitForgotPassword}
      forgotPasswordState={forgotPasswordState}
      clearErrors={removeErrors}
    />
  );
};
