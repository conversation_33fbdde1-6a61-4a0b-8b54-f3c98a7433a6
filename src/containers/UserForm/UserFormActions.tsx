import {
  CLEAR_USER,
  <PERSON><PERSON><PERSON>_USER_SUCCESS,
  CREATE_USER,
  CREATE_USER_ERROR,
  CREATE_USER_SUCCESS,
  EDIT_USER,
  EDIT_USER_ERROR,
  EDIT_USER_SUCCESS,
  GET_USER,
  GET_USER_ERROR,
  GET_USER_SUCCESS,
  RESEND_ACTIVATION,
  RESEND_ACTIVATION_SUCCESS,
  RESEND_ACTIVATION_ERROR,
} from './UserFormTypes';
import * as UserRepository from 'repository/user';

export const createUser = (user: any) => {
  return async (dispatch) => {
    dispatch({
      type: CREATE_USER,
    });

    try {
      const response = await UserRepository.createUser(user);
      dispatch({
        type: CREATE_USER_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: CREATE_USER_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const editUser = (id: number, user: any) => {
  return async (dispatch) => {
    dispatch({
      type: EDIT_USER,
    });

    try {
      const response = await UserRepository.editUser(id, user);
      dispatch({
        type: EDIT_USER_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: EDIT_USER_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const getUser = (id: number) => {
  return async (dispatch) => {
    dispatch({
      type: GET_USER,
    });
    try {
      const response = await UserRepository.getUser(id);
      dispatch({
        type: GET_USER_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: GET_USER_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const clearUser = () => ({
  type: CLEAR_USER,
});

export const removeError = () => ({
  type: GET_USER_ERROR,
  payload: null,
});

export const clearSuccess = () => ({
  type: CLEAR_USER_SUCCESS,
  payload: null,
});

export const resendActivationMail = (userId) => {
  return async (dispatch) => {
    dispatch({
      type: RESEND_ACTIVATION,
    });
    try {
      await UserRepository.resendActivationMail(userId);
      dispatch({
        type: RESEND_ACTIVATION_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: RESEND_ACTIVATION_ERROR,
        payload: error.response?.data,
      });
    }

    setTimeout(() => {}, 3000);
  };
};
