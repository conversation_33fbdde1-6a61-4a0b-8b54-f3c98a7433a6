import React, {useEffect, useState} from 'react';
import {
  CouponImage,
  BrandLogo,
  Discount,
  Heading,
  Description,
  CouponButton,
  Like,
  CouponImageBadge,
} from './CouponParts';
import LimitOverlay, { getOverlayMessage } from './LimitOverlay';
import { calculateTimeLeft } from './utils/helpers';

import './CardHorizontal.scss';

function createMarkup(value) {
  return {
    __html: value.toString(),
  };
}

export const CardHorizontal = ({
  coupon,
  hasImage = true,
  className = '',
  handleLikeClick = () => {},
  isAuth = true,
  isTouchDevice = false,
  isLiked = false,
  hasLongWordsHeadline = false,
  getCouponCode = () => {},
  showToastMessage = () => {},
  PDFDownloadButton = () => {},
  code = '',
  codeURL = '',
  brandCountDownEnabled = false,
}) => {

  const [countDownText, setCountDownText] = useState('');
  const [timerInterval, setTimerInterval] = useState(1000); // Initialize to update every second
  const [remainingTime, setRemainingTime] = useState(coupon.remainingTime);

  useEffect(() => {
    if (!brandCountDownEnabled) return;
  
    // Clear any existing interval
    let intervalId;

    if(intervalId) {
      clearInterval(intervalId);
    }
  
    // Initialize the countdown with the latest remaining time
    const startCountdown = () => {
      let dynamicRemainingTime = remainingTime || 0;
  
      const updateTimer = () => {
        
        // Calculate remaining time and interval for update
        const { text, updateInterval } = calculateTimeLeft(dynamicRemainingTime);

        console.log('text', text, 'updateInterval', updateInterval);
  
        if (!text.length) {
          coupon.status = 'EXPIRED';
          clearInterval(intervalId); // Stop the timer if expired
          return;
        }
  
        // Update state with the new countdown text
        setCountDownText(text);
  
        // Adjust the dynamic remaining time for the next tick
        dynamicRemainingTime -= timerInterval / 1000;
  
        // If the update interval needs to change (e.g., from seconds to hours or vice versa)
        if (updateInterval !== timerInterval) {
          setTimerInterval(updateInterval);
          // Reset interval with new timing
          clearInterval(intervalId);
          intervalId = setInterval(updateTimer, updateInterval);
        }
      };
  
      // Start the countdown interval
      intervalId = setInterval(updateTimer, timerInterval);
    };
  
    // Start the countdown initially and restart when coupon.remainingTime changes
    startCountdown();
  
    // Cleanup function to clear the interval when the component unmounts or dependencies change
    return () => clearInterval(intervalId);
  }, [brandCountDownEnabled, remainingTime]); // Reacting to changes in these values  


  return coupon ? (
    <div className={`CardHorizontal ${className}`}>
      {hasImage && (
        <CouponImage src={coupon.image}>
          <CouponImageBadge coupon={coupon} />
          <LimitOverlay
            message={getOverlayMessage({
              status: coupon.status,
              isBrandCopiesLimitReached: coupon.isBrandCopiesLimitReached,
            })}
          />
          <Like
            isLiked={isLiked || coupon.isFavourite}
            id={coupon.id}
            handleClick={handleLikeClick}
            isAuth={isAuth}
            isTouchDevice={isTouchDevice}
          ></Like>
        </CouponImage>
      )}

      {!hasImage && (
        <div className='CardHorizontal__image' style={{ background: 'grey' }}></div>
      )}

      <div className={`CardHorizontal__details ${Boolean(countDownText.length) ? 'has-countdown' : ''}`}>

        {
          Boolean(countDownText.length) && 
          <div className="countdown">{countDownText}</div>
        }

        <div className="details">
          <Discount
            type={coupon.discountType}
            value={
              coupon.discountType === 'FREE'
                ? coupon.freeDescription
                : coupon.discountValue
            }
            hasLongWords={hasLongWordsHeadline}
          />
          <Heading>
            Mindestbestellwert:{' '}
            {coupon.amountCondition && coupon.amountCondition !== 0 ? (
              <b>{coupon.amountCondition.toLocaleString('de-DE')}</b>
            ) : (
              <b>keiner</b>
            )}
            <br />
            Gültig bis:{' '}
            <b>
              {Intl.DateTimeFormat('de-DE', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
              }).format(new Date(coupon.validationDate))}
            </b>
          </Heading>
          <Description id={coupon.id}>
            <div
              style={{
                wordWrap: 'break-word',
                wordBreak: 'break-word',
              }}
              dangerouslySetInnerHTML={createMarkup(coupon.couponRegulations)}
            ></div>
          </Description>
          <CouponButton
            coupon={{
              ...coupon,
              ...(code && { code }),
              ...(codeURL && { codeURL }),
            }}
            PDFDownloadButton={PDFDownloadButton}
            onClick={getCouponCode}
            addMessage={showToastMessage}
          />
        </div>
      </div>
    </div>
  ) : null;
};

export default CardHorizontal;