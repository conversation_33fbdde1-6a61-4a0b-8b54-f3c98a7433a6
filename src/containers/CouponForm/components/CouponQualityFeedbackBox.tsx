import { makeStyles } from '@material-ui/styles';
import React from 'react';
import { FormControlLabel, FormLabel, Grid, Radio } from '@material-ui/core';
import { RadioButtonGroup, FormTextField } from 'components/FormInputs';
import { useTranslation } from 'react-i18next';

import { InputError } from 'components';

const useStyles = makeStyles({
  couponQualityWrapper: {
    border: 'solid 1px black',
    width: '310px',
    padding: '30px 30px',
  },
  bottomMargin: {
    marginBottom: '40px',
  },
  textCenter: {
    textAlign: 'center',
  },
  centeredOption: {
    display: 'flex',
    alignItems: 'center',
  },
  optionSpace: {
    marginBottom: '7px',
    marginTop: '20px',
  },
  optionLineHeight: {
    lineHeight: 'normal',
    marginLeft: '10px',
  },
});

interface Props {
  feedbackColor: string;
  canAddFeedback: boolean;
  canViewFeedback: boolean;
  control: any;
  qualityFeedbackOptions: any;
  couponQuality?: any;
  errors: any;
}

const CouponQualityFeedbackBox = ({
  feedbackColor,
  canAddFeedback = false,
  canViewFeedback = false,
  control,
  qualityFeedbackOptions,
  couponQuality,
  errors,
}: Props) => {
  const { t }: { t: any } = useTranslation();

  const classes = useStyles();

  return (
    <Grid item xs={12}>
      <Grid container>
        <Grid item xs={3}>
          <h2>{t('couponQualityFeedback')}</h2>
        </Grid>
        <Grid item xs={8}></Grid>
        <Grid item xs={2}>
          <Grid container className={classes.couponQualityWrapper}>
            <Grid
              item
              xs={12}
              className={classes.bottomMargin + ' ' + classes.textCenter}
            >
              <span className={feedbackColor + ' big circle'}></span>
            </Grid>

            {canAddFeedback && (
              <>
                <Grid item xs={12} className={classes.bottomMargin}>
                  <RadioButtonGroup
                    name="couponQuality.title"
                    control={control}
                    rules={{ required: true }}
                  >
                    {qualityFeedbackOptions.map((feedbackOption) => {
                      if (feedbackOption.title !== 'NONE') {
                        return (
                          <Grid item xs={12}>
                            <FormControlLabel
                              value={feedbackOption.title}
                              control={<Radio className="feedback-colors" />}
                              label={
                                <span className={classes.centeredOption}>
                                  <span
                                    className={feedbackOption.color + ' circle'}
                                  ></span>
                                  <span className={classes.optionLineHeight}>
                                    {feedbackOption.label}
                                  </span>
                                </span>
                              }
                            />
                          </Grid>
                        );
                      }
                    })}
                  </RadioButtonGroup>
                </Grid>
                <Grid
                  item
                  xs={12}
                  className={classes.bottomMargin + ' ' + classes.textCenter}
                >
                  <FormLabel>{t('feedbackMax300')}</FormLabel>
                  <FormTextField
                    label=""
                    control={control}
                    name="couponQuality.text"
                    multiline={true}
                    rows={5}
                    variant="outlined"
                    disabled={couponQuality?.title == 'NONE'}
                    rules={{
                      required:
                        couponQuality?.title !== 'NONE' &&
                        t('feedBackIsRequired'),
                      maxLength: {
                        value: 300,
                        message: t('lessThan300'),
                      },
                    }}
                    helperText={
                      couponQuality?.title != 'NONE' &&
                      errors.couponQuality?.text && (
                        <InputError
                          message={errors.couponQuality?.text.message}
                        />
                      )
                    }
                    errors={errors}
                  />
                </Grid>
              </>
            )}

            {canViewFeedback && (
              <>
                <Grid
                  item
                  xs={12}
                  className={classes.bottomMargin + ' ' + classes.textCenter}
                >
                  <Grid container>
                    {qualityFeedbackOptions.map((feedbackOption) => {
                      if (feedbackOption.title !== 'NONE') {
                        return (
                          <Grid
                            item
                            xs={12}
                            className={
                              classes.centeredOption + ' ' + classes.optionSpace
                            }
                          >
                            <span
                              className={feedbackOption.color + ' circle'}
                            ></span>
                            <span className={classes.optionLineHeight}>
                              {feedbackOption.label}
                            </span>
                          </Grid>
                        );
                      }
                    })}
                  </Grid>
                </Grid>
                <Grid item xs={12} className={classes.textCenter}>
                  {couponQuality.text ? (
                    <FormLabel style={{ textTransform: 'none' }}>
                      <p>
                        <b>{t('reason')}</b>
                      </p>
                      {couponQuality.text}
                    </FormLabel>
                  ) : null}
                </Grid>
              </>
            )}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default CouponQualityFeedbackBox;
