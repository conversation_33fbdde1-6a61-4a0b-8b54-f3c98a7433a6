import React, { useState, useEffect } from 'react';
import './TextBanner.scss';
import {
  Grid,
  Button,
  FormLabel,
  TextField,
  FormControlLabel,
  Checkbox,
  Box,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { makeStyles } from '@material-ui/core/styles';
import { useTranslation } from 'react-i18next';
// import { UploadBox } from 'components/UploadBox/UploadBox';
import { RichTextInput } from 'components/FormInputs';
import { useForm, Controller } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import * as TextBannerActions from './TextBannerActions';
import * as TextBannerAPI from 'repository/textBanner';

interface TextBannerFormData {
  text: string;
  buttonText: string;
  buttonLink: string;
  hasButton: boolean;
  hasImage: boolean;
  image: string;
}

const labelStyle = { fontSize: 12, marginBottom: 2 };
const inputStyle = { borderRadius: '12px', fontSize: 11, height: 28 };

const useStyles = makeStyles((theme) => ({
  root: {
    padding: '0'
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '20px'
  },
  saveButton: {
    backgroundColor: '#000',
    color: 'white',
    borderRadius: '15px',
    textTransform: 'uppercase',
    padding: '3px 15px',
    fontSize: '12px',
    minWidth: '60px',
    height: '26px'
  },
  editorContainer: {
    '& .ql-toolbar': {
      borderTopLeftRadius: '15px',
      borderTopRightRadius: '15px',
      border: '1px solid #707070',
      borderBottom: 'none'
    },
    '& .ql-container': {
      border: '1px solid #707070',
      borderBottomLeftRadius: '15px',
      borderBottomRightRadius: '15px',
      minHeight: '150px'
    },
    '& .ql-editor': {
      fontSize: 12,
      padding: '8px 12px',
      height: '150px',
      overflow: 'auto'
    }
  },
  buttonSection: {
    marginTop: '10px'
  },
  uploadButton: {
    fontSize: 10,
    height: 28,
    backgroundColor: '#2196F3',
    color: 'white',
    borderRadius: '20px',
    textTransform: 'none',
    minWidth: '80px'
  },
  checkbox: {
    padding: '9px'
  },
  formLabel: {
    fontSize: 12,
    marginBottom: 2,
    display: 'block'
  }
}));

const TextBanner: React.FC = () => {
  const classes = useStyles();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [richTextContent, setRichTextContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [imageChange, setImageChange] = useState<{ file: File; oldPath?: string } | null>(null);
  const [imageFileName, setImageFileName] = useState<string>('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Get text banner state from Redux
  const textBannerState = useSelector((state: any) => state.textBanner);
  const fetchLoading = textBannerState?.loading || false;

  const { control, handleSubmit, watch, setValue, reset } = useForm<TextBannerFormData>({
    defaultValues: {
      text: '',
      buttonText: '',
      buttonLink: '',
      hasButton: false,
      hasImage: false,
      image: ''
    }
  });

  // Log the initial form state
  console.log('Initial form state:', {
    text: watch('text'),
    buttonText: watch('buttonText'),
    buttonLink: watch('buttonLink'),
    hasButton: watch('hasButton'),
    hasImage: watch('hasImage'),
    image: watch('image')
  });

  const hasButton = watch('hasButton');
  const hasImage = watch('hasImage');

  // Effect to handle hasImage checkbox changes
  useEffect(() => {
    if (!hasImage) {
      // Clear image-related state when hasImage is unchecked
      setImageChange(null);
      setImageFileName('');
      setValue('image', '');
    }
  }, [hasImage, setValue]);

  // Helper to extract filename from URL
  const getFileNameFromUrl = (url: string): string => {
    if (!url) return '';

    // Handle base64 URLs specially (they don't have a real filename)
    if (url.startsWith('data:')) {
      return 'Current image';
    }

    // For regular URLs, get the last part after the last slash
    return url.split('/').pop() || '';
  };

  // Fetch text banner data on component mount
  useEffect(() => {
    const fetchTextBanner = async () => {
      try {
        const response = await dispatch(TextBannerActions.getTextBanner());
        console.log('TextBanner component received response:', response);

        if (response) {
          // Make sure we have the correct data structure
          const textBannerData = response;

          console.log('Setting form values with:', {
            content: textBannerData.content,
            isButton: textBannerData.isButton,
            buttonText: textBannerData.buttonText,
            buttonLink: textBannerData.buttonLink,
            hasImage: !!textBannerData.image,
            image: textBannerData.image
          });

          // Reset the entire form with the data from the API
          reset({
            text: textBannerData.content || '',
            hasButton: textBannerData.isButton || false,
            buttonText: textBannerData.buttonText || '',
            buttonLink: textBannerData.buttonLink || '',
            hasImage: !!textBannerData.image,
            image: textBannerData.image || ''
          });

          console.log('Form reset with API data');

          // Set the image filename for display
          if (textBannerData.image) {
            console.log('Setting imageFileName:', getFileNameFromUrl(textBannerData.image));
            setImageFileName(getFileNameFromUrl(textBannerData.image));
          }

          // Log that we've completed setting values
          console.log('Completed setting form values');
        }
      } catch (error) {
        console.error('Error fetching text banner:', error);
        setSnackbar({
          open: true,
          message: 'Failed to load text banner data',
          severity: 'error'
        });
      }
    };

    fetchTextBanner();
  }, [dispatch, setValue, reset]);

  // Add effect to fix the editor focus issue
  useEffect(() => {
    // Add a small delay to ensure the component is fully rendered
    const timer = setTimeout(() => {
      const editor = document.querySelector('.ql-editor');
      if (editor) {
        editor.setAttribute('contenteditable', 'true');
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Log form values when they change
  const buttonText = watch('buttonText');
  const buttonLink = watch('buttonLink');

  useEffect(() => {
    if (hasButton) {
      console.log('Form values updated - buttonText:', buttonText, 'buttonLink:', buttonLink);
    }
  }, [hasButton, buttonText, buttonLink]);

  const handleSave = async (data: TextBannerFormData) => {
    try {
      setLoading(true);

      console.log('Saving form data:', data);

      // Prepare the payload according to the API requirements
      const payload: TextBannerAPI.TextBanner = {
        id: textBannerState?.textBanner?.id, // Include the textBanner ID
        content: data.text,
        isButton: data.hasButton,
        buttonText: data.hasButton ? data.buttonText : undefined,
        buttonLink: data.hasButton ? data.buttonLink : undefined,
        image: '' // Default to empty string
      };

      // If hasImage is true, use the appropriate image URL
      if (data.hasImage) {
        console.log('hasImage is true, checking for image source...');
        console.log('imageChange:', imageChange);
        console.log('textBannerState:', textBannerState);
        console.log('data.image:', data.image);

        if (imageChange) {
          // New image will be uploaded by the action
          console.log('Using new uploaded image');
          payload.image = data.image;
        } else if (textBannerState?.textBanner?.image) {
          // Use existing image from state
          console.log('Using existing image from state:', textBannerState.textBanner.image);
          payload.image = textBannerState.textBanner.image;
        } else if (data.image) {
          // Fallback to form data
          console.log('Using image from form data:', data.image);
          payload.image = data.image;
        } else {
          console.log('No image source found despite hasImage being true');
        }
      } else {
        console.log('hasImage is false, sending empty image URL');
        payload.image = '';
      }

      console.log('Sending payload to API:', payload);

      // Call the Redux action to update the text banner
      const response = await dispatch(TextBannerActions.updateTextBanner(payload, imageChange || undefined));

      console.log('Update response:', response);

      // Reset image change after successful save
      setImageChange(null);

      setSnackbar({
        open: true,
        message: 'Text banner saved successfully!',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error saving text banner:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save text banner',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Log form values on each render
  console.log('Rendering TextBanner with form values:', {
    buttonText: watch('buttonText'),
    buttonLink: watch('buttonLink'),
    hasButton
  });

  return (
    <div className="text-banner-section">
      <div className="section-header" style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px'
      }}>
        <h2 className="section-title" style={{ margin: 0 }}>{t('textBanner')}</h2>
        <Button
          variant="contained"
          onClick={handleSubmit(handleSave)}
          className="save-button"
          disabled={loading || fetchLoading}
        >
          {loading ? t('saving') : t('save').toUpperCase()}
        </Button>
      </div>

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          {/* Removed the Text label */}
          <Box className={classes.editorContainer}>
            <RichTextInput
              name="text"
              control={control}
              validate={() => true}
              onChangeHandler={(onChange: any) => (content: any, _delta: any, _source: any, editor: any) => {
                onChange(content);
                setRichTextContent(editor.getText());
              }}
              theme="snow"
              style={{}}
            />
          </Box>
        </Grid>

        <Grid item xs={12} md={3}>
          <Controller
            name="hasButton"
            control={control}
            render={({ onChange, value }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={value}
                    onChange={(e) => onChange(e.target.checked)}
                    className={classes.checkbox}
                  />
                }
                label="BUTTON"
              />
            )}
          />
          {hasButton && (
            <Box className={classes.buttonSection}>
              <Box mb={2}>
                <FormLabel style={labelStyle}>Text</FormLabel>
                <Controller
                  name="buttonText"
                  control={control}
                  render={({ onChange, value }) => (
                    <TextField
                      value={value}
                      onChange={(e) => onChange(e.target.value)}
                      fullWidth
                      variant="outlined"
                      size="small"
                      InputProps={{ style: inputStyle }}
                    />
                  )}
                />
              </Box>
              <Box>
                <FormLabel style={labelStyle}>Link</FormLabel>
                <Controller
                  name="buttonLink"
                  control={control}
                  render={({ onChange, value }) => (
                    <TextField
                      value={value}
                      onChange={(e) => onChange(e.target.value)}
                      fullWidth
                      variant="outlined"
                      size="small"
                      InputProps={{ style: inputStyle }}
                    />
                  )}
                />
              </Box>
            </Box>
          )}
        </Grid>

        <Grid item xs={12} md={3}>
          <Controller
            name="hasImage"
            control={control}
            render={({ onChange, value }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={value}
                    onChange={(e) => onChange(e.target.checked)}
                    className={classes.checkbox}
                  />
                }
                label="Image"
              />
            )}
          />
          {hasImage && (
            <Box mt={1}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <Button
                  variant="contained"
                  component="label"
                  className={classes.uploadButton}
                >
                  UPLOAD
                  <input
                    type="file"
                    hidden
                    accept=".jpg,.jpeg,.png"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        const file = e.target.files[0];
                        const oldPath = watch('image');

                        // Store the file and old path for upload on save
                        setImageChange({ file, oldPath });

                        // Update the filename display
                        setImageFileName(file.name);

                        // Enable the image checkbox
                        setValue('hasImage', true);
                      }
                    }}
                  />
                </Button>

                {imageFileName && (
                  <div style={{ display: 'flex', alignItems: 'center', fontSize: 11 }}>
                    <span style={{ color: '#000', textDecoration: 'underline' }}>
                      {imageFileName.length > 20 ? imageFileName.substring(0, 17) + '...' : imageFileName}
                    </span>
                  </div>
                )}
              </div>
            </Box>
          )}
        </Grid>
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default TextBanner;
