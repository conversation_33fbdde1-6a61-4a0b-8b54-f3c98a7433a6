import React, { useState, useEffect, useCallback } from 'react';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { useTranslation } from 'react-i18next';
import CouponRow from './CouponRow';
import { useDispatch, useSelector } from 'react-redux';

import * as CouponOfTheWeekActions from './CouponOfTheWeekActions';


interface CouponItem {
  id?: string | number;
  brand: string;
  coupon: string;
  startDate: Date | string | null;
  endDate: Date | string | null;
}

interface Brand {
  id: string;
  name: string;
}

interface CouponOption {
  id: string | number;
  code: string;
  brandId: string | number;
  categoryName?: string;
  amountCondition?: string;
}

interface CouponResponse {
  id: number;
  brandName: string;
  categoryName: string;
  validationDate: string;
  amountCondition: string;
  status: string;
  isCountDownEnabled: boolean;
}

interface CouponOfTheWeekProps {
  brands: Brand[];
  allCoupons: CouponOption[];
}

const CouponOfTheWeek: React.FC<CouponOfTheWeekProps> = ({ brands, allCoupons }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // Use Redux state with useSelector
  const couponOfTheWeekState = useSelector((state: any) => state.couponOfTheWeek);
  const couponsState = useSelector((state: any) => state.coupons);

  // Local state
  const [mappedCoupons, setMappedCoupons] = useState<CouponItem[]>([
    { id: undefined, brand: '', coupon: '', startDate: null, endDate: null }
  ]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; id: string | number | null }>({ open: false, id: null });

  // Function to fetch coupon of the week data
  const fetchCouponOfTheWeeks = useCallback(() => {
    // Dispatch the action to fetch coupon of the week data
    dispatch(CouponOfTheWeekActions.getCouponOfTheWeeks({
      page: 0,
      size: 100,
      filters: { status: 'ACTIVE' }
    }));
  }, [dispatch]);

  // Effect to fetch data on component mount
  useEffect(() => {
    fetchCouponOfTheWeeks();
  }, [fetchCouponOfTheWeeks]);

  // Effect to map couponOfTheWeek data from Redux to component state
  useEffect(() => {
    // Only update the state if we're not in the middle of a save operation
    if (!loading) {
      if (couponOfTheWeekState?.couponOfTheWeeks?.length > 0) {
        // Map the API response to the component's state format
        const mapped = couponOfTheWeekState.couponOfTheWeeks.map((item: any) => ({
          id: item.id,
          brand: item.brandId ? item.brandId.toString() : '', // Map brandId as string
          coupon: item.couponId ? item.couponId.toString() : '',
          startDate: item.startDate ? new Date(item.startDate) : null,
          endDate: item.endDate ? new Date(item.endDate) : null
        }));

        setMappedCoupons(mapped);
      } else if (!couponOfTheWeekState.loading && couponOfTheWeekState.couponOfTheWeeks?.length === 0) {
        // If no data and not loading, set a default empty coupon
        setMappedCoupons([{ id: undefined, brand: '', coupon: '', startDate: null, endDate: null }]);
      }
    }
  }, [couponOfTheWeekState, loading]);

  // Effect to get all coupons from Redux state
  useEffect(() => {
    if (couponsState?.data) {
      console.log('Setting all coupons from Redux state:', couponsState.data);
    }
  }, [couponsState]);

  // Note: Removed automatic error handling for page loads to prevent false error messages
  // Error handling is now only done in specific user actions (save, delete) where appropriate

  const handleAddCoupon = () => {
    if (mappedCoupons.length === 0) {
      setMappedCoupons([
        { id: undefined, brand: '', coupon: '', startDate: null, endDate: null }
      ]);
      return;
    }

    const lastCoupon = mappedCoupons[mappedCoupons.length - 1];
    const allEmpty = !lastCoupon.brand && !lastCoupon.coupon && !lastCoupon.startDate && !lastCoupon.endDate;

    if (allEmpty) {
      return;
    }

    setMappedCoupons([
      ...mappedCoupons,
      { id: undefined, brand: '', coupon: '', startDate: null, endDate: null }
    ]);
  };

  const handleDeleteCoupon = (id: string | number | undefined, idx?: number) => {
    if (!id) {
      // If no id, remove from frontend immediately
      const newCoupons = mappedCoupons.filter((_, i) => i !== idx);

      // Ensure there's always at least one coupon row
      if (newCoupons.length === 0) {
        setMappedCoupons([{ id: undefined, brand: '', coupon: '', startDate: null, endDate: null }]);
      } else {
        setMappedCoupons(newCoupons);
      }
      return;
    }
    setDeleteDialog({ open: true, id: id ?? null });
  };

  const confirmDelete = async () => {
    try {
      setLoading(true);

      if (deleteDialog.id) {
        await dispatch(CouponOfTheWeekActions.deleteCouponOfTheWeek(Number(deleteDialog.id)));

        // Refresh data from server after deletion
        await dispatch(CouponOfTheWeekActions.getCouponOfTheWeeks({
          page: 0,
          size: 100,
          filters: { status: 'ACTIVE' }
        }));

        // Check if we need to add an empty row after deletion
        // This will be handled by the useEffect that maps couponOfTheWeekState to mappedCoupons
      }

      // Close the dialog
      setDeleteDialog({ open: false, id: null });

      // Show success message
      setSnackbar({
        open: true,
        message: 'Coupon deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to delete coupon',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCouponChange = (index: number, data: Partial<CouponItem>) => {
    setMappedCoupons(coupons => coupons.map((c, i) => i === index ? { ...c, ...data } : c));
  };

  const validateCoupons = () => {
    const missingFields = mappedCoupons.some(c => {
      return !c.coupon?.toString().trim() || !c.startDate;
    });

    if (missingFields) {
      setSnackbar({
        open: true,
        message: 'Please fill all required fields: Coupon and Start Date.',
        severity: 'error'
      });
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateCoupons()) {
      return;
    }

    try {
      setLoading(true);

      // Process all coupons
      await Promise.all(mappedCoupons.map(async (c) => {
        // Format dates as ISO strings with time component as required by the backend
        const formatDate = (date: string | Date | null): string => {
          if (!date) return '';
          if (typeof date === 'string') {
            // If it's already a string, ensure it has the time component
            if (date.includes('T')) return date;
            // Otherwise, add the time component
            return `${date}T00:00:00.000Z`;
          }
          // If it's a Date object, convert to ISO string
          return date.toISOString();
        };

        // For create operation
        if (!c.id) {
          const createPayload = {
            brandId: Number(c.brand || 0),
            couponId: Number(c.coupon),
            startDate: formatDate(c.startDate),
            endDate: formatDate(c.endDate),
            status: 'ACTIVE'
          };

          await dispatch(CouponOfTheWeekActions.createCouponOfTheWeek(createPayload));
        }
        // For update operation
        else {
          const updatePayload = {
            id: Number(c.id),
            brandId: Number(c.brand || 0),
            couponId: Number(c.coupon),
            startDate: formatDate(c.startDate),
            endDate: formatDate(c.endDate),
            status: 'ACTIVE'
          };

          await dispatch(CouponOfTheWeekActions.updateCouponOfTheWeek(updatePayload));
        }
      }));

      // Refresh data from server after all operations are complete
      await dispatch(CouponOfTheWeekActions.getCouponOfTheWeeks({
        page: 0,
        size: 100,
        filters: { status: 'ACTIVE' }
      }));

      setSnackbar({
        open: true,
        message: 'Coupons saved successfully',
        severity: 'success'
      });

    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to save coupons',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="coupon-of-the-week-section">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 className="section-title" style={{ margin: 0 }}>{t('couponOfTheWeek')}</h2>
        <Button
          variant="contained"
          className="save-button"
          onClick={handleSave}
          disabled={couponOfTheWeekState.loading || loading}
        >
          {(couponOfTheWeekState.loading || loading) ? 'Saving...' : 'SAVE'}
        </Button>
      </div>

      {(couponOfTheWeekState.loading || loading) && (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          Loading...
        </div>
      )}

      <div>
        {mappedCoupons.map((coupon, idx) => (
          <CouponRow
            key={coupon.id || idx}
            coupon={coupon}
            brands={brands}
            allCoupons={couponsState?.data || []}
            onDelete={() => handleDeleteCoupon(coupon.id, idx)}
            onChange={data => handleCouponChange(idx, data)}
          />
        ))}
        <Button
          type="button"
          variant="contained"
          className="add-button"
          style={{ textTransform: 'capitalize', height: 28 }}
          onClick={handleAddCoupon}
          disabled={couponOfTheWeekState.loading || loading}
        >
          + {t('addCoupon')}
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, id: null })}>
        <DialogTitle>{t('Delete Coupon?')}</DialogTitle>
        <DialogContent>{t('Are you sure you want to delete this coupon?')}</DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, id: null })}>{t('Cancel')}</Button>
          <Button color="secondary" onClick={confirmDelete}>{t('Delete')}</Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          style={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default CouponOfTheWeek;