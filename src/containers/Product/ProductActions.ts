import * as actionTypes from './ProductTypes';
import * as ProductService from 'repository/product';

export const changeFilter = (filter) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.CHANGE_FILTER,
      payload: {
        page: filter.page,
        pageSize: filter.pageSize,
        sort: { ...filter.sort },
      },
    });
  };
};

export const getProducts = (filter = {}) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.GET_PRODUCTS,
    });

    try {
      const response: any = await ProductService.getProducts(filter);
      dispatch({
        type: actionTypes.GET_PRODUCTS_SUCCESS,
        payload: response?.data,
      });
    } catch (error) {
      dispatch({
        type: actionTypes.GET_PRODUCTS_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const deleteProduct = (product) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.DELETE_PRODUCT,
    });
    try {
      const response = await ProductService.deleteProduct(product);
      dispatch({
        type: actionTypes.DELETE_PRODUCT_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: actionTypes.DELETE_PRODUCT_ERROR,
        payload: error.response?.data,
      });
    }
  };
};

export const markProductFeatured = (id) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.MARK_PRODUCT_FEATURED,
      payload: id,
    });
  };
};

export const setProductFeatured = (data) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.SET_PRODUCT_FEATURED_START,
    });
    try {
      const response = await ProductService.setProductFeatured(data);
      if (response?.data?.position) {
        dispatch({
          type: actionTypes.SET_PRODUCT_FEATURED_SUCCESS,
        });
      } else {
        dispatch({
          type: actionTypes.SET_PRODUCT_NOT_FEATURED_SUCCESS,
        });
      }
    } catch (error) {
      dispatch({
        type: actionTypes.SET_PRODUCT_FEATURED_ERROR,
        payload: error?.response?.data,
      });
    }
  };
};

export const clearProduct = () => ({
  type: actionTypes.CLEAR_PRODUCT,
});

export const getEligibleProducts = (consumerId) => {
  return async (dispatch) => {
    dispatch({
      type: actionTypes.GET_ELIGIBLE_PRODUCTS,
    });
    try {
      const response: any = await ProductService.getEligibleProducts(
        consumerId
      );

      dispatch({
        type: actionTypes.GET_ELIGIBLE_PRODUCTS_SUCCESS,
        payload: response?.data,
      });
    } catch (error) {
      dispatch({
        type: actionTypes.GET_ELIGIBLE_PRODUCTS_ERROR,
        payload: error.response?.data,
      });
    }
  };
};
