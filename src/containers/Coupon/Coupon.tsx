import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

import { CouponView } from './CouponView';
import {
  getCoupons,
  deleteCoupon,
  changeFilter,
  getCouponsAsBrandAdmin,
} from './CouponActions';
import { CouponTable } from './components/CouponTable';
import { Loading } from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import * as RoleManager from 'services/role';
import { getBrandPackageForBrandAdmin } from '../BrandAccount/BrandAccountActions';
import { CLEAR_COUPON_ERROR_MESSAGES } from 'containers/CouponForm/CouponFormTypes';
import { CLEAR_COUPON } from 'containers/Coupon/CouponTypes';

export default () => {
  const { t }: { t: any } = useTranslation();
  const dispatch = useDispatch();
  const coupons = useSelector((state: any) => state.coupons);
  const couponsForm = useSelector((state: any) => state.couponsForm);
  const brandCoupons = useSelector((state: any) => state.brandCoupons);
  const couponDeleteSuccess = useSelector(
    (state: any) => state.coupons.deleteSuccess
  );
  const couponDeleteError = useSelector(
    (state: any) => state.coupons.deleteError
  );
  const couponError = useSelector((state: any) => state.coupons.error);
  const brandAccount = useSelector((state: any) => state.brandAccount);
  const { brandPackage, error: brandAccountError } = brandAccount;

  const { addMessage } = useAPIError();

  const [filter, setFilter] = useState({
    pageSize: coupons?.filter?.pageSize || 10,
    page: coupons?.filter?.page || 0,
    sort: coupons?.filter?.sort || {
      field: 'id',
      dir: 'DESC',
    },
    search: '',
  });

  useEffect(() => {
    if (RoleManager.isAbleTo('brand_account', 'view')) {
      dispatch(getBrandPackageForBrandAdmin());
    }
  }, []);

  const fetchCoupons = (filter) => {
    if (RoleManager.isAbleTo('brand_coupon', 'view')) {
      dispatch(getCouponsAsBrandAdmin(filter));
    } else {
      dispatch(getCoupons(filter));
    }
  };

  const onFilterChange = (data) => {
    const filterUpdate = { ...filter, ...data };
    setFilter(filterUpdate);
    fetchCoupons(filterUpdate);
    dispatch(changeFilter(filterUpdate));
  };

  useEffect(() => {
    if (couponDeleteSuccess) {
      addMessage(t('successCouponDeleted'), 'warning');
      fetchCoupons(filter);
    }
    if (couponDeleteError) {
      addMessage(t(couponDeleteError.errorCode), 'error');
    }
    if (couponsForm.editSuccess) {
      addMessage(t('successCouponUpdate'), 'success');
    }
    if (couponsForm.createSuccess) {
      addMessage(t('successCouponCreated'), 'success');
    }
    if (couponsForm.error) {
      addMessage(t(couponsForm.error.errorCode), 'error');
    }
    if (couponError) {
      addMessage(t(couponError.errorCode), 'error');
    }
    if (brandAccountError) {
      addMessage(t(brandAccountError.errorCode || 'errorOccured'), 'error');
    }

    dispatch({
      type: CLEAR_COUPON,
    });
    dispatch({
      type: CLEAR_COUPON_ERROR_MESSAGES,
    });
    fetchCoupons(filter);
  }, [dispatch, couponDeleteSuccess, couponDeleteError, couponError]);

  const onDeleteCoupon = (coupon) => {
    dispatch(deleteCoupon(coupon));
  };

  return (
    <CouponView
      onFilterChange={onFilterChange}
      filter={filter}
      brandPackage={brandPackage}
    >
      {(brandCoupons.data || coupons.data) && (
        <CouponTable
          coupons={
            RoleManager.isAbleTo('brand_coupon', 'view')
              ? brandCoupons.data
              : coupons.data
          }
          pagination={coupons.pagination}
          onFilterChange={onFilterChange}
          filter={filter}
          onDeleteCoupon={onDeleteCoupon}
        >
          {coupons.loading && <Loading />}
        </CouponTable>
      )}
    </CouponView>
  );
};
